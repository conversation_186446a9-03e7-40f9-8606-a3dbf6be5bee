name: Docker Build, Push and Run (Azure)

on:
  push:
    branches:
      - main

jobs:
  build-and-run:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v2

      # Step 2: Set up SSH key for Azure VM
      - name: Set up SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.AZURE_VM_HOST }} >> ~/.ssh/known_hosts

      # Step 4: SSH into Azure VM and deploy Docker containers
      - name: Deploy to Azure VM
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.AZURE_VM_HOST }}
          username: ${{ secrets.AZURE_VM_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.AZURE_VM_SSH_PORT || 22 }}
          script: |
            cd ShangrillaDistillery
            git pull origin main

            # Stop existing containers gracefully
            docker-compose down

            # Clean up only unused images and build cache (not containers)
            docker image prune -f
            docker builder prune -f

            # Build and start containers
            docker-compose up -d --build

            # Wait for containers to start
            sleep 10

            # Verify both containers are running
            echo "=== Container Status ==="
            docker ps

            # Check if frontend is running, if not try to start it
            if ! docker ps | grep -q "frontend"; then
              echo "Frontend not running, attempting to start..."
              docker-compose up -d frontend
              sleep 5
            fi

            # Final status check
            echo "=== Final Container Status ==="
            docker ps

            # Test if services are responding
            echo "=== Testing Services ==="
            curl -I http://localhost:3001 || echo "Frontend not responding"
            curl -I http://localhost:8000 || echo "Backend not responding"