import Navigation from "../components/Navigation";
import { Calendar, Clock, MapPin, Users, Facebook, Twitter, Instagram, Linkedin, Phone, Mail } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import Footer from "../components/home/<USER>";

const Events = () => {
  const upcomingEvents = [
    {
      title: "Whiskey Tasting Experience",
      date: "March 15, 2024",
      time: "6:00 PM - 9:00 PM",
      location: "Shangrila Distillery, Hetauda",
      description: "Join our master distiller for an exclusive tasting of our premium whiskey collection.",
      attendees: "Limited to 20 guests"
    },
    {
      title: "Distillery Tour & Masterclass",
      date: "March 22, 2024",
      time: "2:00 PM - 5:00 PM",
      location: "Shangrila Distillery, Hetauda",
      description: "Behind-the-scenes tour followed by a masterclass on the art of distillation.",
      attendees: "Open to all"
    },
    {
      title: "Tejas Gold Launch Event",
      date: "April 5, 2024",
      time: "7:00 PM - 10:00 PM",
      location: "Kathmandu Hotel Yak & Yeti",
      description: "Celebrate the launch of our newest premium whiskey with industry leaders.",
      attendees: "Invitation only"
    }
  ];

  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">Heritage Events & Experiences</h1>
            <p className="text-xl distillery-text max-w-3xl mx-auto">
              Join us for special events, spirit release parties, masterclasses, and private functions. Create memorable experiences at our distillery.
            </p>
          </div>



          {/* Upcoming Events */}
          {/* <div className="mb-16">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Upcoming Events</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {upcomingEvents.map((event, index) => (
                <div key={index} className="distillery-card p-6 hover:scale-105 transition-all duration-300">
                  <h3 className="text-xl font-playfair font-bold text-amber-100 mb-4">{event.title}</h3>
                  
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-5 w-5 text-amber-400" />
                      <span className="distillery-text">{event.date}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Clock className="h-5 w-5 text-amber-400" />
                      <span className="distillery-text">{event.time}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-5 w-5 text-amber-400" />
                      <span className="distillery-text">{event.location}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Users className="h-5 w-5 text-amber-400" />
                      <span className="distillery-text">{event.attendees}</span>
                    </div>
                  </div>
                  
                  <p className="distillery-text mb-6">{event.description}</p>
                  
                  <Button className="w-full premium-button font-crimson font-semibold">
                    Register Now
                  </Button>
                </div>
              ))}
            </div>
          </div> */}

          {/* Event Types */}
          {/* <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Calendar className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Tasting Events</h3>
              <p className="distillery-text">Guided tastings of our premium spirit collection</p>
            </div>
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Users className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Private Functions</h3>
              <p className="distillery-text">Exclusive venue hire for corporate and private events</p>
            </div>
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Clock className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Masterclasses</h3>
              <p className="distillery-text">Learn the art of distillation from our experts</p>
            </div>
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <MapPin className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Distillery Tours</h3>
              <p className="distillery-text">Behind-the-scenes tours of our facilities</p>
            </div>
          </div> */}

          {/* Call to Action */}
          <div className="copper-gradient rounded-3xl p-8 md:p-16 text-center text-stone-900">
            <h3 className="text-3xl md:text-4xl font-playfair font-bold mb-6">Ready to Join Us?</h3>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Contact us to book your spot at our upcoming events or to arrange a private function at our distillery.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact">
                <Button className="bg-stone-900 hover:bg-stone-800 text-white px-8 py-4 text-lg font-crimson font-semibold">
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
};

export default Events;
