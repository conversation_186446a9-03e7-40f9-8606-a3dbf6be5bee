import Navigation from "../components/Navigation";
import { MapPin, Users, Store, Building, Facebook, Twitter, Instagram, Linkedin, Phone, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Link } from "react-router-dom";
import Footer from "../components/home/<USER>";
import { useState } from "react";
import axios from "axios";
import { toast } from "sonner";

type FormData = {
  fullName: string;
  businessName: string;
  email: string;
  phone: string;
  businessLocation: string;
  message: string;
};

const Sales = () => {
  const segments = [
    { price: "25 UP", description: "Extra Premium segment" },
    { price: "30 UP", description: "Premium Vodka Segment" },
    { price: "40 UP", description: "Premium Segment" },
    { price: "70 UP", description: "Entry Segment" }
  ];

  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    businessName: "",
    email: "",
    phone: "",
    businessLocation: "",
    message: "",
  });
  
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when user types
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    
    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.businessName.trim()) newErrors.businessName = 'Business name is required';
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^[0-9\s\-+()]*$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }
    if (!formData.businessLocation.trim()) newErrors.businessLocation = 'Business location is required';
    if (!formData.message.trim()) newErrors.message = 'Please tell us about your business';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL || 'https://mail.shangriladistillery.com'}/api/local`,
        formData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
      if (response.status === 200) {
        toast.success("Your application has been submitted successfully!");
        // Reset form
        setFormData({
          fullName: "",
          businessName: "",
          email: "",
          phone: "",
          businessLocation: "",
          message: "",
        });
      } else {
        throw new Error('Failed to submit application');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      toast.error(
        error.response?.data?.message || 
        'Failed to submit application. Please try again later.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="flex justify-center mb-6">
              <img
                src="/lovable-uploads/favicon-shangrila.png"
                alt="Shangrila Distillery"
                className="h-20 w-20 rounded-full object-cover filter brightness-110 shadow-lg"
              />
            </div>
            <h1 className="text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">Local Heritage</h1>
            <p className="text-xl distillery-text max-w-3xl mx-auto">
              We're expanding across Nepal, bringing premium spirits to every corner of the nation.
            </p>
          </div>

          {/* Map Section */}
          <div className="distillery-card p-8 mb-16 text-center">
            <MapPin className="h-16 w-16 text-amber-400 mx-auto mb-6 animate-float" />
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-4">Expanding Across Nepal</h2>
            <p className="text-lg distillery-text max-w-2xl mx-auto">
              From Kathmandu to the furthest reaches of our beautiful nation, 
              we're building a comprehensive distribution network to serve every region.
            </p>
          </div>

          {/* Target Segments */}
          <div className="mb-16">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Our Target Segments</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {segments.map((segment) => (
                <div key={segment.price} className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
                  <div className="text-3xl font-playfair font-bold text-amber-400 mb-2">{segment.price}</div>
                  <p className="distillery-text">{segment.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Distribution Channels */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Store className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Bottle Shops</h3>
              <p className="distillery-text">Premium retail outlets across major cities and towns</p>
            </div>
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Building className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">HORECA</h3>
              <p className="distillery-text">Hotels, restaurants, and cafes for premium dining experiences</p>
            </div>
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Users className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Distribution</h3>
              <p className="distillery-text">Authorized distributors for nationwide coverage</p>
            </div>
          </div>

          {/* Branded Sales Section */}
          <div className="mb-16 text-center">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-6">Branded Products for Sale</h2>
            <p className="text-xl text-amber-100/80 max-w-2xl mx-auto font-crimson mb-6">
              Shangrila Distillery proudly offers a range of premium branded spirits for sale across Nepal. Our products are available through authorized distributors, bottle shops, and HORECA partners nationwide. Experience the quality and heritage of our spirits in every sip.
            </p>
            <div className="mt-8">
              <span className="text-amber-200 font-crimson text-lg">For sales inquiries, contact us at </span>
              <a href="mailto:<EMAIL>" className="text-amber-400 underline font-bold font-baskerville hover:text-amber-300 transition-colors"><EMAIL></a>
            </div>
          </div>

          {/* Storage Capacity */}
          <div className="distillery-card p-8 mb-16 text-center">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-4">2,00,000+ litres storage capacity</h2>
            <p className="text-lg distillery-text max-w-2xl mx-auto">
              and planning on more
            </p>
          </div>

          {/* Why Partner With Us */}
          <div className="mb-16">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Why Partner With Us?</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="distillery-card p-6 text-center">
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Quality Products</h3>
                <p className="distillery-text">A portfolio of award-winning, premium quality spirits.</p>
              </div>
              <div className="distillery-card p-6 text-center">
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Marketing Support</h3>
                <p className="distillery-text">Comprehensive marketing and promotional support.</p>
              </div>
              <div className="distillery-card p-6 text-center">
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Competitive Pricing</h3>
                <p className="distillery-text">Attractive pricing and margin for our partners.</p>
              </div>
            </div>
          </div>

          {/* Testimonials */}
          <div className="mb-16">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">What Our Partners Say</h2>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="distillery-card p-6">
                <p className="distillery-text italic">"Working with Shangrila has been a game-changer for our business. Their products are top-notch and their team is incredibly supportive."</p>
                <p className="text-amber-200 font-semibold mt-4">- Partner A</p>
              </div>
              <div className="distillery-card p-6">
                <p className="distillery-text italic">"The quality of Shangrila's spirits is unmatched. Our customers love them, and we've seen a significant increase in sales since we started carrying their products."</p>
                <p className="text-amber-200 font-semibold mt-4">- Partner B</p>
              </div>
            </div>
          </div>

          {/* Dealer Application Form */}
          <div className="distillery-card p-8">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Join Our Network</h2>
            <div className="max-w-2xl mx-auto">
              <form className="space-y-6" onSubmit={handleSubmit}>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="fullName" className="text-amber-200">Full Name</Label>
                    <Input 
                      id="fullName" 
                      name="fullName"
                      type="text" 
                      value={formData.fullName}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.fullName ? 'border-red-500' : ''}`} 
                      onChange={handleChange}
                    />
                    {errors.fullName && <p className="mt-1 text-sm text-red-400">{errors.fullName}</p>}
                  </div>
                  <div>
                    <Label htmlFor="businessName" className="text-amber-200">Business Name</Label>
                    <Input 
                      id="businessName" 
                      name="businessName"
                      type="text" 
                      value={formData.businessName}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.businessName ? 'border-red-500' : ''}`} 
                      onChange={handleChange}
                    />
                    {errors.businessName && <p className="mt-1 text-sm text-red-400">{errors.businessName}</p>}
                  </div>
                </div>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email" className="text-amber-200">Email</Label>
                    <Input 
                      id="email" 
                      name="email"
                      type="email" 
                      value={formData.email}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.email ? 'border-red-500' : ''}`} 
                      onChange={handleChange}
                    />
                    {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-amber-200">Phone</Label>
                    <Input 
                      id="phone" 
                      name="phone"
                      type="tel" 
                      value={formData.phone}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.phone ? 'border-red-500' : ''}`} 
                      onChange={handleChange}
                    />
                    {errors.phone && <p className="mt-1 text-sm text-red-400">{errors.phone}</p>}
                  </div>
                </div>
                <div>
                  <Label htmlFor="businessLocation" className="text-amber-200">Business Location</Label>
                  <Input 
                    id="businessLocation" 
                    name="businessLocation"
                    type="text" 
                    value={formData.businessLocation}
                    className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.businessLocation ? 'border-red-500' : ''}`} 
                    onChange={handleChange}
                  />
                  {errors.businessLocation && <p className="mt-1 text-sm text-red-400">{errors.businessLocation}</p>}
                </div>
                <div>
                  <Label htmlFor="message" className="text-amber-200">Tell us about your business</Label>
                  <Textarea 
                    id="message" 
                    name="message"
                    rows={4} 
                    value={formData.message}
                    className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.message ? 'border-red-500' : ''}`} 
                    onChange={handleChange}
                  />
                  {errors.message && <p className="mt-1 text-sm text-red-400">{errors.message}</p>}
                </div>
                <Button 
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full premium-button py-3 font-crimson font-semibold text-lg ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </>
                  ) : 'Apply to Become a Dealer'}
                </Button>
              </form>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
};

export default Sales;
