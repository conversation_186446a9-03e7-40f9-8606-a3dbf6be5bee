import Navigation from "../../components/Navigation";
import Footer from "../../components/home/<USER>";
import { blogs } from "../../data/blogs";
import { useParams } from "react-router-dom";
import { Helmet } from "react-helmet";

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  const blog = blogs.find((b) => b.slug === slug);

  if (!blog) {
    return <div>Blog post not found</div>;
  }

  return (
    <div className="min-h-screen distillery-gradient">
      <Helmet>
        <title>{blog.metaTitle}</title>
        <meta name="description" content={blog.metaDescription} />
      </Helmet>
      <Navigation />
      <div className="pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6">{blog.title}</h1>
          </div>

          {blog.image && <img src={blog.image} alt={blog.title} className="w-full md:w-1/2 mx-auto h-auto object-cover rounded-lg mb-12" />}

          <div className="prose prose-lg max-w-none mx-auto distillery-text">
            <p className="lead">{blog.introduction}</p>
            {blog.sections.map((section, index) => (
              <div key={index} className="mt-8">
                <h2 className="text-2xl font-playfair font-bold text-amber-100">{section.title}</h2>
                <p>{section.content}</p>
              </div>
            ))}
            <p className="mt-8">{blog.conclusion}</p>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
};

export default BlogPost;
