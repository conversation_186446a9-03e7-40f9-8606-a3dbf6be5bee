import Navigation from "../components/Navigation";
import { useState } from "react";
import ProductHero from "../components/products/ProductHero";
import CategoryFilter from "../components/products/CategoryFilter";
import ProductCard from "../components/products/ProductCard";
import CraftingProcess from "../components/products/CraftingProcess";
import AwardsSection from "../components/products/AwardsSection";
import ComingSoonSection from "../components/products/ComingSoonSection";
import Footer from "../components/home/<USER>";
import { products, categories } from "../data/products";

const Products = () => {
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredProducts = selectedCategory === "All" 
    ? products 
    : products.filter(product => product.category === selectedCategory);

  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <ProductHero />
          
          <CategoryFilter 
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />

          {/* Product Banners */}
          <div className="space-y-12 mb-16">
            {filteredProducts.map((product, index) => (
              <ProductCard key={product.name} product={product} index={index} />
            ))}
          </div>

          <CraftingProcess />
          <AwardsSection />
          <ComingSoonSection />
        </div>

        <Footer />
      </div>
    </div>
  );
};

export default Products;
