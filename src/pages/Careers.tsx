import Navigation from "../components/Navigation";
import Footer from "../components/home/<USER>";
import { Briefcase, Settings, ShieldCheck, Megaphone, Truck } from "lucide-react";

const jobOpenings = [
  {
    title: "Sales Executive",
    icon: Briefcase,
    summary: "Drive sales growth, build relationships with distributors and retailers, and expand our market presence across Nepal.",
    responsibilities: [
      "Identify and pursue new sales opportunities",
      "Build and maintain strong client relationships",
      "Achieve monthly and annual sales targets",
      "Represent the brand at events and trade shows"
    ],
    requirements: [
      "Bachelor's degree in Business or related field",
      "1+ years of sales experience (FMCG or spirits preferred)",
      "Excellent communication and negotiation skills"
    ],
    benefits: [
      "Competitive salary and incentives",
      "Travel allowance",
      "Professional growth opportunities"
    ]
  },
  {
    title: "Production Operator",
    icon: Settings,
    summary: "Operate and maintain distillery equipment, ensuring quality and efficiency in every batch.",
    responsibilities: [
      "Operate blending, bottling, and packaging machinery",
      "Monitor production processes for quality and safety",
      "Perform routine equipment maintenance",
      "Follow SOPs and safety protocols"
    ],
    requirements: [
      "High school diploma or equivalent",
      "Experience in manufacturing or food/beverage industry preferred",
      "Attention to detail and reliability"
    ],
    benefits: [
      "Stable employment",
      "On-the-job training",
      "Supportive team environment"
    ]
  },
  {
    title: "Quality Assurance Officer",
    icon: ShieldCheck,
    summary: "Monitor production standards, conduct quality checks, and help maintain our reputation for excellence.",
    responsibilities: [
      "Conduct inspections and quality tests at various production stages",
      "Document and report quality issues",
      "Ensure compliance with regulatory standards",
      "Collaborate with production and R&D teams"
    ],
    requirements: [
      "Bachelor's degree in Science, Food Technology, or related field",
      "Experience in quality control/assurance preferred",
      "Strong analytical and documentation skills"
    ],
    benefits: [
      "Competitive compensation",
      "Learning and development opportunities",
      "Contribution to product excellence"
    ]
  },
  {
    title: "Marketing Specialist",
    icon: Megaphone,
    summary: "Develop and execute marketing campaigns to promote our brands and engage customers.",
    responsibilities: [
      "Plan and implement digital and offline marketing campaigns",
      "Create engaging content for social media and print",
      "Analyze campaign performance and report results",
      "Coordinate with sales and creative teams"
    ],
    requirements: [
      "Bachelor's degree in Marketing, Communications, or related field",
      "Experience in marketing or advertising",
      "Creativity and strong communication skills"
    ],
    benefits: [
      "Dynamic work environment",
      "Opportunities for creativity",
      "Performance-based bonuses"
    ]
  },
  {
    title: "Logistics Coordinator",
    icon: Truck,
    summary: "Manage supply chain operations, coordinate shipments, and ensure timely delivery of our products.",
    responsibilities: [
      "Coordinate inbound and outbound logistics",
      "Manage inventory and warehouse operations",
      "Liaise with transporters and vendors",
      "Optimize delivery routes and schedules"
    ],
    requirements: [
      "Bachelor's degree or diploma in Logistics, Supply Chain, or related field",
      "Experience in logistics or warehouse management",
      "Organizational and problem-solving skills"
    ],
    benefits: [
      "Competitive salary",
      "Travel and meal allowances",
      "Career advancement opportunities"
    ]
  }
];

const Careers = () => {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      <div className="pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">Careers at Shangrila Distillery</h1>
            <p className="text-xl distillery-text max-w-2xl mx-auto">
              Join our passionate team and help shape the future of Nepalese spirits. We value innovation, dedication, and a commitment to excellence.
            </p>
          </div>

          {/* Job Openings */}
          <div className="mb-16">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Current Openings</h2>
            <div className="grid gap-10">
              {jobOpenings.map((job) => (
                <div key={job.title} className="distillery-card p-8 bg-stone-900/70 rounded-2xl shadow-xl flex flex-col md:flex-row items-center md:items-start gap-8 hover:scale-[1.02] transition-transform duration-300">
                  <div className="flex-shrink-0 flex items-center justify-center w-20 h-20 rounded-full bg-amber-900/30 border-2 border-amber-400/30 mb-4 md:mb-0">
                    <job.icon className="h-10 w-10 text-amber-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-amber-100 mb-2 font-playfair">{job.title}</h3>
                    <p className="text-amber-100/80 font-crimson text-base mb-4">{job.summary}</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
                      <div>
                        <h4 className="text-lg font-semibold text-amber-200 mb-2">Responsibilities</h4>
                        <ul className="list-disc list-inside text-amber-100/70 text-sm font-crimson space-y-1">
                          {job.responsibilities.map((item, idx) => (
                            <li key={idx}>{item}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-amber-200 mb-2">Requirements</h4>
                        <ul className="list-disc list-inside text-amber-100/70 text-sm font-crimson space-y-1">
                          {job.requirements.map((item, idx) => (
                            <li key={idx}>{item}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-amber-200 mb-2">Benefits</h4>
                        <ul className="list-disc list-inside text-amber-100/70 text-sm font-crimson space-y-1">
                          {job.benefits.map((item, idx) => (
                            <li key={idx}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Email */}
          <div className="text-center mt-12">
            <span className="text-amber-200 font-crimson text-lg">To apply or inquire, email us at </span>
            <a href="mailto:<EMAIL>" className="text-amber-400 underline font-bold font-baskerville hover:text-amber-300 transition-colors"><EMAIL></a>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
};

export default Careers; 