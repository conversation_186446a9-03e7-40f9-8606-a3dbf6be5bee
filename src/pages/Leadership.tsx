import { useState } from "react";
import { Link } from "react-router-dom";
import Navigation from "../components/Navigation";
import Footer from "../components/home/<USER>";
import { X } from "lucide-react";

const Leadership = () => {
  const [selectedMember, setSelectedMember] = useState<number | null>(null);

  const teamMembers = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON>",
      position: "Managing Director",
      image: "/lovable-uploads/prashanna.png",
      shortDescription: "<PERSON><PERSON><PERSON> is a dynamic and forward-thinking leader with a deep-rooted passion for the liquor and beverage industry.",
      fullDescription: `<PERSON><PERSON><PERSON>, the Managing Director of Shangrila Distillery, is a dynamic and forward-thinking leader with a deep-rooted passion for the liquor and beverage industry. He began his journey in Australia, where he gained invaluable exposure to world-class distilleries and participated in advanced blending workshops, sharpening his technical and operational understanding of premium spirits production.

He holds a Bachelor of Business Administration from Federation University and a Master of Business Administration from Victoria University, Australia—credentials that reflect his strong academic foundation in global business and management.

With a strategic vision and entrepreneurial spirit, he is committed to establishing Shangrila Distillery as a premier name in Nepal's liquor industry. His ambition is not only to develop a robust domestic presence but also to position Shangrila as a competitive exporter of high-quality spirits. Under his leadership, the distillery aims to contribute significantly to Nepal's economic landscape by enhancing the country's export footprint and reputation for excellence in craftsmanship.

Regarded as a rising figure in the industry, Prajanna Raj Adhikari brings clarity of purpose, global perspective, and relentless drive—qualities that are shaping Shangrila Distillery's path to becoming a distinguished force in the world of spirits.`
    },
    {
      id: 2,
      name: "Prameshwor Raj Adhikari",
      position: "Executive Director",
      image: "/lovable-uploads/favicon-shangrila.png",
      shortDescription: "With decades of experience in the construction industry, Prameshwor Raj Adhikari brings deep business insight and a strong foundation in infrastructure development.",
      fullDescription: `Prameshwor Raj Adhikari brings decades of experience in the construction industry, providing deep business insight and a strong foundation in infrastructure development to Shangrila Distillery. His extensive background in construction and project management has been instrumental in shaping the physical foundation of our distillery operations.

He single-handedly designed the entire layout and facility of Shangrila Distillery, blending practical engineering with visionary planning. His expertise in infrastructure development ensures that our distillery operates with maximum efficiency while maintaining the highest standards of safety and quality.

With his comprehensive understanding of business operations and construction management, Prameshwor Raj Adhikari plays a crucial role in the strategic planning and execution of our expansion projects. His vision extends beyond just building structures; he creates environments where craftsmanship and innovation can flourish.

His leadership in facility design and operational planning has positioned Shangrila Distillery as a state-of-the-art production facility that honors traditional distilling methods while embracing modern efficiency and sustainability practices.`
    },
    {
      id: 3,
      name: "Sanskar Agrawal",
      position: "Executive Director",
      image: "/lovable-uploads/favicon-shangrila.png",
      shortDescription: "Sanskar Agrawal brings strategic insight and operational excellence to drive innovation and growth at Shangrila Distillery.",
      fullDescription: `Sanskar Agrawal serves as Executive Director at Shangrila Distillery, bringing a wealth of strategic insight and operational excellence to the organization. His leadership style combines analytical thinking with innovative approaches to business development and operational efficiency.

With a strong background in business strategy and operations management, Sanskar plays a pivotal role in driving the company's growth initiatives and ensuring operational excellence across all departments. His expertise in strategic planning and business development has been instrumental in positioning Shangrila Distillery for sustainable growth and market expansion.

Sanskar's commitment to innovation and quality excellence aligns perfectly with Shangrila Distillery's mission to produce world-class spirits. He oversees various operational aspects of the business, ensuring that every process meets the highest standards of quality and efficiency.

His forward-thinking approach and dedication to continuous improvement make him an invaluable member of the leadership team, contributing significantly to the company's vision of becoming a leading name in the global spirits industry.`
    }
  ];

  const openModal = (id: number) => {
    setSelectedMember(id);
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setSelectedMember(null);
    document.body.style.overflow = 'auto';
  };

  const selectedMemberData = selectedMember ? teamMembers.find(member => member.id === selectedMember) : null;

  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20">
          {/* Title */}
          <div className="text-center mb-12 sm:mb-16">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4 sm:mb-6">Our Leadership</h1>
            <p className="text-base sm:text-lg lg:text-xl text-amber-100/70 max-w-3xl mx-auto font-crimson leading-relaxed px-2">
              Meet the visionaries guiding Shangrila Distillery to new heights of excellence and innovation.
            </p>
          </div>

          {/* Leadership Team Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {teamMembers.map((member) => {
              // Prajanna Raj Adhikari (id: 1) should link to dedicated page
              if (member.id === 1) {
                return (
                  <Link
                    key={member.id}
                    to="/leadership/prajanna-raj-adhikari"
                    className="group cursor-pointer distillery-card p-6 text-center hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-amber-500/20 block"
                  >
                    <div className="relative mb-6 overflow-hidden rounded-lg mx-auto w-32 h-32 sm:w-40 sm:h-40">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 border-4 border-amber-400/40 rounded-lg"
                      />
                    </div>
                    <h3 className="text-xl sm:text-2xl font-playfair font-bold text-amber-100 mb-2">
                      {member.name}
                    </h3>
                    <p className="text-amber-300 font-medium font-crimson mb-4">{member.position}</p>
                    <p className="text-amber-100/80 font-crimson text-sm leading-relaxed">
                      {member.shortDescription}
                    </p>
                    <div className="mt-4 text-amber-400 text-sm font-medium">
                      Learn more about Prajanna Raj Adhikari →
                    </div>
                  </Link>
                );
              }

              // Other members open modal
              return (
                <div
                  key={member.id}
                  onClick={() => openModal(member.id)}
                  className="group cursor-pointer distillery-card p-6 text-center hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-amber-500/20"
                >
                  <div className="relative mb-6 overflow-hidden rounded-lg mx-auto w-32 h-32 sm:w-40 sm:h-40">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 border-4 border-amber-400/40 rounded-lg"
                    />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-playfair font-bold text-amber-100 mb-2">
                    {member.name}
                  </h3>
                  <p className="text-amber-300 font-medium font-crimson mb-4">{member.position}</p>
                  <p className="text-amber-100/80 font-crimson text-sm leading-relaxed">
                    {member.shortDescription}
                  </p>
                  <div className="mt-4 text-amber-400 text-sm font-medium">
                    Click to read more →
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <Footer />
      </div>

      {/* Modal */}
      {selectedMember !== null && selectedMemberData && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={closeModal}
        >
          <div
            className="relative bg-stone-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-amber-400/30 shadow-2xl shadow-amber-500/10"
            onClick={e => e.stopPropagation()}
          >
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-amber-100 hover:text-amber-300 transition-colors p-2 z-10"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Modal Header */}
            <div className="bg-gradient-to-r from-amber-900/30 to-amber-800/30 p-6 sm:p-8">
              <div className="flex flex-col sm:flex-row items-center gap-6">
                <div className="flex-shrink-0">
                  <img
                    src={selectedMemberData.image}
                    alt={selectedMemberData.name}
                    className="w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 rounded-lg border-4 border-amber-400/60 shadow-xl object-cover bg-white/5"
                  />
                </div>
                <div className="text-center sm:text-left">
                  <h2 className="text-2xl sm:text-3xl font-playfair font-bold text-amber-100 mb-2">
                    {selectedMemberData.name}
                  </h2>
                  <p className="text-lg text-amber-200 font-semibold mb-2">{selectedMemberData.position}</p>
                  <p className="text-amber-100/80 font-crimson">Shangrila Distillery</p>
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 sm:p-8">
              <div className="space-y-4 text-amber-100/90 font-crimson leading-relaxed">
                {selectedMemberData.fullDescription.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="text-justify">
                    {paragraph}
                  </p>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-amber-400/20">
                <div className="text-right font-semibold text-amber-200 font-crimson">
                  — {selectedMemberData.position}<br />Shangrila Distillery
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Leadership;
