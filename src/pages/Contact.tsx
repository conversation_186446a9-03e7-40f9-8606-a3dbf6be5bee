import Navigation from "../components/Navigation";
import {
  Mail,
  Phone,
  MapPin,
  MessageSquare,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Link } from "react-router-dom";
import Map from "../components/Map";
import Footer from "../components/home/<USER>";
import { useState } from "react";
import axios from "axios";
import { toast } from "sonner";
const Contact = () => {
    const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    inquiryType: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ""
      });
    }
  };

  const handleSelectChange = (value: string) => {
    setFormData({
      ...formData,
      inquiryType: value,
    });
    if (errors.inquiryType) {
      setErrors({
        ...errors,
        inquiryType: ""
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^[0-9\s\-+()]*$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }
    if (!formData.inquiryType) newErrors.inquiryType = 'Please select an inquiry type';
    if (!formData.message.trim()) newErrors.message = 'Message is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL || 'https://mail.shangriladistillery.com'}/api/contact`,
        formData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
      if (response.status === 200) {
        toast.success("Your message has been sent successfully!");
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
          inquiryType: "",
          subject: "",
          message: "",
        });
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(
        error.response?.data?.message || 
        'Failed to send message. Please try again later.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">
              Visit Us
            </h1>
            <p className="text-xl distillery-text max-w-3xl mx-auto">
              Get in touch with our team for inquiries, partnerships, or to
              learn more about our premium spirits.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div className="space-y-8">
              <div className="distillery-card p-6">
                <div className="flex items-start space-x-4">
                  <Mail className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Email
                    </h3>
                    <p className="distillery-text">
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>

              <div className="distillery-card p-6">
                <div className="flex items-start space-x-4">
                  <Phone className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Phone
                    </h3>
                    <p className="distillery-text">+977-1-4528118</p>
                    <p className="distillery-text">WhatsApp: +977 1-4528118</p>
                  </div>
                </div>
              </div>

              <div className="distillery-card p-6 space-y-6">
                <div className="flex items-start space-x-4">
                  <MapPin className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Head Office Address
                    </h3>
                    <p className="distillery-text">
                      Pipalbot dillibazar-29
                      <br />
                      kathmandu nepal
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <MapPin className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Factory Location
                    </h3>
                    <p className="distillery-text">
                      Brahmanagar, Rapti Nagarpalika-9
                      <br />
                      Chitwan, Nepal
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-amber-600 to-amber-500 rounded-lg p-6 text-stone-900">
                <MessageSquare className="h-8 w-8 text-stone-900 mb-4" />
                <h3 className="text-xl font-playfair font-semibold mb-2">
                  Business Hours
                </h3>
                <p className="font-crimson">
                  Sunday - Friday: 9:00 AM - 6:00 PM
                  <br />
                  Saturday: Closed
                  <br />
                </p>
              </div>

              {/* Map Component */}
              <Map />
            </div>

            {/* Contact Form */}
            <div className="distillery-card p-8">
              <h2 className="text-2xl font-playfair font-bold text-amber-100 mb-6">
                Send us a Message
              </h2>
              <form className="space-y-6" onSubmit={handleSubmit}>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="firstName" className="text-amber-200">
                      First Name
                    </Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      type="text"
                      value={formData.firstName}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.firstName ? 'border-red-500' : ''}`}
                      onChange={handleChange}
                    />
                    {errors.firstName && <p className="mt-1 text-sm text-red-400">{errors.firstName}</p>}
                  </div>
                  <div>
                    <Label htmlFor="lastName" className="text-amber-200">
                      Last Name
                    </Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      type="text"
                      value={formData.lastName}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.lastName ? 'border-red-500' : ''}`}
                      onChange={handleChange}
                    />
                    {errors.lastName && <p className="mt-1 text-sm text-red-400">{errors.lastName}</p>}
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="email" className="text-amber-200">
                    Email
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.email ? 'border-red-500' : ''}`}
                    onChange={handleChange}
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="phone" className="text-amber-200">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.phone ? 'border-red-500' : ''}`}
                      onChange={handleChange}
                      placeholder="+****************"
                    />
                    {errors.phone && <p className="mt-1 text-sm text-red-400">{errors.phone}</p>}
                  </div>
                <div>
                    <Label htmlFor="inquiryType" className="text-amber-200">
                      Inquiry Type
                    </Label>
                    <Select
                      value={formData.inquiryType}
                      onValueChange={handleSelectChange}
                    >
                      <SelectTrigger className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.inquiryType ? 'border-red-500' : ''}`}>
                      <SelectValue placeholder="Select inquiry type" />
                    </SelectTrigger>
                    <SelectContent className="bg-stone-800 border-amber-500/30">
                        <SelectItem value="general">General Inquiry</SelectItem>
                        <SelectItem value="wholesale">Wholesale Inquiry</SelectItem>
                        <SelectItem value="partnership">Partnership</SelectItem>
                        <SelectItem value="press">Press/Media</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                    {errors.inquiryType && <p className="mt-1 text-sm text-red-400">{errors.inquiryType}</p>}
                  </div>
                </div>

                <div className="space-y-4">
                <div>
                    <Label htmlFor="subject" className="text-amber-200">
                      Subject (Optional)
                    </Label>
                    <Input
                      id="subject"
                      name="subject"
                      type="text"
                      value={formData.subject}
                      className="mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400"
                      onChange={handleChange}
                    />
                </div>
                <div>
                    <Label htmlFor="message" className="text-amber-200">
                      Message
                    </Label>
                    <Textarea
                      id="message"
                      name="message"
                      rows={5}
                      value={formData.message}
                      className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.message ? 'border-red-500' : ''}`}
                      onChange={handleChange}
                    />
                    {errors.message && <p className="mt-1 text-sm text-red-400">{errors.message}</p>}
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full bg-amber-600 hover:bg-amber-700 text-white py-6 text-lg font-medium transition-colors duration-200 ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending...
                    </>
                  ) : 'Send Message'}
                </Button>
              </form>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
};

export default Contact;
