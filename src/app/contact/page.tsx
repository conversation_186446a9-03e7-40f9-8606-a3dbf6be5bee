import Navigation from "@/components/Navigation";
import ContactForm from "@/components/ContactForm";
import Map from "@/components/Map";
import Footer from "@/components/home/<USER>";
import { Mail, Phone, MapPin, MessageSquare } from "lucide-react";
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Contact Us - Visit Our Distillery',
  description: 'Get in touch with Shangrila Distillery for inquiries, partnerships, or to learn more about our premium spirits. Visit our facility in Chitwan, Nepal.',
  keywords: ['Contact Shangrila Distillery', 'Nepal Distillery Contact', 'Visit Distillery Nepal', 'Shangrila Distillery Address', 'Premium Spirits Inquiry'],
  openGraph: {
    title: 'Contact Shangrila Distillery | Visit Us',
    description: 'Get in touch with our team for inquiries, partnerships, or to learn more about our premium spirits.',
    url: 'https://shangriladistillery.com/contact',
    images: [
      {
        url: '/lovable-uploads/favicon-shangrila.png',
        width: 1200,
        height: 630,
        alt: 'Contact Shangrila Distillery',
      },
    ],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/contact',
  },
}

export default function Contact() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">
              Visit Us
            </h1>
            <p className="text-xl distillery-text max-w-3xl mx-auto">
              Get in touch with our team for inquiries, partnerships, or to
              learn more about our premium spirits.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div className="space-y-8">
              <div className="distillery-card p-6">
                <div className="flex items-start space-x-4">
                  <Mail className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Email
                    </h3>
                    <p className="distillery-text">
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>

              <div className="distillery-card p-6">
                <div className="flex items-start space-x-4">
                  <Phone className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Phone
                    </h3>
                    <p className="distillery-text">+977-1-4528118</p>
                    <p className="distillery-text">WhatsApp: +977 1-4528118</p>
                  </div>
                </div>
              </div>

              <div className="distillery-card p-6 space-y-6">
                <div className="flex items-start space-x-4">
                  <MapPin className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Head Office Address
                    </h3>
                    <p className="distillery-text">
                      Pipalbot dillibazar-29
                      <br />
                      kathmandu nepal
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <MapPin className="h-6 w-6 text-amber-400 mt-1" />
                  <div>
                    <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">
                      Factory Location
                    </h3>
                    <p className="distillery-text">
                      Brahmanagar, Rapti Nagarpalika-9
                      <br />
                      Chitwan, Nepal
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-amber-600 to-amber-500 rounded-lg p-6 text-stone-900">
                <MessageSquare className="h-8 w-8 text-stone-900 mb-4" />
                <h3 className="text-xl font-playfair font-semibold mb-2">
                  Business Hours
                </h3>
                <p className="font-crimson">
                  Sunday - Friday: 9:00 AM - 6:00 PM
                  <br />
                  Saturday: Closed
                  <br />
                </p>
              </div>

              {/* Map Component */}
              <Map />
            </div>

            {/* Contact Form */}
            <ContactForm />
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
}