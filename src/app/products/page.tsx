import Navigation from "@/components/Navigation";
import { useState } from "react";
import ProductHero from "@/components/products/ProductHero";
import CategoryFilter from "@/components/products/CategoryFilter";
import ProductCard from "@/components/products/ProductCard";
import CraftingProcess from "@/components/products/CraftingProcess";
import AwardsSection from "@/components/products/AwardsSection";
import ComingSoonSection from "@/components/products/ComingSoonSection";
import Footer from "@/components/home/<USER>";
import { products, categories } from "@/data/products";
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Premium Heritage Collection - Whiskey, Vodka & Spirits',
  description: 'Explore Shangrila Distillery\'s premium heritage collection. Discover our finest whiskeys, vodkas, and craft spirits made with traditional methods in Nepal.',
  keywords: ['Shangrila Products', 'Nepal Whiskey Collection', 'Premium Vodka Nepal', 'Tejas Gold', 'Tejas Black', 'Reef Vodka', 'LOD Vodka', 'Craft Spirits Nepal'],
  openGraph: {
    title: 'Premium Heritage Collection | Shangrila Distillery',
    description: 'Discover our premium collection of spirits, each crafted with precision and passion in the heart of Nepal\'s majestic Himalayas.',
    url: 'https://shangriladistillery.com/products',
    images: [
      {
        url: '/lovable-uploads/tejas gold.jpeg',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery Premium Heritage Collection',
      },
    ],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/products',
  },
}

export default function Products() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <ProductHero />
          
          {/* Product Banners */}
          <div className="space-y-12 mb-16">
            {products.map((product, index) => (
              <ProductCard key={product.name} product={product} index={index} />
            ))}
          </div>

          <CraftingProcess />
          <AwardsSection />
          <ComingSoonSection />
        </div>

        <Footer />
      </div>
    </div>
  );
}