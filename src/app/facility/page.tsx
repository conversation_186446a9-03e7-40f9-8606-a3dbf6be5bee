import Navigation from "@/components/Navigation";
import FacilityHero from "@/components/facility/FacilityHero";
import FacilityImageGallery from "@/components/facility/FacilityImageGallery";
import FacilitySpecifications from "@/components/facility/FacilitySpecifications";
import EnvironmentalCommitment from "@/components/facility/EnvironmentalCommitment";
import ExpansionPlans from "@/components/facility/ExpansionPlans";
import Footer from "@/components/home/<USER>";
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Our State-of-the-Art Distillery Facility',
  description: 'Tour Shangrila Distillery\'s world-class facility in Nepal. Discover our advanced production capabilities, quality standards, and sustainable practices.',
  keywords: ['Shangrila Distillery Facility', 'Nepal Distillery Tour', 'Craft Spirits Production', 'Distillery Technology', 'Quality Standards Nepal'],
  openGraph: {
    title: 'Master Distillery Facility | Shangrila Distillery',
    description: 'A world-class distillery facility combining traditional craftsmanship with modern technology in the heart of Nepal\'s majestic Himalayas.',
    url: 'https://shangriladistillery.com/facility',
    images: [
      {
        url: '/lovable-uploads/d30150e7-f7de-46a2-bee7-4d4d9a82caff.png',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery Facility',
      },
    ],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/facility',
  },
}

export default function Facility() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <FacilityHero />
          <FacilityImageGallery />
          <FacilitySpecifications />
          <EnvironmentalCommitment />
          <ExpansionPlans />
        </div>

        <Footer />
      </div>
    </div>
  );
}