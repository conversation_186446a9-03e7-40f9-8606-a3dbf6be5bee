import Navigation from "@/components/Navigation";
import SalesForm from "@/components/SalesForm";
import Footer from "@/components/home/<USER>";
import { MapPin, Users, Store, Building } from "lucide-react";
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Sales & Distribution - Join Our Network',
  description: 'Join Shangrila Distillery\'s distribution network in Nepal. Become a dealer and bring premium craft spirits to your local market.',
  keywords: ['Shangrila Distillery Sales', 'Nepal Spirits Distribution', 'Become Dealer Nepal', 'Premium Spirits Sales', 'Distillery Partnership Nepal'],
  openGraph: {
    title: 'Sales & Distribution | Shangrila Distillery',
    description: 'Join our growing network of partners and bring premium Nepalese spirits to your market.',
    url: 'https://shangriladistillery.com/sales',
    images: [
      {
        url: '/lovable-uploads/favicon-shangrila.png',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery Sales',
      },
    ],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/sales',
  },
}

export default function Sales() {
  const segments = [
    { price: "25 UP", description: "Extra Premium segment" },
    { price: "30 UP", description: "Premium Vodka Segment" },
    { price: "40 UP", description: "Premium Segment" },
    { price: "70 UP", description: "Entry Segment" }
  ];

  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="flex justify-center mb-6">
              <img
                src="/lovable-uploads/favicon-shangrila.png"
                alt="Shangrila Distillery"
                className="h-20 w-20 rounded-full object-cover filter brightness-110 shadow-lg"
              />
            </div>
            <h1 className="text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">Local Heritage</h1>
            <p className="text-xl distillery-text max-w-3xl mx-auto">
              We're expanding across Nepal, bringing premium spirits to every corner of the nation.
            </p>
          </div>

          {/* Map Section */}
          <div className="distillery-card p-8 mb-16 text-center">
            <MapPin className="h-16 w-16 text-amber-400 mx-auto mb-6 animate-float" />
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-4">Expanding Across Nepal</h2>
            <p className="text-lg distillery-text max-w-2xl mx-auto">
              From Kathmandu to the furthest reaches of our beautiful nation, 
              we're building a comprehensive distribution network to serve every region.
            </p>
          </div>

          {/* Target Segments */}
          <div className="mb-16">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Our Target Segments</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {segments.map((segment) => (
                <div key={segment.price} className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
                  <div className="text-3xl font-playfair font-bold text-amber-400 mb-2">{segment.price}</div>
                  <p className="distillery-text">{segment.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Distribution Channels */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Store className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Bottle Shops</h3>
              <p className="distillery-text">Premium retail outlets across major cities and towns</p>
            </div>
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Building className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">HORECA</h3>
              <p className="distillery-text">Hotels, restaurants, and cafes for premium dining experiences</p>
            </div>
            <div className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
              <Users className="h-12 w-12 text-amber-400 mx-auto mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Distribution</h3>
              <p className="distillery-text">Authorized distributors for nationwide coverage</p>
            </div>
          </div>

          {/* Storage Capacity */}
          <div className="distillery-card p-8 mb-16 text-center">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-4">2,00,000+ litres storage capacity</h2>
            <p className="text-lg distillery-text max-w-2xl mx-auto">
              and planning on more
            </p>
          </div>

          {/* Why Partner With Us */}
          <div className="mb-16">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Why Partner With Us?</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="distillery-card p-6 text-center">
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Quality Products</h3>
                <p className="distillery-text">A portfolio of award-winning, premium quality spirits.</p>
              </div>
              <div className="distillery-card p-6 text-center">
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Marketing Support</h3>
                <p className="distillery-text">Comprehensive marketing and promotional support.</p>
              </div>
              <div className="distillery-card p-6 text-center">
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">Competitive Pricing</h3>
                <p className="distillery-text">Attractive pricing and margin for our partners.</p>
              </div>
            </div>
          </div>

          {/* Dealer Application Form */}
          <SalesForm />
        </div>

        <Footer />
      </div>
    </div>
  );
}