@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 25 20% 8%;
    --foreground: 40 15% 92%;

    --card: 25 20% 12%;
    --card-foreground: 40 15% 92%;

    --popover: 25 20% 12%;
    --popover-foreground: 40 15% 92%;

    --primary: 35 65% 55%;
    --primary-foreground: 25 20% 8%;

    --secondary: 25 15% 18%;
    --secondary-foreground: 40 15% 85%;

    --muted: 25 15% 18%;
    --muted-foreground: 40 10% 65%;

    --accent: 35 45% 35%;
    --accent-foreground: 40 15% 92%;

    --destructive: 0 65% 55%;
    --destructive-foreground: 40 15% 92%;

    --border: 25 15% 22%;
    --input: 25 15% 22%;
    --ring: 35 65% 55%;

    --radius: 0.75rem;

    --sidebar-background: 25 20% 10%;
    --sidebar-foreground: 40 15% 85%;
    --sidebar-primary: 35 65% 55%;
    --sidebar-primary-foreground: 25 20% 8%;
    --sidebar-accent: 25 15% 18%;
    --sidebar-accent-foreground: 40 15% 85%;
    --sidebar-border: 25 15% 22%;
    --sidebar-ring: 35 65% 55%;

    /* Custom distillery colors */
    --amber-gold: 45 95% 68%;
    --rich-brown: 25 45% 25%;
    --copper: 25 85% 55%;
    --oak: 35 35% 45%;
    --cream: 45 35% 92%;
    --whiskey: 35 75% 45%;

    /* Font variables */
    --font-playfair: 'Playfair Display', serif;
    --font-crimson: 'Crimson Text', serif;
    --font-baskerville: 'Libre Baskerville', serif;
  }

  .light {
    --background: 45 35% 96%;
    --foreground: 25 45% 12%;

    --card: 0 0% 100%;
    --card-foreground: 25 45% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 25 45% 12%;

    --primary: 35 65% 45%;
    --primary-foreground: 45 35% 96%;

    --secondary: 45 25% 88%;
    --secondary-foreground: 25 45% 18%;

    --muted: 45 25% 88%;
    --muted-foreground: 25 25% 45%;

    --accent: 35 45% 85%;
    --accent-foreground: 25 45% 18%;

    --destructive: 0 65% 55%;
    --destructive-foreground: 45 35% 96%;

    --border: 45 25% 85%;
    --input: 45 25% 85%;
    --ring: 35 65% 45%;

    --sidebar-background: 45 35% 98%;
    --sidebar-foreground: 25 35% 25%;
    --sidebar-primary: 35 65% 45%;
    --sidebar-primary-foreground: 45 35% 96%;
    --sidebar-accent: 45 25% 92%;
    --sidebar-accent-foreground: 25 35% 25%;
    --sidebar-border: 45 25% 85%;
    --sidebar-ring: 35 65% 45%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-crimson);
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(25 20% 6%) 100%);
    min-height: 100vh;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-playfair);
  }

  .distillery-gradient {
    background: linear-gradient(135deg, 
      hsl(25 45% 8%) 0%, 
      hsl(25 35% 12%) 25%, 
      hsl(30 40% 15%) 50%, 
      hsl(35 35% 18%) 75%, 
      hsl(25 30% 12%) 100%);
  }

  .amber-gradient {
    background: linear-gradient(135deg, 
      hsl(35 85% 45%) 0%, 
      hsl(40 90% 55%) 50%, 
      hsl(45 95% 65%) 100%);
  }

  .copper-gradient {
    background: linear-gradient(135deg, 
      hsl(25 75% 45%) 0%, 
      hsl(30 80% 55%) 50%, 
      hsl(35 85% 65%) 100%);
  }
}

@layer components {
  .distillery-card {
    @apply bg-gradient-to-br from-stone-900 via-amber-900/20 to-stone-800 backdrop-blur-sm border border-amber-500/20 rounded-lg shadow-2xl;
  }

  .premium-button {
    @apply bg-gradient-to-r from-amber-600 to-amber-500 hover:from-amber-500 hover:to-amber-400 text-amber-950 font-semibold shadow-lg transition-all duration-300 hover:shadow-amber-500/25 hover:scale-105;
  }

  .distillery-text {
    @apply text-amber-100/90 leading-relaxed;
  }

  .aged-paper {
    background: linear-gradient(135deg, 
      hsl(40 45% 92%) 0%, 
      hsl(35 40% 88%) 50%, 
      hsl(30 35% 85%) 100%);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
  }
}