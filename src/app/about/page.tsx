import Navigation from "@/components/Navigation";
import Footer from "@/components/home/<USER>";
import AboutHero from "@/components/about/AboutHero";
import MissionVision from "@/components/about/MissionVision";
import ValuesSection from "@/components/about/ValuesSection";
import TimelineSection from "@/components/about/TimelineSection";
import CompanyHistory from "@/components/about/CompanyHistory";
import FeaturedBlog from "@/components/home/<USER>";
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'About Us - Our Story & Heritage',
  description: 'Learn about Shangrila Distillery\'s heritage, mission, and vision. Discover how we became Nepal\'s premier craft spirits producer with traditional methods and modern innovation.',
  keywords: ['Shangrila Distillery History', 'Nepal Distillery Heritage', 'Craft Spirits Nepal', 'Distillery Mission Vision', 'Premium Spirits Heritage'],
  openGraph: {
    title: 'About Shangrila Distillery - Our Heritage & Story',
    description: 'Discover the heritage and story behind Nepal\'s premier craft distillery. Learn about our mission, vision, and commitment to excellence.',
    url: 'https://shangriladistillery.com/about',
    images: [
      {
        url: '/lovable-uploads/favicon-shangrila.png',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery Heritage',
      },
    ],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/about',
  },
}

export default function About() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <AboutHero />
          <MissionVision />
          <FeaturedBlog />
          <ValuesSection />
          <TimelineSection />
          <CompanyHistory />
          
          {/* Strategic Partnerships */}
          <div className="mb-20">
            <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Strategic Partnerships</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Trade Vision Partners Card */}
              <div className="distillery-card p-8 text-center">
                <div className="flex items-center justify-center mb-4">
                  <span className="text-xl font-bold text-amber-200 mr-3">Trade Vision Partners</span>
                  <a 
                    href="https://tradevisionpartners.com/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-amber-400 hover:text-amber-300 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2h6m5-1l2 2m0 0l-7 7m7-7V3m0 4h-4" /></svg>
                  </a>
                </div>
                <span className="block text-amber-300 mb-2">Australia</span>
                <span className="block distillery-text text-base mb-2">Our flagship partnership brings decades of international distribution expertise, opening doors to premium markets across Australia and beyond. Together, we're setting new standards for Nepalese spirits on the global stage.</span>
              </div>
              
              {/* ShyamBaba Group Card */}
              <div className="distillery-card p-8 text-center">
                <div className="flex items-center justify-center mb-4">
                  <span className="text-xl font-bold text-amber-200 mr-3">ShyamBaba Group</span>
                  <a 
                    href="https://sbgcompanies.com/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-amber-400 hover:text-amber-300 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2h6m5-1l2 2m0 0l-7 7m7-7V3m0 4h-4" /></svg>
                  </a>
                </div>
                <span className="block text-amber-300 mb-2">Nepal</span>
                <span className="block distillery-text text-base mb-2">Nepal's foremost diversified business group with interests in Food Products, Cement, Construction, Manufacturing and Trading.</span>
                <span className="block distillery-text text-base">With over 2000 employees and 10,000 direct customers, ShyamBaba Group touches lives across Nepal through its vast offering of market-leading high quality consumer products.</span>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
}