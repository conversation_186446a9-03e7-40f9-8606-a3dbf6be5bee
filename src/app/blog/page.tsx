import Navigation from "@/components/Navigation";
import Footer from "@/components/home/<USER>";
import { blogs } from "@/data/blogs";
import BlogCard from "@/components/blog/BlogCard";
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Blog - Stories from Shangrila Distillery',
  description: 'Discover the stories behind Shangrila Distillery, from our heritage to our sustainable practices. Read about Nepal\'s premier craft spirits producer.',
  keywords: ['Shangrila Distillery Blog', 'Nepal Distillery Stories', 'Craft Spirits Blog', 'Distillery Heritage', 'Premium Spirits News'],
  openGraph: {
    title: 'Blog | Shangrila Distillery Stories',
    description: 'Discover the stories behind Shangrila Distillery, from our heritage to our sustainable practices.',
    url: 'https://shangriladistillery.com/blog',
    images: [
      {
        url: '/lovable-uploads/prashanna.png',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery Blog',
      },
    ],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/blog',
  },
}

export default function Blog() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">Our Blog</h1>
            <p className="text-xl distillery-text max-w-3xl mx-auto">
              Discover the stories behind Shangrila Distillery, from our heritage to our sustainable practices.
            </p>
          </div>

          {/* Blog Posts */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogs.map((blog) => (
              <BlogCard
                key={blog.id}
                slug={blog.slug}
                title={blog.title}
                introduction={blog.introduction}
              />
            ))}
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
}