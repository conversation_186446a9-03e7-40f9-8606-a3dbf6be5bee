import Navigation from "@/components/Navigation";
import Footer from "@/components/home/<USER>";
import { blogs } from "@/data/blogs";
import { notFound } from 'next/navigation';
import type { Metadata } from 'next'

interface BlogPostProps {
  params: {
    slug: string;
  };
}

export async function generateStaticParams() {
  return blogs.map((blog) => ({
    slug: blog.slug,
  }));
}

export async function generateMetadata({ params }: BlogPostProps): Promise<Metadata> {
  const blog = blogs.find((b) => b.slug === params.slug);

  if (!blog) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    title: blog.metaTitle,
    description: blog.metaDescription,
    keywords: blog.metaTitle.split(' ').concat(['Shangrila Distillery', 'Nepal Spirits', 'Premium Distillery']),
    openGraph: {
      title: blog.metaTitle,
      description: blog.metaDescription,
      url: `https://shangriladistillery.com/blog/${blog.slug}`,
      images: blog.image ? [
        {
          url: blog.image,
          width: 1200,
          height: 630,
          alt: blog.title,
        },
      ] : [],
      type: 'article',
      publishedTime: '2024-01-01T00:00:00.000Z',
      authors: ['Shangrila Distillery'],
    },
    twitter: {
      card: 'summary_large_image',
      title: blog.metaTitle,
      description: blog.metaDescription,
      images: blog.image ? [blog.image] : [],
    },
    alternates: {
      canonical: `https://shangriladistillery.com/blog/${blog.slug}`,
    },
  };
}

export default function BlogPost({ params }: BlogPostProps) {
  const blog = blogs.find((b) => b.slug === params.slug);

  if (!blog) {
    notFound();
  }

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": blog.title,
    "description": blog.metaDescription,
    "image": blog.image ? `https://shangriladistillery.com${blog.image}` : undefined,
    "author": {
      "@type": "Organization",
      "name": "Shangrila Distillery"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Shangrila Distillery",
      "logo": {
        "@type": "ImageObject",
        "url": "https://shangriladistillery.com/lovable-uploads/favicon-shangrila.png"
      }
    },
    "datePublished": "2024-01-01T00:00:00.000Z",
    "dateModified": "2024-01-01T00:00:00.000Z",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://shangriladistillery.com/blog/${blog.slug}`
    }
  };

  return (
    <div className="min-h-screen distillery-gradient">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <Navigation />
      <div className="pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6">{blog.title}</h1>
          </div>

          {blog.image && (
            <img 
              src={blog.image} 
              alt={blog.title} 
              className="w-full md:w-1/2 mx-auto h-auto object-cover rounded-lg mb-12" 
            />
          )}

          <div className="prose prose-lg max-w-none mx-auto distillery-text">
            <p className="lead">{blog.introduction}</p>
            {blog.sections.map((section, index) => (
              <div key={index} className="mt-8">
                <h2 className="text-2xl font-playfair font-bold text-amber-100">{section.title}</h2>
                <p>{section.content}</p>
              </div>
            ))}
            <p className="mt-8">{blog.conclusion}</p>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
}