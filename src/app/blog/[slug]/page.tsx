import Navigation from "@/components/Navigation";
import Footer from "@/components/home/<USER>";
import { blogs } from "@/data/blogs";
import { notFound } from 'next/navigation';
import type { Metadata } from 'next'

interface BlogPostProps {
  params: {
    slug: string;
  };
}

export async function generateStaticParams() {
  return blogs.map((blog) => ({
    slug: blog.slug,
  }));
}

export async function generateMetadata({ params }: BlogPostProps): Promise<Metadata> {
  const blog = blogs.find((b) => b.slug === params.slug);

  if (!blog) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }

  // Enhanced keywords for <PERSON><PERSON><PERSON>'s blog post
  const prajannaKeywords = blog.slug === 'prajanna-raj-adhikari-leading-shangrila-distillery' ? [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON> Managing Director',
    '<PERSON><PERSON><PERSON> CEO',
    '<PERSON><PERSON><PERSON>grila Distillery',
    'Shangrila Distillery Managing Director',
    'Shangrila Distillery CEO',
    'Nepal Distillery CEO',
    'Nepal Whiskey Industry Leader',
    'Nepal Vodka Industry CEO',
    'Nepal Gin Producer CEO',
    'Federation University Business Graduate',
    'Victoria University MBA Nepal',
    'Australia Business Graduate Nepal',
    'Nepal Premium Spirits CEO',
    'Chitwan Distillery CEO',
    'Nepal Alcohol Industry Leader',
    'Himalayan Spirits CEO',
    'Nepal Beverage Industry CEO',
    'Craft Distillery CEO Nepal',
    'Nepal Liquor Industry Leader',
    'Distillery Management Expert',
    'Spirits Industry Executive Nepal',
    'Nepal Business Leader',
    'Premium Alcohol CEO Nepal',
    'Nepal Export Business Leader'
  ] : blog.metaTitle.split(' ').concat(['Shangrila Distillery', 'Nepal Spirits', 'Premium Distillery']);

  return {
    title: blog.metaTitle,
    description: blog.metaDescription,
    keywords: prajannaKeywords,
    openGraph: {
      title: blog.metaTitle,
      description: blog.metaDescription,
      url: `https://shangriladistillery.com/blog/${blog.slug}`,
      images: blog.image ? [
        {
          url: blog.image,
          width: 1200,
          height: 630,
          alt: blog.title,
        },
      ] : [],
      type: 'article',
      publishedTime: '2024-01-01T00:00:00.000Z',
      authors: ['Shangrila Distillery'],
    },
    twitter: {
      card: 'summary_large_image',
      title: blog.metaTitle,
      description: blog.metaDescription,
      images: blog.image ? [blog.image] : [],
    },
    alternates: {
      canonical: `https://shangriladistillery.com/blog/${blog.slug}`,
    },
  };
}

export default function BlogPost({ params }: BlogPostProps) {
  const blog = blogs.find((b) => b.slug === params.slug);

  if (!blog) {
    notFound();
  }

  // Enhanced structured data for Prajanna's blog post
  const structuredData = blog.slug === 'prajanna-raj-adhikari-leading-shangrila-distillery' ? {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": blog.title,
    "description": blog.metaDescription,
    "image": {
      "@type": "ImageObject",
      "url": blog.image ? `https://shangriladistillery.com${blog.image}` : "https://shangriladistillery.com/lovable-uploads/favicon-shangrila.png",
      "width": 1200,
      "height": 630
    },
    "author": {
      "@type": "Organization",
      "name": "Shangrila Distillery Editorial Team"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Shangrila Distillery",
      "logo": {
        "@type": "ImageObject",
        "url": "https://shangriladistillery.com/lovable-uploads/favicon-shangrila.png",
        "width": 512,
        "height": 512
      }
    },
    "datePublished": "2024-09-15T10:00:00.000Z",
    "dateModified": "2024-12-19T15:30:00.000Z",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://shangriladistillery.com/blog/${blog.slug}`
    },
    "keywords": "Prajanna Raj Adhikari, Managing Director, CEO, Shangrila Distillery, Nepal Distillery, Premium Spirits, Whiskey, Vodka, Gin, Federation University, Victoria University, MBA, Business Leader",
    "articleSection": "Leadership",
    "wordCount": 1200,
    "about": [
      {
        "@type": "Person",
        "name": "Prajanna Raj Adhikari",
        "jobTitle": "Managing Director & CEO",
        "worksFor": {
          "@type": "Organization",
          "name": "Shangrila Distillery",
          "url": "https://shangriladistillery.com"
        },
        "alumniOf": [
          {
            "@type": "EducationalOrganization",
            "name": "Federation University Australia"
          },
          {
            "@type": "EducationalOrganization",
            "name": "Victoria University Australia"
          }
        ]
      },
      {
        "@type": "Organization",
        "name": "Shangrila Distillery",
        "url": "https://shangriladistillery.com",
        "industry": "Alcoholic Beverages",
        "location": {
          "@type": "Place",
          "name": "Nepal"
        }
      }
    ],
    "mentions": [
      {
        "@type": "Organization",
        "name": "Shangrila Distillery",
        "url": "https://shangriladistillery.com"
      },
      {
        "@type": "Place",
        "name": "Nepal",
        "url": "https://en.wikipedia.org/wiki/Nepal"
      },
      {
        "@type": "EducationalOrganization",
        "name": "Federation University Australia",
        "url": "https://federation.edu.au"
      },
      {
        "@type": "EducationalOrganization",
        "name": "Victoria University Australia",
        "url": "https://www.vu.edu.au"
      }
    ]
  } : {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": blog.title,
    "description": blog.metaDescription,
    "image": blog.image ? `https://shangriladistillery.com${blog.image}` : undefined,
    "author": {
      "@type": "Organization",
      "name": "Shangrila Distillery"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Shangrila Distillery",
      "logo": {
        "@type": "ImageObject",
        "url": "https://shangriladistillery.com/lovable-uploads/favicon-shangrila.png"
      }
    },
    "datePublished": "2024-01-01T00:00:00.000Z",
    "dateModified": "2024-01-01T00:00:00.000Z",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://shangriladistillery.com/blog/${blog.slug}`
    }
  };

  // FAQ Schema for Prajanna's blog post
  const faqSchema = blog.slug === 'prajanna-raj-adhikari-leading-shangrila-distillery' ? {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Who is Prajanna Raj Adhikari?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Prajanna Raj Adhikari is the Managing Director and CEO of Shangrila Distillery, Nepal's leading premium spirits producer. He holds a Bachelor's degree from Federation University Australia and an MBA from Victoria University Australia."
        }
      },
      {
        "@type": "Question",
        "name": "What is Prajanna Raj Adhikari's educational background?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Prajanna earned his Bachelor's in Business from Federation University Australia, majoring in Marketing, and completed his MBA at Victoria University Australia, specializing in Global Business."
        }
      },
      {
        "@type": "Question",
        "name": "What experience does Prajanna Raj Adhikari have in the spirits industry?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Before becoming CEO of Shangrila Distillery, Prajanna gained extensive experience managing pubs, restaurants, and bottle shops. He also completed professional blending classes to master the technical aspects of spirits production."
        }
      },
      {
        "@type": "Question",
        "name": "What makes Prajanna Raj Adhikari qualified to lead Shangrila Distillery?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Prajanna combines hands-on hospitality experience, formal business education from Australian universities, professional blending expertise, and strategic vision to lead Nepal's premier distillery into global markets."
        }
      }
    ]
  } : null;

  return (
    <div className="min-h-screen distillery-gradient">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      {faqSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
        />
      )}

      <Navigation />
      <div className="pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6">{blog.title}</h1>
          </div>

          {blog.image && (
            <img 
              src={blog.image} 
              alt={blog.title} 
              className="w-full md:w-1/2 mx-auto h-auto object-cover rounded-lg mb-12" 
            />
          )}

          <div className="prose prose-lg max-w-none mx-auto distillery-text">
            <p className="lead">{blog.introduction}</p>
            {blog.sections.map((section, index) => (
              <div key={index} className="mt-8">
                <h2 className="text-2xl font-playfair font-bold text-amber-100">{section.title}</h2>
                <p>{section.content}</p>
              </div>
            ))}
            <p className="mt-8">{blog.conclusion}</p>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
}