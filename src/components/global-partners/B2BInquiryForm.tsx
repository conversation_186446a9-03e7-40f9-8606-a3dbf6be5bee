import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import axios from "axios";
import { toast } from "sonner";

type FormData = {
  companyName: string;
  contactPerson: string;
  email: string;
  country: string;
  expectedAnualVolume: string;
  distributionExp: string;
};

const B2BInquiryForm = () => {
  const [formData, setFormData] = useState<FormData>({
    companyName: "",
    contactPerson: "",
    email: "",
    country: "",
    expectedAnualVolume: "",
    distributionExp: "",
  });
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user types
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    
    if (!formData.companyName.trim()) newErrors.companyName = 'Company name is required';
    if (!formData.contactPerson.trim()) newErrors.contactPerson = 'Contact person is required';
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.country.trim()) newErrors.country = 'Country is required';
    if (!formData.expectedAnualVolume.trim()) newErrors.expectedAnualVolume = 'Expected annual volume is required';
    if (!formData.distributionExp.trim()) newErrors.distributionExp = 'Distribution experience is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/global`,
        formData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
      if (response.status === 200) {
        toast.success("Your inquiry has been sent successfully!");
        // Reset form
        setFormData({
          companyName: "",
          contactPerson: "",
          email: "",
          country: "",
          expectedAnualVolume: "",
          distributionExp: "",
        });
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(
        error.response?.data?.message || 
        'Failed to send message. Please try again later.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <div className="distillery-card p-8">
      <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">B2B Partnership Inquiry</h2>
      <div className="max-w-2xl mx-auto">
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="companyName" className="text-amber-200">Company Name</Label>
              <Input 
                id="companyName" 
                name="companyName"
                type="text" 
                value={formData.companyName}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.companyName ? 'border-red-500' : ''}`} 
                onChange={handleChange} 
              />
              {errors.companyName && <p className="mt-1 text-sm text-red-400">{errors.companyName}</p>}
            </div>
            <div>
              <Label htmlFor="contactPerson" className="text-amber-200">Contact Person</Label>
              <Input 
                id="contactPerson" 
                name="contactPerson"
                type="text" 
                value={formData.contactPerson}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.contactPerson ? 'border-red-500' : ''}`} 
                onChange={handleChange} 
              />
              {errors.contactPerson && <p className="mt-1 text-sm text-red-400">{errors.contactPerson}</p>}
            </div>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="email" className="text-amber-200">Email</Label>
              <Input 
                id="email" 
                name="email"
                type="email" 
                value={formData.email}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.email ? 'border-red-500' : ''}`} 
                onChange={handleChange} 
              />
              {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
            </div>
            <div>
              <Label htmlFor="country" className="text-amber-200">Country/Region</Label>
              <Input 
                id="country" 
                name="country"
                type="text" 
                value={formData.country}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.country ? 'border-red-500' : ''}`} 
                onChange={handleChange} 
              />
              {errors.country && <p className="mt-1 text-sm text-red-400">{errors.country}</p>}
            </div>
          </div>
          <div>
            <Label htmlFor="expectedAnualVolume" className="text-amber-200">Expected Annual Volume</Label>
            <Input 
              id="expectedAnualVolume" 
              name="expectedAnualVolume"
              type="text" 
              value={formData.expectedAnualVolume}
              className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.expectedAnualVolume ? 'border-red-500' : ''}`} 
              onChange={handleChange} 
              placeholder="e.g., 10,000 bottles" 
            />
            {errors.expectedAnualVolume && <p className="mt-1 text-sm text-red-400">{errors.expectedAnualVolume}</p>}
          </div>
          <div>
            <Label htmlFor="distributionExp" className="text-amber-200">Distribution Experience</Label>
            <Textarea 
              id="distributionExp" 
              name="distributionExp"
              rows={4} 
              value={formData.distributionExp}
              className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.distributionExp ? 'border-red-500' : ''}`} 
              onChange={handleChange} 
              placeholder="Tell us about your distribution network and experience with premium spirits..." 
            />
            {errors.distributionExp && <p className="mt-1 text-sm text-red-400">{errors.distributionExp}</p>}
          </div>
          <Button 
            type="submit"
            disabled={isSubmitting}
            className={`w-full premium-button py-3 font-crimson font-semibold ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </>
            ) : 'Submit Partnership Inquiry'}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default B2BInquiryForm;
