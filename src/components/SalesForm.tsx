'use client';

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import axios from "axios";
import { toast } from "sonner";

type FormData = {
  fullName: string;
  businessName: string;
  email: string;
  phone: string;
  businessLocation: string;
  message: string;
};

const SalesForm = () => {
  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    businessName: "",
    email: "",
    phone: "",
    businessLocation: "",
    message: "",
  });
  
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when user types
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    
    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.businessName.trim()) newErrors.businessName = 'Business name is required';
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^[0-9\s\-+()]*$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }
    if (!formData.businessLocation.trim()) newErrors.businessLocation = 'Business location is required';
    if (!formData.message.trim()) newErrors.message = 'Please tell us about your business';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL || 'https://mail.shangriladistillery.com'}/api/local`,
        formData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
      if (response.status === 200) {
        toast.success("Your application has been submitted successfully!");
        // Reset form
        setFormData({
          fullName: "",
          businessName: "",
          email: "",
          phone: "",
          businessLocation: "",
          message: "",
        });
      } else {
        throw new Error('Failed to submit application');
      }
    } catch (error: any) {
      console.error('Error submitting application:', error);
      toast.error(
        error.response?.data?.message || 
        'Failed to submit application. Please try again later.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="distillery-card p-8">
      <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Join Our Network</h2>
      <div className="max-w-2xl mx-auto">
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="fullName" className="text-amber-200">Full Name</Label>
              <Input 
                id="fullName" 
                name="fullName"
                type="text" 
                value={formData.fullName}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.fullName ? 'border-red-500' : ''}`} 
                onChange={handleChange}
              />
              {errors.fullName && <p className="mt-1 text-sm text-red-400">{errors.fullName}</p>}
            </div>
            <div>
              <Label htmlFor="businessName" className="text-amber-200">Business Name</Label>
              <Input 
                id="businessName" 
                name="businessName"
                type="text" 
                value={formData.businessName}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.businessName ? 'border-red-500' : ''}`} 
                onChange={handleChange}
              />
              {errors.businessName && <p className="mt-1 text-sm text-red-400">{errors.businessName}</p>}
            </div>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="email" className="text-amber-200">Email</Label>
              <Input 
                id="email" 
                name="email"
                type="email" 
                value={formData.email}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.email ? 'border-red-500' : ''}`} 
                onChange={handleChange}
              />
              {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
            </div>
            <div>
              <Label htmlFor="phone" className="text-amber-200">Phone</Label>
              <Input 
                id="phone" 
                name="phone"
                type="tel" 
                value={formData.phone}
                className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.phone ? 'border-red-500' : ''}`} 
                onChange={handleChange}
              />
              {errors.phone && <p className="mt-1 text-sm text-red-400">{errors.phone}</p>}
            </div>
          </div>
          <div>
            <Label htmlFor="businessLocation" className="text-amber-200">Business Location</Label>
            <Input 
              id="businessLocation" 
              name="businessLocation"
              type="text" 
              value={formData.businessLocation}
              className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.businessLocation ? 'border-red-500' : ''}`} 
              onChange={handleChange}
            />
            {errors.businessLocation && <p className="mt-1 text-sm text-red-400">{errors.businessLocation}</p>}
          </div>
          <div>
            <Label htmlFor="message" className="text-amber-200">Tell us about your business</Label>
            <Textarea 
              id="message" 
              name="message"
              rows={4} 
              value={formData.message}
              className={`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${errors.message ? 'border-red-500' : ''}`} 
              onChange={handleChange}
            />
            {errors.message && <p className="mt-1 text-sm text-red-400">{errors.message}</p>}
          </div>
          <Button 
            type="submit"
            disabled={isSubmitting}
            className={`w-full premium-button py-3 font-crimson font-semibold text-lg ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Submitting...
              </>
            ) : 'Apply to Become a Dealer'}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default SalesForm;