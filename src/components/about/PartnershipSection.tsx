
import { Handshake, ExternalLink } from "lucide-react";

const PartnershipSection = () => {
  return (
    <div className="bg-gradient-to-r from-amber-900/50 to-stone-900/50 rounded-lg p-8 border border-amber-500/30 backdrop-blur-sm">
      <div className="flex items-center justify-center mb-6">
        <Handshake className="h-8 w-8 text-amber-400 mr-3" />
        <h2 className="text-3xl font-playfair font-bold text-amber-100">Strategic Partnership</h2>
      </div>
      <div className="text-center max-w-4xl mx-auto">
        <div className="flex items-center justify-center mb-4">
          <h3 className="text-2xl font-crimson font-semibold text-amber-300 mr-3">Trade Vision Partners, Australia</h3>
          <a 
            href="https://tradevisionpartners.com/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-amber-400 hover:text-amber-300 transition-colors"
          >
            <ExternalLink className="h-6 w-6" />
          </a>
        </div>
        <p className="text-lg distillery-text leading-relaxed mb-6">
          Our strategic partnership with Trade Vision Partners represents a powerful alliance that 
          combines our premium product development capabilities, quality focus, and blending expertise 
          with their extensive international distribution network and market knowledge.
        </p>
        <p className="text-lg distillery-text leading-relaxed">
          This collaboration enables us to bring authentic Nepalese spirits to discerning consumers 
          in both domestic and international markets while maintaining the highest standards of 
          quality, innovation, and authenticity that define the Shangrila brand.
        </p>
      </div>
    </div>
  );
};

export default PartnershipSection;
