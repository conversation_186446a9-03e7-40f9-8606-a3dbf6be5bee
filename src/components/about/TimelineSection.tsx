
import { Calendar } from "lucide-react";

const TimelineSection = () => {
  const timeline = [
    {
      year: "2023",
      title: "Foundation",
      description: "Our group acquired the company and started building our facilities"
    },
    {
      year: "2024",
      title: "Partnership",
      description: "Strategic alliance with Trade Vision Partners, Australia"
    },
    {
      year: "2025",
      title: "Launch",
      description: "First product line launch and domestic market entry"
    },
    {
      year: "2026",
      title: "Expansion",
      description: "International market expansion and capacity increase"
    }
  ];

  return (
    <div className="mb-20">
      <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-12 text-center">Our Journey</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        {timeline.map((item) => (
          <div key={item.year} className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
            <div className="w-12 h-12 bg-amber-400 rounded-full flex items-center justify-center mx-auto mb-4">
              <Calendar className="h-6 w-6 text-stone-900" />
            </div>
            <div className="text-2xl font-playfair font-bold text-amber-400 mb-2">{item.year}</div>
            <h3 className="text-lg font-crimson font-semibold text-amber-100 mb-2">{item.title}</h3>
            <p className="distillery-text text-sm">{item.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TimelineSection;
