
const FacilityImageGallery = () => {
  return (
    <div className="mb-20">
      <div className="grid md:grid-cols-2 gap-8 mb-12">
        <div className="relative overflow-hidden rounded-lg shadow-2xl">
          <img 
            src="/lovable-uploads/d30150e7-f7de-46a2-bee7-4d4d9a82caff.png" 
            alt="Shangrila Distillery Facility - Mountain View" 
            className="w-full h-80 object-cover hover:scale-105 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"></div>
          <div className="absolute bottom-4 left-4 text-amber-100">
            <h3 className="text-xl font-playfair font-bold">Himalayan Setting</h3>
            <p className="text-amber-100/80">Nestled in the pristine mountains of Nepal</p>
          </div>
        </div>
        <div className="relative overflow-hidden rounded-lg shadow-2xl">
          <img 
            src="/lovable-uploads/25cb54a2-cb09-4306-91bf-d1d6cbf98ec7.png" 
            alt="Shangrila Distillery Main Building" 
            className="w-full h-80 object-cover hover:scale-105 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"></div>
          <div className="absolute bottom-4 left-4 text-amber-100">
            <h3 className="text-xl font-playfair font-bold">Modern Architecture</h3>
            <p className="text-amber-100/80">Contemporary design meets traditional craftsmanship</p>
          </div>
        </div>
      </div>
      
      <div className="grid md:grid-cols-2 gap-8">
        <div className="relative overflow-hidden rounded-lg shadow-2xl">
          <img 
            src="/lovable-uploads/3f437121-b350-4cd1-8796-babca2f34cb6.png" 
            alt="Shangrila Distillery Production Wing" 
            className="w-full h-80 object-cover hover:scale-105 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"></div>
          <div className="absolute bottom-4 left-4 text-amber-100">
            <h3 className="text-xl font-playfair font-bold">Production Facility</h3>
            <p className="text-amber-100/80">Advanced manufacturing capabilities</p>
          </div>
        </div>
        <div className="relative overflow-hidden rounded-lg shadow-2xl">
          <img 
            src="/lovable-uploads/7a56bbca-5184-47f8-bb85-623b34c87dde.png" 
            alt="Shangrila Distillery Infrastructure" 
            className="w-full h-80 object-cover hover:scale-105 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"></div>
          <div className="absolute bottom-4 left-4 text-amber-100">
            <h3 className="text-xl font-playfair font-bold">Infrastructure Excellence</h3>
            <p className="text-amber-100/80">Built for scalability and efficiency</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FacilityImageGallery;
