
import { Factory, BarChart3, Shield, TrendingUp, Cog, Users, MapPin, Clock } from "lucide-react";

const FacilitySpecifications = () => {
  const facilities = [
    {
      title: "Blending Tanks",
      description: "State-of-the-art stainless steel tanks for precise blending and maturation",
      icon: Factory
    },
    {
      title: "Bottling Lines", 
      description: "Automated bottling systems ensuring consistency and quality control",
      icon: BarChart3
    },
    {
      title: "Storage Facilities",
      description: "Climate-controlled warehouses for optimal aging and inventory management",
      icon: Shield
    },
    {
      title: "Quality Lab",
      description: "Advanced testing laboratory ensuring every batch meets our standards",
      icon: TrendingUp
    }
  ];

  const specifications = [
    {
      icon: Cog,
      title: "Production Technology",
      details: ["Automated control systems", "Temperature monitoring", "Precision measurement"]
    },
    {
      icon: Users,
      title: "Expert Team",
      details: ["Master distillers", "Quality assurance", "Production specialists"]
    },
    {
      icon: MapPin,
      title: "Strategic Location",
      details: ["Himalayan water source", "Optimal climate", "Transportation access"]
    },
    {
      icon: Clock,
      title: "Continuous Operation",
      details: ["24/7 monitoring", "Shift operations", "Quality control"]
    }
  ];

  return (
    <>
      {/* Infrastructure Overview */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
        {facilities.map((facility) => (
          <div key={facility.title} className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
            <div className="w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <facility.icon className="h-8 w-8 text-amber-400" />
            </div>
            <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">{facility.title}</h3>
            <p className="distillery-text">{facility.description}</p>
          </div>
        ))}
      </div>

      {/* Detailed Specifications */}
      <div className="mb-20">
        <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-12 text-center">Facility Specifications</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {specifications.map((spec) => (
            <div key={spec.title} className="distillery-card p-6 hover:scale-105 transition-all duration-300">
              <div className="w-12 h-12 bg-amber-400/20 rounded-full flex items-center justify-center mb-4">
                <spec.icon className="h-6 w-6 text-amber-400" />
              </div>
              <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-3">{spec.title}</h3>
              <ul className="space-y-1">
                {spec.details.map((detail) => (
                  <li key={detail} className="distillery-text text-sm">• {detail}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default FacilitySpecifications;
