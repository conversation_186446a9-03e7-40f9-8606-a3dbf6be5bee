
import { Award, ArrowRight, Facebook, Instagram } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Product {
  name: string;
  description: string;
  category: string;
  type: string;
  alcohol: string;
  notes: string[];
  bgColor: string;
  textColor: string;
  image: string;
}

interface ProductCardProps {
  product: Product;
  index: number;
}

const ProductCard = ({ product, index }: ProductCardProps) => {
  return (
    <div className={`relative overflow-hidden rounded-3xl bg-gradient-to-r ${product.bgColor} min-h-[600px] shadow-2xl`}>
      <div className="absolute inset-0 bg-black/30"></div>
      <div className="relative z-10 grid lg:grid-cols-2 gap-12 items-center p-12 lg:p-16">
        <div className={index % 2 === 0 ? "order-1" : "order-2"}>
          <div className="mb-6">
            <span className={`text-lg font-crimson font-semibold ${product.textColor}/80`}>
              CRAFTED FROM HERITAGE
            </span>
          </div>
          <h2 className={`text-5xl md:text-5xl font-playfair font-bold ${product.textColor} mb-8 leading-tight`}>
            Shangrila <span className="italic text-amber-400">Collection</span>
          </h2>
          <h3 className={`text-3xl font-playfair font-bold ${product.textColor} mb-6`}>
            {product.name}
          </h3>
          <div className={`text-xl font-crimson ${product.textColor}/90 mb-2`}>
            {product.type} • {product.alcohol} ABV
          </div>
          <p className={`text-lg font-crimson ${product.textColor}/80 leading-relaxed mb-8 max-w-2xl`}>
            {product.description}
          </p>
          
          {/* Tasting Notes */}
          <div className="mb-8">
            <h4 className={`text-lg font-playfair font-semibold ${product.textColor} mb-3`}>Tasting Notes</h4>
            <div className="flex flex-wrap gap-2">
              {product.notes.map((note) => (
                <span key={note} className={`px-4 py-2 bg-white/20 backdrop-blur-sm ${product.textColor} rounded-full text-sm border border-white/30 font-crimson`}>
                  {note}
                </span>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <Button 
              variant="outline" 
              className={`${product.textColor} border-white/50 hover:bg-white/20 px-6 py-3 font-crimson font-semibold`}
            >
              Explore Legacy <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            
            <div className="flex space-x-3">
              <Button 
                size="icon" 
                variant="outline" 
                className={`${product.textColor} border-white/50 hover:bg-white/20 rounded-full`}
              >
                <Facebook className="h-4 w-4" />
              </Button>
              <Button 
                size="icon" 
                variant="outline" 
                className={`${product.textColor} border-white/50 hover:bg-white/20 rounded-full`}
              >
                <Instagram className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        
        <div className={index % 2 === 0 ? "order-2" : "order-1"}>
          <div className="relative">
            <div className="w-80 h-96 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto border border-white/20 p-8">
              <img 
                src={product.image} 
                alt={`Shangrila Distillery ${product.name}`} 
                className="max-w-full max-h-full object-contain filter drop-shadow-2xl"
              />
            </div>
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-amber-400 rounded-full opacity-60"></div>
            <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-amber-400 rounded-full opacity-40"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
