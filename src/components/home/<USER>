import Link from "next/link";
import { Award, Clock, Users, Gem } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const HeritageBanner = () => {
  return (
    <div className="aged-paper py-20 relative overflow-hidden">
      <div className="absolute inset-0 bg-distillery-texture opacity-30"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          <div className="mb-8">
            <span className="text-lg font-semibold text-amber-900 font-baskerville italic">Heritage & Craftsmanship</span>
          </div>
          <h2 className="text-5xl md:text-7xl font-playfair font-bold text-stone-900 mb-8 leading-tight">
            Crafting<br />
            <span className="text-amber-700 relative">
              Legendary
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-amber-600 to-amber-400 rounded-full"></div>
            </span><br />
            Experiences
          </h2>
          <p className="text-xl text-stone-700 leading-relaxed mb-12 font-crimson max-w-4xl mx-auto">
           At Shangrila Distillery, we honour the noble art of blending — where time, technique, and tradition converge.
 Each bottle is a testament to our unwavering dedication, carrying the legacy of master blenders through generations.
 From hand-selected malts to precise blending methods, we craft spirits that are rich in character and timeless in quality.
          </p>
          
          {/* Heritage Stats */}
          <div className="flex flex-col md:flex-row justify-center items-center gap-8 mb-12 max-w-2xl mx-auto">
            <div className="text-center group w-full md:w-1/2">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                <Award className="h-8 w-8 text-amber-800" />
              </div>
              <h3 className="text-3xl font-playfair font-bold text-amber-800 mb-2">Master</h3>
              <p className="text-stone-600 font-crimson">blender craft </p>
            </div>
            <div className="text-center group w-full md:w-1/2">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto">
                <Users className="h-8 w-8 text-amber-800" />
              </div>
              <h3 className="text-3xl font-playfair font-bold text-amber-800 mb-2">Legacy</h3>
              <p className="text-stone-600 font-crimson">Generations Deep</p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/products">
              <Button className="bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 text-amber-50 px-8 py-4 text-lg font-baskerville shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <Gem className="mr-2 h-5 w-5" />
                Explore Our Heritage Collection
              </Button>
            </Link>
            <Link href="/about">
              <Button className="bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 text-amber-50 border-2 border-amber-700 px-8 py-4 text-lg font-baskerville shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                Discover Our Story
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeritageBanner;