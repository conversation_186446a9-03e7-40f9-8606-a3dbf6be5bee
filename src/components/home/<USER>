import Link from "next/link";
import { Phone, Mail, MapPin, Facebook, Twitter, Instagram, Linkedin } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-stone-950 border-t border-amber-400/30 relative overflow-hidden">
      <div className="absolute inset-0 bg-distillery-texture opacity-10"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <img 
              src="/lovable-uploads/favicon-shangrila.png" 
              alt="Shangrila Distillery" 
              className="h-16 w-16 mb-6 rounded-full object-cover filter brightness-110"
            />
            <h4 className="text-xl font-playfair font-bold text-amber-100 mb-4">About Shangrila Distillery</h4>
            <p className="text-amber-100/70 mb-6 leading-relaxed font-crimson">
              Shangrila Distillery is Nepal's premier producer of fine craft spirits. Founded in the heart of the Himalayas, Shangrila Distillery blends tradition and innovation to deliver exceptional gin, vodka, and whiskey to spirit lovers worldwide.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-amber-900/50 rounded-full flex items-center justify-center hover:bg-amber-600 transition-colors backdrop-blur-sm border border-amber-500/30">
                <Facebook className="h-5 w-5 text-amber-300 hover:text-amber-950" />
              </a>
              <a href="#" className="w-10 h-10 bg-amber-900/50 rounded-full flex items-center justify-center hover:bg-amber-600 transition-colors backdrop-blur-sm border border-amber-500/30">
                <Twitter className="h-5 w-5 text-amber-300 hover:text-amber-950" />
              </a>
              <a href="https://www.instagram.com/shangriladistillery" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-amber-900/50 rounded-full flex items-center justify-center hover:bg-amber-600 transition-colors backdrop-blur-sm border border-amber-500/30">
                <Instagram className="h-5 w-5 text-amber-300 hover:text-amber-950" />
              </a>
              <a href="https://www.linkedin.com/company/shangrila-distillery/?viewAsMember=true" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-amber-900/50 rounded-full flex items-center justify-center hover:bg-amber-600 transition-colors backdrop-blur-sm border border-amber-500/30">
                <Linkedin className="h-5 w-5 text-amber-300 hover:text-amber-950" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-playfair font-bold text-amber-100 mb-6">Heritage Links</h4>
            <ul className="space-y-3">
              <li><Link href="/about" className="text-amber-100/70 hover:text-amber-300 transition-colors font-crimson">Our Story</Link></li>
              <li><Link href="/products" className="text-amber-100/70 hover:text-amber-300 transition-colors font-crimson">Premium Collection</Link></li>
              <li><Link href="/facility" className="text-amber-100/70 hover:text-amber-300 transition-colors font-crimson">Master Distillery</Link></li>
              <li><Link href="/events" className="text-amber-100/70 hover:text-amber-300 transition-colors font-crimson">Heritage Events</Link></li>
              <li><Link href="/exports" className="text-amber-100/70 hover:text-amber-300 transition-colors font-crimson">Global Heritage</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-playfair font-bold text-amber-100 mb-6">Contact Us</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-amber-400 mt-1" />
                <div>
                  <p className="text-amber-100/70 font-crimson">Brahmanagar, Rapti Nagarpalika-9, Chitwan, Nepal</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-amber-400" />
                <p className="text-amber-100/70 font-crimson">+977 1-4528118</p>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-amber-400" />
                <p className="text-amber-100/70 font-crimson"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-amber-500/30 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-amber-100/60 text-sm font-crimson">
              © 2025 Shangrila Distillery. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy-policy" className="text-amber-100/60 hover:text-amber-300 text-sm transition-colors font-crimson">Privacy Policy</Link>
              <Link href="/terms-of-service" className="text-amber-100/60 hover:text-amber-300 text-sm transition-colors font-crimson">Terms of Service</Link>
              <Link href="/responsible-drinking" className="text-amber-100/60 hover:text-amber-300 text-sm transition-colors font-crimson">Responsible Drinking</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;