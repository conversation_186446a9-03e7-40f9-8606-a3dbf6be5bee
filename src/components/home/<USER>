import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Handshake, ExternalLink } from "lucide-react";
import Link from "next/link";

const partners = [
  {
    name: "Trade Vision Partners",
    location: "Australia",
    description:
      "Our flagship partnership brings decades of international distribution expertise, opening doors to premium markets across Australia and beyond. Together, we're setting new standards for Nepalese spirits on the global stage.",
    link: "https://tradevisionpartners.com/",
  },
  {
    name: "ShyamBaba Group",
    location: "Nepal",
    description:
      "Nepal's foremost diversified business group with interests in Food Products, Cement, Construction, Manufacturing and Trading. With over 2000 employees and 10,000 direct customers, ShyamBaba Group touches lives across Nepal through its vast offering of market-leading high quality consumer products.",
    link: "https://sbgcompanies.com/",
  },
];

const ExportPartner = () => {
  return (
    <div className="bg-stone-900 py-20 sm:py-28">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl sm:text-5xl font-playfair font-bold text-amber-100">
            Our Export Partners
          </h2>
          <p className="mt-4 text-lg text-amber-100/70 font-crimson">
            Collaborating with the best to bring our spirits to the world.
          </p>
        </div>
        <div className="grid md:grid-cols-2 gap-10">
          {partners.map((partner) => (
            <div
              key={partner.name}
              className="distillery-card p-8 text-center"
            >
              <Handshake className="h-16 w-16 mx-auto mb-6 text-amber-400 animate-float" />
              <div className="flex items-center justify-center mb-4">
                <h3 className="text-2xl font-playfair font-bold text-amber-100 mr-3">
                  {partner.name}
                </h3>
                <a 
                  href={partner.link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-amber-400 hover:text-amber-300 transition-colors"
                >
                  <ExternalLink className="h-6 w-6" />
                </a>
              </div>
              <p className="text-amber-300 mb-4 font-crimson">
                {partner.location}
              </p>
              <p className="text-lg distillery-text">
                {partner.description}
              </p>
            </div>
          ))}
        </div>
        <div className="mt-16 text-center">
          <Link href="/exports">
            <Button
              variant="outline"
              className="border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-8 py-4 text-lg font-baskerville backdrop-blur-sm"
            >
              <span>Become a Partner</span>
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ExportPartner;