import Link from "next/link";
import { Droplets, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";

const CallToAction = () => {
  return (
    <div className="bg-gradient-to-br from-stone-700 via-amber-800 to-stone-700 py-20 relative overflow-hidden">
      <div className="absolute inset-0 bg-distillery-texture opacity-20"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className="max-w-3xl mx-auto">
          <span className="text-amber-300 font-baskerville italic text-lg tracking-wide mb-4 block">Experience the Legend</span>
          <h3 className="text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6">Taste the Himalayan Heritage</h3>
          <p className="text-xl text-amber-100/80 mb-8 font-crimson leading-relaxed">
            Join an exclusive circle of connoisseurs who appreciate the finest spirits. Discover why our heritage continues to captivate palates across the globe.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link href="/products">
              <Button className="premium-button px-10 py-4 text-lg font-baskerville tracking-wide">
                <Droplets className="mr-2 h-5 w-5" />
                Explore Our Spirits
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" className="border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-10 py-4 text-lg font-baskerville backdrop-blur-sm">
                <Globe className="mr-2 h-5 w-5" />
                Become a Partner
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallToAction;