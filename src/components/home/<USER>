import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Star, Linkedin, Facebook } from "lucide-react";
import { Button } from "@/components/ui/button";

const FlagshipCollection = () => {
  const flagshipBrands = [
    {
      name: "Tejas Gold",
      description: "A distinguished blend of the finest aged malts, crafted with time-honored traditions",
      category: "Premium Whiskey",
      image: "/lovable-uploads/tejas gold.jpeg"
    },
    {
      name: "Tejas Black",
      description: "Bold and smoky, with the depth of character that defines true craftsmanship",
      category: "Peated Whiskey", 
      image: "/lovable-uploads/tejas black.png", 
    },
    {
      name: "Reef Vodka",
      description: "Crystal pure vodka, distilled to perfection from the finest mountain spring water",
      category: "Premium Vodka",
      image: "/lovable-uploads/reef.png",
    },
    {
      name: "LOD Vodka",
      description: "Ultra-premium vodka representing the pinnacle of our distilling excellence",
      category: "Luxury Vodka",
      image: "/lovable-uploads/lod.png",
    },
    {
      name: "<PERSON>",
      description: "A bold new expression in our whiskey lineup, Phantom delivers a smooth yet powerful character crafted for the domestic connoisseur.",
      category: "40UP Whiskey",
      image: "/lovable-uploads/phantom.jpeg",
    },
    {
      name: "Royal Distinction",
      description: "A regal blend that offers a smooth, rich taste, fit for connoisseurs seeking a truly royal experience.",
      category: "Premium Whiskey",
      image: "/lovable-uploads/royal.png",
    },
    {
      name: "0 Degree",
      description: "A crisp and clean spirit, perfect for cocktails or sipping neat, offering a refreshing and pure taste.",
      category: "Vodka",
      image: "/lovable-uploads/vodka.png",
    },
    {
      name: "Lynx",
      description: "A smooth and sophisticated vodka, crafted to deliver a premium experience with every sip.",
      category: "Premium Vodka",
      image: "/lovable-uploads/lynxvodka.png",
    }
  ];

  return (
    <div className="py-20 bg-gradient-to-br from-stone-800 via-amber-900/30 to-stone-900 relative overflow-hidden">
      <div className="absolute inset-0 bg-distillery-texture opacity-10"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-16">
          <span className="text-amber-400 font-baskerville italic text-lg tracking-wide mb-4 block">Our Distinguished Collection</span>
          <h3 className="text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6">Premium Heritage Spirits</h3>
          <p className="text-xl text-amber-100/70 max-w-3xl mx-auto font-crimson leading-relaxed">
            Each bottle in our collection represents decades of refinement, carrying the soul of the Himalayas and the expertise of our master distillers.
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {flagshipBrands.map((brand) => (
            <div key={brand.name} className="group distillery-card p-6 text-center hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-amber-500/20">
              <div className="relative mb-6 overflow-hidden rounded-lg">
                <img 
                  src={brand.image} 
                  alt={brand.name}
                  className="w-32 h-48 object-contain mx-auto shadow-md group-hover:scale-110 transition-transform duration-500 filter sepia-[0.2]"
                />
                <div className="absolute -top-2 -right-2 bg-amber-500 text-amber-950 p-2 rounded-full">
                  <Star className="h-4 w-4" />
                </div>
              </div>
              <h4 className="text-2xl font-playfair font-bold text-amber-100 mb-2">{brand.name}</h4>
              <p className="text-sm font-semibold text-amber-400 mb-3 uppercase tracking-wide font-baskerville">{brand.category}</p>
              <p className="text-amber-100/70 leading-relaxed font-crimson text-sm">{brand.description}</p>
              <div className="mt-6 pt-4 border-t border-amber-500/30">
                <Link href="/contact">
                  <Button variant="ghost" className="text-amber-400 hover:text-amber-300 font-baskerville font-semibold group-hover:bg-amber-500/10 mb-4">
                    Visit Us
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                
                <div className="flex space-x-3 justify-center">
                  <a 
                    href="https://www.facebook.com/profile.php?id=61577339984580&mibextid=wwXIfr&rdid=NeM8ITYNkZt9W2LF&share_url=https%3A%2F%2Fwww.facebook.com%2Fshare%2F18zeJbDG6E%2F%3Fmibextid%3DwwXIfr"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button 
                      size="icon" 
                      variant="outline" 
                      className="text-amber-400 border-amber-500/50 hover:bg-amber-500/20 hover:border-amber-300 rounded-full"
                    >
                      <Facebook className="h-4 w-4" />
                    </Button>
                  </a>
                  <a 
                    href="https://www.linkedin.com/company/shangrila-distillery/"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button 
                      size="icon" 
                      variant="outline" 
                      className="text-amber-400 border-amber-500/50 hover:bg-amber-500/20 hover:border-amber-300 rounded-full"
                    >
                      <Linkedin className="h-4 w-4" />
                    </Button>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-4 bg-amber-900/30 backdrop-blur-sm border border-amber-500/30 rounded-full px-6 py-3">
            <span className="text-amber-200 font-crimson">Connect with us:</span>
            <a 
              href="https://www.linkedin.com/company/shangrila-distillery/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 text-amber-300 hover:text-amber-100 transition-colors"
            >
              <Linkedin className="h-5 w-5" />
              <span className="font-baskerville font-semibold">Shangrila Distillery</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlagshipCollection;