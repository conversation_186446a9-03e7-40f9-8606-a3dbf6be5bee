import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const HeroSection = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20">
      <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
        <div>
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-playfair font-bold text-amber-100 mb-4 sm:mb-6 leading-tight">
           Welcome to Shangrila Distillery – Nepal's Premium Craft Spirits
            <span className="block bg-gradient-to-r from-amber-400 via-amber-300 to-amber-500 bg-clip-text text-transparent">Blending Excellence.</span>
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-amber-100/80 mb-6 sm:mb-8 leading-relaxed font-crimson">
        From the heart of Nepal, we source the finest malts and botanicals, expertly blending them in our state-of-the-art facility to craft spirits of unmatched character and clarity.
 Where tradition meets technique, every blend tells a story of purity, precision, and passion.
Crafted in Nepal. Savoured Around the World.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <Link href="/products">
              <Button className="premium-button px-8 py-4 text-lg font-baskerville tracking-wide">
                <Crown className="mr-2 h-5 w-5" />
                Discover Our Collection
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" className="hidden sm:inline-flex border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-8 py-4 text-lg font-baskerville backdrop-blur-sm">
                Visit Our Heritage
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
        
        <div className="relative flex flex-col md:flex-row items-center justify-center gap-4 md:gap-6 min-h-[150px] sm:min-h-[200px]">
          <img
            src="/lovable-uploads/fontlogo.jpeg"
            alt="Shangrila Distillery Logo"
            className="h-48 w-auto sm:h-64 md:h-80 lg:h-[28rem] max-w-[90vw] sm:max-w-none rounded-lg shadow-2xl border-2 border-amber-400/30 bg-white/10 backdrop-blur-sm object-cover"
          />
          {/* Decorative elements */}
          <div className="absolute -top-4 -right-4 w-8 h-8 bg-amber-400 rounded-full opacity-60 animate-pulse"></div>
          <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-amber-400 rounded-full opacity-40 animate-pulse delay-1000"></div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;