import { useState } from "react";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi } from "@/components/ui/carousel";
import { products } from "@/data/products";
import { useAutoCarousel } from "@/hooks/useAutoCarousel";

const ProductCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();
  
  // Auto-scroll every 3 seconds
  useAutoCarousel(api, 3000);

  return (
    <div className="py-10 md:py-16 bg-transparent">
      <div className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 relative z-10">
        <Carousel 
          setApi={setApi}
          className="w-full"
          opts={{
            align: "center",
            loop: true,
          }}
        >
          <CarouselContent className="-ml-1 md:-ml-4">
            {products.map((product) => (
              <CarouselItem key={product.name} className="pl-1 md:pl-4 basis-2/3 sm:basis-1/2 md:basis-1/4 lg:basis-1/5">
                <div className="relative group">
                  <div className="w-40 h-56 md:w-48 md:h-64 bg-white/5 rounded-xl flex items-center justify-center border border-white/10 p-3 md:p-6 hover:bg-white/10 transition-all duration-300">
                    <img 
                      src={product.image} 
                      alt={product.name} 
                      className="max-w-full max-h-full object-contain filter drop-shadow-xl group-hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex -left-16 bg-amber-900/50 border-amber-400/50 text-amber-300 hover:bg-amber-600" />
          <CarouselNext className="hidden md:flex -right-16 bg-amber-900/50 border-amber-400/50 text-amber-300 hover:bg-amber-600" />
        </Carousel>
      </div>
    </div>
  );
};

export default ProductCarousel;
