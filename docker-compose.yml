version: "3.8"
services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      args:
        VITE_API_URL: https://mail.shangriladistillery.com
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - tradevision-network

  backend:
    build:
      context: .
      dockerfile: Dockerfile.server
    ports:
      - "8000:8000"
    env_file:
      - server/.env
    networks:
      - tradevision-network

networks:
  tradevision-network:
    driver: bridge
