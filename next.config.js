/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['shangriladistillery.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'shangriladistillery.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: '/global-partners',
        destination: '/exports',
        permanent: true,
      },
    ]
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ]
  },
}

module.exports = nextConfig