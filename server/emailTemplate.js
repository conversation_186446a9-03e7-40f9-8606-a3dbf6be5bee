function contactEmailTemplate({ firstName, lastName, email, phone, subject, inquiryType, message }) {
    return `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${firstName} ${lastName}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Phone:</strong> ${phone}</p>
      <p><strong>Subject:</strong> ${subject}</p>
      <p><strong>Inquiry Type:</strong> ${inquiryType}</p>
      <p><strong>Message:</strong><br/>${message}</p>
    `;
  }
  
  function confirmationEmailTemplate({ firstName, lastName }) {
    return `
      <h2>Thank You for Contacting Us!</h2>
      <p>Dear ${firstName} ${lastName},</p>
      <p>We have received your message and will get back to you soon.</p>
      <p>Best regards,<br/>The Trade Vision Team</p>
    `;
  }
  
  function globalEmailTemplate({ companyName, contactPerson, email, country, expectedAnualVolume, distributionExp }) {
    return `
      <h2>New Partner Form Submission</h2>
      <p><strong>Company Name:</strong> ${companyName}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Contact Person:</strong> ${contactPerson}</p>
      <p><strong>Country:</strong> ${country}</p>
      <p><strong>Expected Annual Volume:</strong> ${expectedAnualVolume}</p>
      <p><strong>Distribution Experience:</strong> ${distributionExp}</p>
    `;
  }
  
  function confirmationGlobalEmailTemplate({ companyName }) {
    return `
      <h2>Thank You for Partnering with Trade Vision!</h2>
      <p>Dear ${companyName},</p>
      <p>We have received your message and will get back to you soon.</p>
      <p>Best regards,<br/>The Trade Vision Team</p>
    `;
  }
  
  function localEmailTemplate({ fullName, businessName, phone, email, businessLocation, message }) {
    return `
      <h2>New Product Inquiry Form Submission</h2>
      <p><strong>Name:</strong> ${fullName}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Phone:</strong> ${phone}</p>
      <p><strong>Business Name:</strong> ${businessName}</p>
      <p><strong>Business Location:</strong> ${businessLocation}</p>
      <p><strong>Message:</strong><br/>${message}</p>
    `;
  }
  
  function confirmationLocalEmailTemplate({ fullName }) {
    return `
      <h2>Thank You for Partnering with Trade Vision!</h2>
      <p>Dear ${fullName},</p>
      <p>We have received your message and will get back to you soon.</p>
      <p>Best regards,<br/>The Trade Vision Team</p>
    `;
  }
  
  export {
    contactEmailTemplate,
    confirmationEmailTemplate,
    globalEmailTemplate,
    confirmationGlobalEmailTemplate,
    localEmailTemplate,
    confirmationLocalEmailTemplate,
  };