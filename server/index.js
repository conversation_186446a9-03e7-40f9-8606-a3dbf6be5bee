import express from "express";
import bodyParser from "body-parser";
import cors from "cors";
import dotenv from "dotenv";
import nodemailer from "nodemailer";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
import {
  contactEmailTemplate,
  confirmationEmailTemplate,
  globalEmailTemplate,
  confirmationGlobalEmailTemplate,
  localEmailTemplate,
  confirmationLocalEmailTemplate,
} from "./emailTemplate.js";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
app.use(bodyParser.json());
app.use(cors());

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, '../dist')));

console.log("SMTP Config:", {
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  user: process.env.EMAIL_USER,
});

const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  debug: true,
  logger: true,
});

// SEO Meta Tags Configuration
const seoConfig = {
  '/leadership/prajanna-raj-adhikari': {
    title: 'Prajanna Raj Adhikari – Managing Director at Shangrila Distillery',
    description: 'Meet Prajanna Raj Adhikari, Managing Director of Shangrila Distillery, Nepal\'s premier spirits producer. Learn about his leadership, vision, and contribution to Nepal\'s finest whiskey and vodka.',
    keywords: 'Prajanna Raj Adhikari, Shangrila Distillery, Nepal Whiskey, Nepal Vodka, Managing Director, Leadership, Premium Spirits, Nepal Distillery, Craft Spirits',
    ogTitle: 'Prajanna Raj Adhikari – Managing Director at Shangrila Distillery',
    ogDescription: 'Discover the leadership of Prajanna Raj Adhikari at Shangrila Distillery, shaping Nepal\'s finest whiskey and vodka with global expertise and vision.',
    ogUrl: 'https://shangriladistillery.com/leadership/prajanna-raj-adhikari',
    ogImage: 'https://shangriladistillery.com/lovable-uploads/prashanna.png',
    canonical: 'https://shangriladistillery.com/leadership/prajanna-raj-adhikari',
    linkedIn: 'https://www.linkedin.com/in/prajannarajadhikari'
  },
  '/our-leadership': {
    title: 'Our Leadership Team - Shangrila Distillery',
    description: 'Meet the visionary leadership team behind Shangrila Distillery, Nepal\'s premier craft spirits producer. Discover the expertise driving our premium whiskey and vodka production.',
    keywords: 'Shangrila Distillery Leadership, Nepal Distillery Team, Premium Spirits Leadership, Craft Distillery Management',
    ogTitle: 'Our Leadership Team - Shangrila Distillery',
    ogDescription: 'Meet the visionary leadership team behind Shangrila Distillery, Nepal\'s premier craft spirits producer.',
    ogUrl: 'https://shangriladistillery.com/our-leadership',
    ogImage: 'https://shangriladistillery.com/lovable-uploads/prashanna.png',
    canonical: 'https://shangriladistillery.com/our-leadership'
  }
};

// Function to inject SEO meta tags into HTML
function injectSEOTags(html, route) {
  const config = seoConfig[route];
  if (!config) return html;

  console.log(`Injecting SEO tags for route: ${route}`);

  let modifiedHtml = html;

  // Replace title
  modifiedHtml = modifiedHtml.replace(
    /<title>.*?<\/title>/s,
    `<title>${config.title}</title>`
  );

  // Replace description - more flexible pattern
  modifiedHtml = modifiedHtml.replace(
    /<meta name="description" content="[^"]*" \/>/g,
    `<meta name="description" content="${config.description}" />`
  );

  // If the above doesn't work, try without the self-closing slash
  modifiedHtml = modifiedHtml.replace(
    /<meta name="description" content="[^"]*">/g,
    `<meta name="description" content="${config.description}">`
  );

  // Replace author and add keywords + canonical after it
  modifiedHtml = modifiedHtml.replace(
    /<meta name="author" content="[^"]*" \/>/g,
    `<meta name="author" content="Shangrila Distillery" />
    <meta name="keywords" content="${config.keywords}" />
    <link rel="canonical" href="${config.canonical}" />`
  );

  // If the above doesn't work, try without the self-closing slash
  modifiedHtml = modifiedHtml.replace(
    /<meta name="author" content="[^"]*">/g,
    `<meta name="author" content="Shangrila Distillery">
    <meta name="keywords" content="${config.keywords}">
    <link rel="canonical" href="${config.canonical}">`
  );

  // Replace Open Graph tags
  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:title" content="[^"]*" \/>/g,
    `<meta property="og:title" content="${config.ogTitle}" />`
  );

  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:title" content="[^"]*">/g,
    `<meta property="og:title" content="${config.ogTitle}">`
  );

  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:description" content="[^"]*" \/>/g,
    `<meta property="og:description" content="${config.ogDescription}" />`
  );

  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:description" content="[^"]*">/g,
    `<meta property="og:description" content="${config.ogDescription}">`
  );

  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:url" content="[^"]*" \/>/g,
    `<meta property="og:url" content="${config.ogUrl}" />`
  );

  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:url" content="[^"]*">/g,
    `<meta property="og:url" content="${config.ogUrl}">`
  );

  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:image" content="[^"]*" \/>/g,
    `<meta property="og:image" content="${config.ogImage}" />`
  );

  modifiedHtml = modifiedHtml.replace(
    /<meta property="og:image" content="[^"]*">/g,
    `<meta property="og:image" content="${config.ogImage}">`
  );

  // Add LinkedIn profile link if available
  if (config.linkedIn) {
    modifiedHtml = modifiedHtml.replace(
      /<link rel="canonical" href="[^"]*" \/>/g,
      `<link rel="canonical" href="${config.canonical}" />
    <link rel="me" href="${config.linkedIn}" />`
    );

    modifiedHtml = modifiedHtml.replace(
      /<link rel="canonical" href="[^"]*">/g,
      `<link rel="canonical" href="${config.canonical}">
    <link rel="me" href="${config.linkedIn}">`
    );
  }

  console.log('SEO injection completed');
  return modifiedHtml;
}

// SEO Route Handler
app.get(['/leadership/prajanna-raj-adhikari', '/our-leadership'], (req, res) => {
  const indexPath = path.join(__dirname, '../dist/index.html');

  fs.readFile(indexPath, 'utf8', (err, html) => {
    if (err) {
      console.error('Error reading index.html:', err);
      return res.status(500).send('Server Error');
    }

    const modifiedHtml = injectSEOTags(html, req.path);
    res.send(modifiedHtml);
  });
});

app.post("/api/contact", async (req, res) => {
  const { firstName, lastName, email, message, phone, inquiryType, subject } =
    req.body;
  try {
    // Email to team
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER,
      subject: `New Contact Form Submission from ${firstName} ${lastName}`,
      html: contactEmailTemplate({
        firstName,
        lastName,
        email,
        message,
        phone,
        inquiryType,
        subject,
      }),
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Thank you for contacting Trade Vision!",
      html: confirmationEmailTemplate({ firstName, lastName }),
    });

    res
      .status(200)
      .json({ success: true, message: "Emails sent successfully" });
  } catch (error) {
    console.error("Email send error:", error);
    res.status(500).json({ success: false, message: "Failed to send emails" });
  }
});

app.post("/api/global", async (req, res) => {
  const {
    companyName,
    contactPerson,
    email,
    country,
    expectedAnualVolume,
    distributionExp,
  } = req.body;
  try {
    // Email to team
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER,
      subject: `New Partner Form Submission from ${email}`,
      html: globalEmailTemplate({
        companyName,
        contactPerson,
        email,
        country,
        expectedAnualVolume,
        distributionExp,
      }),
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Thank you for partnering with Trade Vision!",
      html: confirmationGlobalEmailTemplate({ companyName }),
    });

    res
      .status(200)
      .json({ success: true, message: "Emails sent successfully" });
  } catch (error) {
    console.error("Email send error:", error);
    res.status(500).json({ success: false, message: "Failed to send emails" });
  }
});

app.post("/api/local", async (req, res) => {
  const { fullName, businessName, phone, email, businessLocation, message } =
    req.body;
  try {
    // Email to team
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER, // Team's email
      subject: `New Local Product Inquiry Form Submission from ${fullName}`,
      html: localEmailTemplate({
        fullName,
        businessName,
        phone,
        email,
        businessLocation,
        message,
      }),
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Thank you for your inquiry!",
      html: confirmationLocalEmailTemplate({ fullName }),
    });

    res
      .status(200)
      .json({ success: true, message: "Emails sent successfully" });
  } catch (error) {
    console.error("Email send error:", error);
    res.status(500).json({ success: false, message: "Failed to send emails" });
  }
});

// Catch-all handler: send back React's index.html file for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

const port = process.env.PORT || 8000;
app.listen(port, () => {
  console.log(`Server started on port ${port}`);
  console.log(`Frontend available at: http://localhost:${port}`);
  console.log(`SEO-optimized routes:`);
  console.log(`  - http://localhost:${port}/leadership/prajanna-raj-adhikari`);
  console.log(`  - http://localhost:${port}/our-leadership`);
});
