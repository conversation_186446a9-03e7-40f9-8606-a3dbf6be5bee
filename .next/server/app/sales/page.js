(()=>{var a={};a.id=117,a.ids=[117],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},933:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/shangrila/src/components/SalesForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/shangrila/src/components/SalesForm.tsx","default")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7807:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(21124),e=c(38301),f=c(35284),g=c(93758),h=c(68120),i=c(68254),j=c(13222),k=c(42830);let l=()=>{let[a,b]=(0,e.useState)({fullName:"",businessName:"",email:"",phone:"",businessLocation:"",message:""}),[c,l]=(0,e.useState)({}),[m,n]=(0,e.useState)(!1),o=a=>{let{name:d,value:e}=a.target;b(a=>({...a,[d]:e})),c[d]&&l(a=>({...a,[d]:""}))},p=async c=>{if(c.preventDefault(),(()=>{let b={};return a.fullName.trim()||(b.fullName="Full name is required"),a.businessName.trim()||(b.businessName="Business name is required"),a.email?/\S+@\S+\.\S+/.test(a.email)||(b.email="Email is invalid"):b.email="Email is required",a.phone?/^[0-9\s\-+()]*$/.test(a.phone)||(b.phone="Please enter a valid phone number"):b.phone="Phone number is required",a.businessLocation.trim()||(b.businessLocation="Business location is required"),a.message.trim()||(b.message="Please tell us about your business"),l(b),0===Object.keys(b).length})()){n(!0);try{let c=await j.A.post(`${process.env.NEXT_PUBLIC_API_URL||"https://mail.shangriladistillery.com"}/api/local`,a,{headers:{"Content-Type":"application/json"}});if(200===c.status)k.toast.success("Your application has been submitted successfully!"),b({fullName:"",businessName:"",email:"",phone:"",businessLocation:"",message:""});else throw Error("Failed to submit application")}catch(a){console.error("Error submitting application:",a),k.toast.error(a.response?.data?.message||"Failed to submit application. Please try again later.")}finally{n(!1)}}};return(0,d.jsxs)("div",{className:"distillery-card p-8",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Join Our Network"}),(0,d.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,d.jsxs)("form",{className:"space-y-6",onSubmit:p,children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"fullName",className:"text-amber-200",children:"Full Name"}),(0,d.jsx)(g.p,{id:"fullName",name:"fullName",type:"text",value:a.fullName,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.fullName?"border-red-500":""}`,onChange:o}),c.fullName&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.fullName})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"businessName",className:"text-amber-200",children:"Business Name"}),(0,d.jsx)(g.p,{id:"businessName",name:"businessName",type:"text",value:a.businessName,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.businessName?"border-red-500":""}`,onChange:o}),c.businessName&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.businessName})]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,d.jsx)(g.p,{id:"email",name:"email",type:"email",value:a.email,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.email?"border-red-500":""}`,onChange:o}),c.email&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"phone",className:"text-amber-200",children:"Phone"}),(0,d.jsx)(g.p,{id:"phone",name:"phone",type:"tel",value:a.phone,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.phone?"border-red-500":""}`,onChange:o}),c.phone&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.phone})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"businessLocation",className:"text-amber-200",children:"Business Location"}),(0,d.jsx)(g.p,{id:"businessLocation",name:"businessLocation",type:"text",value:a.businessLocation,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.businessLocation?"border-red-500":""}`,onChange:o}),c.businessLocation&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.businessLocation})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"message",className:"text-amber-200",children:"Tell us about your business"}),(0,d.jsx)(i.T,{id:"message",name:"message",rows:4,value:a.message,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.message?"border-red-500":""}`,onChange:o}),c.message&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.message})]}),(0,d.jsx)(f.$,{type:"submit",disabled:m,className:`w-full premium-button py-3 font-crimson font-semibold text-lg ${m?"opacity-70 cursor-not-allowed":""}`,children:m?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Submitting..."]}):"Apply to Become a Dealer"})]})})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25267:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,75687)),Promise.resolve().then(c.bind(c,7807))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35284:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(21124),e=c(38301),f=c(96425),g=c(26691),h=c(44943);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44943:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(43249),e=c(58829);function f(...a){return(0,e.QP)((0,d.$)(a))}},48788:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(75338),e=c(24169),f=c(933),g=c(80450),h=c(64723);let i=(0,c(4290).A)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]]);var j=c(50163),k=c(73515);let l={title:"Sales & Distribution - Join Our Network",description:"Join Shangrila Distillery's distribution network in Nepal. Become a dealer and bring premium craft spirits to your local market.",keywords:["Shangrila Distillery Sales","Nepal Spirits Distribution","Become Dealer Nepal","Premium Spirits Sales","Distillery Partnership Nepal"],openGraph:{title:"Sales & Distribution | Shangrila Distillery",description:"Join our growing network of partners and bring premium Nepalese spirits to your market.",url:"https://shangriladistillery.com/sales",images:[{url:"/lovable-uploads/favicon-shangrila.png",width:1200,height:630,alt:"Shangrila Distillery Sales"}]},alternates:{canonical:"https://shangriladistillery.com/sales"}};function m(){return(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(e.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("div",{className:"flex justify-center mb-6",children:(0,d.jsx)("img",{src:"/lovable-uploads/favicon-shangrila.png",alt:"Shangrila Distillery",className:"h-20 w-20 rounded-full object-cover filter brightness-110 shadow-lg"})}),(0,d.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Local Heritage"}),(0,d.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"We're expanding across Nepal, bringing premium spirits to every corner of the nation."})]}),(0,d.jsxs)("div",{className:"distillery-card p-8 mb-16 text-center",children:[(0,d.jsx)(h.A,{className:"h-16 w-16 text-amber-400 mx-auto mb-6 animate-float"}),(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-4",children:"Expanding Across Nepal"}),(0,d.jsx)("p",{className:"text-lg distillery-text max-w-2xl mx-auto",children:"From Kathmandu to the furthest reaches of our beautiful nation, we're building a comprehensive distribution network to serve every region."})]}),(0,d.jsxs)("div",{className:"mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Our Target Segments"}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{price:"25 UP",description:"Extra Premium segment"},{price:"30 UP",description:"Premium Vodka Segment"},{price:"40 UP",description:"Premium Segment"},{price:"70 UP",description:"Entry Segment"}].map(a=>(0,d.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,d.jsx)("div",{className:"text-3xl font-playfair font-bold text-amber-400 mb-2",children:a.price}),(0,d.jsx)("p",{className:"distillery-text",children:a.description})]},a.price))})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:[(0,d.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,d.jsx)(i,{className:"h-12 w-12 text-amber-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:"Bottle Shops"}),(0,d.jsx)("p",{className:"distillery-text",children:"Premium retail outlets across major cities and towns"})]}),(0,d.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,d.jsx)(j.A,{className:"h-12 w-12 text-amber-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:"HORECA"}),(0,d.jsx)("p",{className:"distillery-text",children:"Hotels, restaurants, and cafes for premium dining experiences"})]}),(0,d.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,d.jsx)(k.A,{className:"h-12 w-12 text-amber-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:"Distribution"}),(0,d.jsx)("p",{className:"distillery-text",children:"Authorized distributors for nationwide coverage"})]})]}),(0,d.jsxs)("div",{className:"distillery-card p-8 mb-16 text-center",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-4",children:"2,00,000+ litres storage capacity"}),(0,d.jsx)("p",{className:"text-lg distillery-text max-w-2xl mx-auto",children:"and planning on more"})]}),(0,d.jsxs)("div",{className:"mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Why Partner With Us?"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"distillery-card p-6 text-center",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:"Quality Products"}),(0,d.jsx)("p",{className:"distillery-text",children:"A portfolio of award-winning, premium quality spirits."})]}),(0,d.jsxs)("div",{className:"distillery-card p-6 text-center",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:"Marketing Support"}),(0,d.jsx)("p",{className:"distillery-text",children:"Comprehensive marketing and promotional support."})]}),(0,d.jsxs)("div",{className:"distillery-card p-6 text-center",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:"Competitive Pricing"}),(0,d.jsx)("p",{className:"distillery-text",children:"Attractive pricing and margin for our partners."})]})]})]}),(0,d.jsx)(f.default,{})]}),(0,d.jsx)(g.A,{})]})]})}},50163:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68120:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(21124),e=c(38301),f=c(46029),g=c(26691),h=c(44943);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},68254:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},72123:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,24169)),Promise.resolve().then(c.bind(c,933))},73515:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93758:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:e,...c}));g.displayName="Input"},94735:a=>{"use strict";a.exports=require("events")},95456:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,48788)),"/home/<USER>/shangrila/src/app/sales/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/shangrila/src/app/sales/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/sales/page",pathname:"/sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/sales/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,251,583,602,925,744],()=>b(b.s=95456));module.exports=c})();