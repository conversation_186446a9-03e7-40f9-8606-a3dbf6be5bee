(()=>{var a={};a.id=958,a.ids=[958],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8010:(a,b,c)=>{"use strict";c.d(b,{$:()=>m});var d=c(75338),e=c(74515),f=e.forwardRef((a,b)=>{let{children:c,...f}=a,h=e.Children.toArray(c),j=h.find(i);if(j){let a=j.props.children,c=h.map(b=>b!==j?b:e.Children.count(a)>1?e.Children.only(null):e.isValidElement(a)?a.props.children:null);return(0,d.jsx)(g,{...f,ref:b,children:e.isValidElement(a)?e.cloneElement(a,void 0,c):null})}return(0,d.jsx)(g,{...f,ref:b,children:c})});f.displayName="Slot";var g=e.forwardRef((a,b)=>{let{children:c,...d}=a;if(e.isValidElement(c)){let a=function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(c);return e.cloneElement(c,{...function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{f(...a),e(...a)}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props),ref:b?function(...a){return b=>a.forEach(a=>{"function"==typeof a?a(b):null!=a&&(a.current=b)})}(b,a):a})}return e.Children.count(c)>1?e.Children.only(null):null});g.displayName="SlotClone";var h=({children:a})=>(0,d.jsx)(d.Fragment,{children:a});function i(a){return e.isValidElement(a)&&a.type===h}var j=c(86281),k=c(70673);let l=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},h)=>{let i=e?f:"button";return(0,d.jsx)(i,{className:(0,k.cn)(l({variant:b,size:c,className:a})),ref:h,...g})});m.displayName="Button"},8312:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(21124),e=c(38301),f=c(35284),g=c(93758),h=c(68120),i=c(68254),j=c(13222),k=c(42830);let l=()=>{let[a,b]=(0,e.useState)({companyName:"",contactPerson:"",email:"",country:"",expectedAnualVolume:"",distributionExp:""}),[c,l]=(0,e.useState)({}),[m,n]=(0,e.useState)(!1),o=a=>{let{name:d,value:e}=a.target;b(a=>({...a,[d]:e})),c[d]&&l(a=>({...a,[d]:""}))},p=async c=>{if(c.preventDefault(),(()=>{let b={};return a.companyName.trim()||(b.companyName="Company name is required"),a.contactPerson.trim()||(b.contactPerson="Contact person is required"),a.email?/\S+@\S+\.\S+/.test(a.email)||(b.email="Email is invalid"):b.email="Email is required",a.country.trim()||(b.country="Country is required"),a.expectedAnualVolume.trim()||(b.expectedAnualVolume="Expected annual volume is required"),a.distributionExp.trim()||(b.distributionExp="Distribution experience is required"),l(b),0===Object.keys(b).length})()){n(!0);try{let c=await j.A.post(`${process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000"}/api/global`,a,{headers:{"Content-Type":"application/json"}});if(200===c.status)k.toast.success("Your inquiry has been sent successfully!"),b({companyName:"",contactPerson:"",email:"",country:"",expectedAnualVolume:"",distributionExp:""});else throw Error("Failed to send message")}catch(a){console.error("Error sending message:",a),k.toast.error(a.response?.data?.message||"Failed to send message. Please try again later.")}finally{n(!1)}}};return(0,d.jsxs)("div",{className:"distillery-card p-8",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"B2B Partnership Inquiry"}),(0,d.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,d.jsxs)("form",{className:"space-y-6",onSubmit:p,children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"companyName",className:"text-amber-200",children:"Company Name"}),(0,d.jsx)(g.p,{id:"companyName",name:"companyName",type:"text",value:a.companyName,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.companyName?"border-red-500":""}`,onChange:o}),c.companyName&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.companyName})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"contactPerson",className:"text-amber-200",children:"Contact Person"}),(0,d.jsx)(g.p,{id:"contactPerson",name:"contactPerson",type:"text",value:a.contactPerson,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.contactPerson?"border-red-500":""}`,onChange:o}),c.contactPerson&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.contactPerson})]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,d.jsx)(g.p,{id:"email",name:"email",type:"email",value:a.email,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.email?"border-red-500":""}`,onChange:o}),c.email&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"country",className:"text-amber-200",children:"Country/Region"}),(0,d.jsx)(g.p,{id:"country",name:"country",type:"text",value:a.country,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.country?"border-red-500":""}`,onChange:o}),c.country&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.country})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"expectedAnualVolume",className:"text-amber-200",children:"Expected Annual Volume"}),(0,d.jsx)(g.p,{id:"expectedAnualVolume",name:"expectedAnualVolume",type:"text",value:a.expectedAnualVolume,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.expectedAnualVolume?"border-red-500":""}`,onChange:o,placeholder:"e.g., 10,000 bottles"}),c.expectedAnualVolume&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.expectedAnualVolume})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(h.J,{htmlFor:"distributionExp",className:"text-amber-200",children:"Distribution Experience"}),(0,d.jsx)(i.T,{id:"distributionExp",name:"distributionExp",rows:4,value:a.distributionExp,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${c.distributionExp?"border-red-500":""}`,onChange:o,placeholder:"Tell us about your distribution network and experience with premium spirits..."}),c.distributionExp&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.distributionExp})]}),(0,d.jsx)(f.$,{type:"submit",disabled:m,className:`w-full premium-button py-3 font-crimson font-semibold ${m?"opacity-70 cursor-not-allowed":""}`,children:m?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):"Submit Partnership Inquiry"})]})})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35140:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["exports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,44297)),"/home/<USER>/shangrila/src/app/exports/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/shangrila/src/app/exports/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/exports/page",pathname:"/exports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/exports/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},39614:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Handshake",[["path",{d:"m11 17 2 2a1 1 0 1 0 3-3",key:"efffak"}],["path",{d:"m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4",key:"9pr0kb"}],["path",{d:"m21 3 1 11h-2",key:"1tisrp"}],["path",{d:"M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3",key:"1uvwmv"}],["path",{d:"M3 4h8",key:"1ep09j"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44297:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>ac,metadata:()=>ab});var d,e,f,g,h=c(75338),i=c(24169),j=c(80450),k=c(4290);let l=(0,k.A)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]);var m=c(64723),n=c(73515);let o=()=>{let a=[{icon:l,title:"Export Growth",value:"300%",description:"Projected growth over 3 years"},{icon:m.A,title:"Target Markets",value:"15+",description:"Countries in expansion plan"},{icon:n.A,title:"Partner Network",value:"25+",description:"Potential distribution partners"}];return(0,h.jsx)("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:a.map(a=>(0,h.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,h.jsx)("div",{className:"w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,h.jsx)(a.icon,{className:"h-8 w-8 text-amber-400"})}),(0,h.jsx)("div",{className:"text-3xl font-playfair font-bold text-amber-400 mb-2",children:a.value}),(0,h.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:a.title}),(0,h.jsx)("p",{className:"distillery-text text-sm",children:a.description})]},a.title))})};var p=c(39614),q=c(75553);let r=()=>(0,h.jsxs)("div",{className:"mb-16",children:[(0,h.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Strategic Partnerships"}),(0,h.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,h.jsxs)("div",{className:"distillery-card p-8 text-center",children:[(0,h.jsx)(p.A,{className:"h-16 w-16 mx-auto mb-6 text-amber-400 animate-float"}),(0,h.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,h.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mr-3",children:"Trade Vision Partners"}),(0,h.jsx)("a",{href:"https://tradevisionpartners.com/",target:"_blank",rel:"noopener noreferrer",className:"text-amber-400 hover:text-amber-300 transition-colors",children:(0,h.jsx)(q.A,{className:"h-6 w-6"})})]}),(0,h.jsx)("p",{className:"text-amber-300 mb-4 font-crimson",children:"Australia"}),(0,h.jsx)("p",{className:"text-lg distillery-text",children:"Our flagship partnership brings decades of international distribution expertise, opening doors to premium markets across Australia and beyond. Together, we're setting new standards for Nepalese spirits on the global stage."})]}),(0,h.jsxs)("div",{className:"distillery-card p-8 text-center",children:[(0,h.jsx)(p.A,{className:"h-16 w-16 mx-auto mb-6 text-amber-400 animate-float"}),(0,h.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,h.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mr-3",children:"ShyamBaba Group"}),(0,h.jsx)("a",{href:"https://sbgcompanies.com/",target:"_blank",rel:"noopener noreferrer",className:"text-amber-400 hover:text-amber-300 transition-colors",children:(0,h.jsx)(q.A,{className:"h-6 w-6"})})]}),(0,h.jsx)("p",{className:"text-amber-300 mb-4 font-crimson",children:"Nepal"}),(0,h.jsx)("p",{className:"text-lg distillery-text mb-4",children:"Nepal's foremost diversified business group with interests in Food Products, Cement, Construction, Manufacturing and Trading."}),(0,h.jsx)("p",{className:"distillery-text",children:"With over 2000 employees and 10,000 direct customers, ShyamBaba Group touches lives across Nepal through its vast offering of market-leading high quality consumer products."})]})]})]});var s=c(79574);let t=(0,k.A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]);var u=c(63234);let v=()=>{let a=[{icon:s.A,title:"International Standards",description:"ISO certified facilities meeting global quality requirements"},{icon:t,title:"Logistics Excellence",description:"Streamlined export processes and cold chain management"},{icon:u.A,title:"Global Compliance",description:"Meeting regulatory requirements across target markets"},{icon:n.A,title:"Partner Support",description:"Dedicated team for international business development"}];return(0,h.jsxs)("div",{className:"mb-16",children:[(0,h.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Export-Ready Infrastructure"}),(0,h.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:a.map(a=>(0,h.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,h.jsx)(a.icon,{className:"h-12 w-12 text-amber-400 mx-auto mb-4"}),(0,h.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:a.title}),(0,h.jsx)("p",{className:"distillery-text",children:a.description})]},a.title))})]})},w=()=>(0,h.jsxs)("div",{className:"distillery-card p-8 mb-16",children:[(0,h.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Global Expansion Strategy"}),(0,h.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-4",children:"Phase 1: Asia-Pacific"}),(0,h.jsxs)("ul",{className:"space-y-2 distillery-text",children:[(0,h.jsx)("li",{children:"• Australia & New Zealand"}),(0,h.jsx)("li",{children:"• Southeast Asian markets"}),(0,h.jsx)("li",{children:"• Premium retail partnerships"})]})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-4",children:"Phase 2: Global Markets"}),(0,h.jsxs)("ul",{className:"space-y-2 distillery-text",children:[(0,h.jsx)("li",{children:"• European Union"}),(0,h.jsx)("li",{children:"• North American markets"}),(0,h.jsx)("li",{children:"• Duty-free channels"})]})]})]})]});var x=c(8010),y=c(74515),z=c(22682);function A(){return(A=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function B(a,b){if(!1===a||null==a)throw Error(b)}function C(a){let{pathname:b="/",search:c="",hash:d=""}=a;return c&&"?"!==c&&(b+="?"===c.charAt(0)?c:"?"+c),d&&"#"!==d&&(b+="#"===d.charAt(0)?d:"#"+d),b}function D(a){let b={};if(a){let c=a.indexOf("#");c>=0&&(b.hash=a.substr(c),a=a.substr(0,c));let d=a.indexOf("?");d>=0&&(b.search=a.substr(d),a=a.substr(0,d)),a&&(b.pathname=a)}return b}function E(a,b,c,d){return"Cannot include a '"+a+"' character in a manually specified "+("`to."+b+"` field [")+JSON.stringify(d)+"].  Please separate it out to the `to."+c+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function F(a,b){let c=a.filter((a,b)=>0===b||a.route.path&&a.route.path.length>0);return b?c.map((a,b)=>b===c.length-1?a.pathname:a.pathnameBase):c.map(a=>a.pathnameBase)}function G(a,b,c,d){let e,f;void 0===d&&(d=!1),"string"==typeof a?e=D(a):(B(!(e=A({},a)).pathname||!e.pathname.includes("?"),E("?","pathname","search",e)),B(!e.pathname||!e.pathname.includes("#"),E("#","pathname","hash",e)),B(!e.search||!e.search.includes("#"),E("#","search","hash",e)));let g=""===a||""===e.pathname,h=g?"/":e.pathname;if(null==h)f=c;else{let a=b.length-1;if(!d&&h.startsWith("..")){let b=h.split("/");for(;".."===b[0];)b.shift(),a-=1;e.pathname=b.join("/")}f=a>=0?b[a]:"/"}let i=function(a,b){var c;let d;void 0===b&&(b="/");let{pathname:e,search:f="",hash:g=""}="string"==typeof a?D(a):a;return{pathname:e?e.startsWith("/")?e:(c=e,d=b.replace(/\/+$/,"").split("/"),c.split("/").forEach(a=>{".."===a?d.length>1&&d.pop():"."!==a&&d.push(a)}),d.length>1?d.join("/"):"/"):b,search:I(f),hash:J(g)}}(e,f),j=h&&"/"!==h&&h.endsWith("/"),k=(g||"."===h)&&c.endsWith("/");return!i.pathname.endsWith("/")&&(j||k)&&(i.pathname+="/"),i}!function(a){a.Pop="POP",a.Push="PUSH",a.Replace="REPLACE"}(d||(d={})),function(a){a.data="data",a.deferred="deferred",a.redirect="redirect",a.error="error"}(e||(e={}));let H=a=>a.join("/").replace(/\/\/+/g,"/"),I=a=>a&&"?"!==a?a.startsWith("?")?a:"?"+a:"",J=a=>a&&"#"!==a?a.startsWith("#")?a:"#"+a:"";function K(){return(K=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}Symbol("deferred");let L=y.createContext(null),M=y.createContext(null),N=y.createContext(null),O=y.createContext({outlet:null,matches:[],isDataRoute:!1});function P(){return null!=y.useContext(N)}function Q(){return P()||B(!1),y.useContext(N).location}function R(a){y.useContext(M).static||y.useLayoutEffect(a)}function S(a,b){let{relative:c}=void 0===b?{}:b,{future:d}=y.useContext(M),{matches:e}=y.useContext(O),{pathname:f}=Q(),g=JSON.stringify(F(e,d.v7_relativeSplatPath));return y.useMemo(()=>G(a,JSON.parse(g),f,"path"===c),[a,g,f,c])}y.Component;var T=function(a){return a.UseBlocker="useBlocker",a.UseRevalidator="useRevalidator",a.UseNavigateStable="useNavigate",a}(T||{}),U=function(a){return a.UseBlocker="useBlocker",a.UseLoaderData="useLoaderData",a.UseActionData="useActionData",a.UseRouteError="useRouteError",a.UseNavigation="useNavigation",a.UseRouteLoaderData="useRouteLoaderData",a.UseMatches="useMatches",a.UseRevalidator="useRevalidator",a.UseNavigateStable="useNavigate",a.UseRouteId="useRouteId",a}(U||{});y.startTransition;var V=function(a){return a[a.pending=0]="pending",a[a.success=1]="success",a[a.error=2]="error",a}(V||{});function W(){return(W=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}new Promise(()=>{}),y.Component;let X=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(a){}y.startTransition,z.flushSync,y.useId;let Y="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Z=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,$=y.forwardRef(function(a,b){let c,{onClick:d,relative:e,reloadDocument:f,replace:g,state:h,target:i,to:j,preventScrollReset:k,viewTransition:l}=a,m=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,X),{basename:n}=y.useContext(M),o=!1;if("string"==typeof j&&Z.test(j)&&(c=j,Y))try{let a=new URL(window.location.href),b=new URL(j.startsWith("//")?a.protocol+j:j),c=function(a,b){if("/"===b)return a;if(!a.toLowerCase().startsWith(b.toLowerCase()))return null;let c=b.endsWith("/")?b.length-1:b.length,d=a.charAt(c);return d&&"/"!==d?null:a.slice(c)||"/"}(b.pathname,n);b.origin===a.origin&&null!=c?j=c+b.search+b.hash:o=!0}catch(a){}let p=function(a,b){let{relative:c}=void 0===b?{}:b;P()||B(!1);let{basename:d,navigator:e}=y.useContext(M),{hash:f,pathname:g,search:h}=S(a,{relative:c}),i=g;return"/"!==d&&(i="/"===g?d:H([d,g])),e.createHref({pathname:i,search:h,hash:f})}(j,{relative:e}),q=function(a,b){let{target:c,replace:d,state:e,preventScrollReset:f,relative:g,viewTransition:h}=void 0===b?{}:b,i=function(){let{isDataRoute:a}=y.useContext(O);return a?function(){let a,b,c,d,{router:e}=(T.UseNavigateStable,(a=y.useContext(L))||B(!1),a),f=(U.UseNavigateStable,(d=((b=y.useContext(O))||B(!1),c=b).matches[c.matches.length-1]).route.id||B(!1),d.route.id),g=y.useRef(!1);return R(()=>{g.current=!0}),y.useCallback(function(a,b){void 0===b&&(b={}),g.current&&("number"==typeof a?e.navigate(a):e.navigate(a,K({fromRouteId:f},b)))},[e,f])}():function(){P()||B(!1);let a=y.useContext(L),{basename:b,future:c,navigator:d}=y.useContext(M),{matches:e}=y.useContext(O),{pathname:f}=Q(),g=JSON.stringify(F(e,c.v7_relativeSplatPath)),h=y.useRef(!1);return R(()=>{h.current=!0}),y.useCallback(function(c,e){if(void 0===e&&(e={}),!h.current)return;if("number"==typeof c)return void d.go(c);let i=G(c,JSON.parse(g),f,"path"===e.relative);null==a&&"/"!==b&&(i.pathname="/"===i.pathname?b:H([b,i.pathname])),(e.replace?d.replace:d.push)(i,e.state,e)},[b,d,g,f,a])}()}(),j=Q(),k=S(a,{relative:g});return y.useCallback(b=>{0!==b.button||c&&"_self"!==c||b.metaKey||b.altKey||b.ctrlKey||b.shiftKey||(b.preventDefault(),i(a,{replace:void 0!==d?d:C(j)===C(k),state:e,preventScrollReset:f,relative:g,viewTransition:h}))},[j,i,k,d,e,c,a,f,g,h])}(j,{replace:g,state:h,target:i,preventScrollReset:k,relative:e,viewTransition:l});return y.createElement("a",W({},m,{href:c||p,onClick:o||f?d:function(a){d&&d(a),a.defaultPrevented||q(a)},ref:b,target:i}))});!function(a){a.UseScrollRestoration="useScrollRestoration",a.UseSubmit="useSubmit",a.UseSubmitFetcher="useSubmitFetcher",a.UseFetcher="useFetcher",a.useViewTransitionState="useViewTransitionState"}(f||(f={})),function(a){a.UseFetcher="useFetcher",a.UseFetchers="useFetchers",a.UseScrollRestoration="useScrollRestoration"}(g||(g={}));let _=()=>(0,h.jsxs)("div",{className:"distillery-card p-8 mb-16 text-center",children:[(0,h.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-4",children:"Interested in Importing Our Brands?"}),(0,h.jsx)("p",{className:"text-lg distillery-text mb-6 max-w-2xl mx-auto",children:"Join our growing network of international partners and bring authentic Nepalese premium spirits to your market."}),(0,h.jsx)($,{to:"/contact",children:(0,h.jsx)($,{to:"/contact",children:(0,h.jsx)(x.$,{className:"premium-button px-8 py-3 text-lg font-crimson font-semibold",children:"Contact Our Export Team"})})})]});var aa=c(64110);let ab={title:"Global Exports - Bringing Shangrila Spirits Worldwide",description:"Discover Shangrila Distillery's global export strategy. Partner with us to bring premium Nepalese spirits to international markets.",keywords:["Shangrila Distillery Exports","Nepal Spirits Export","International Distribution","Global Spirits Partnership","Premium Spirits Export"],openGraph:{title:"Global Exports | Shangrila Distillery",description:"Expanding globally through strategic partnerships and export excellence.",url:"https://shangriladistillery.com/exports",images:[{url:"/lovable-uploads/favicon-shangrila.png",width:1200,height:630,alt:"Shangrila Distillery Global Exports"}]},alternates:{canonical:"https://shangriladistillery.com/exports"}};function ac(){return(0,h.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,h.jsx)(i.default,{}),(0,h.jsxs)("div",{className:"pt-20",children:[(0,h.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,h.jsxs)("div",{className:"text-center mb-16",children:[(0,h.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Bringing Shangrila Spirits to the World"}),(0,h.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Expanding globally through strategic partnerships and export excellence."})]}),(0,h.jsx)(o,{}),(0,h.jsx)(r,{}),(0,h.jsx)(v,{}),(0,h.jsx)(w,{}),(0,h.jsx)(_,{}),(0,h.jsx)(aa.default,{})]}),(0,h.jsx)(j.A,{})]})]})}},50332:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,8312)),Promise.resolve().then(c.bind(c,75687))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63234:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},64110:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/shangrila/src/components/global-partners/B2BInquiryForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/shangrila/src/components/global-partners/B2BInquiryForm.tsx","default")},68254:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},73515:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},74075:a=>{"use strict";a.exports=require("zlib")},75553:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},79551:a=>{"use strict";a.exports=require("url")},79574:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92188:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,64110)),Promise.resolve().then(c.bind(c,24169))},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,482,373,828,529,744],()=>b(b.s=35140));module.exports=c})();