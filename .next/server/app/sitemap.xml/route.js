(()=>{var a={};a.id=475,a.ids=[475],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1581:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{default:()=>x});var e={};c.r(e),c.d(e,{GET:()=>A});var f=c(95736),g=c(9117),h=c(4044),i=c(39326),j=c(32324),k=c(261),l=c(54290),m=c(85328),n=c(38928),o=c(46595),p=c(3421),q=c(17679),r=c(41681),s=c(63446),t=c(86439),u=c(51356),v=c(10641),w=c(47616);function x(){let a="https://shangriladistillery.com";return[{url:a,lastModified:new Date,changeFrequency:"weekly",priority:1},{url:`${a}/about`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${a}/products`,lastModified:new Date,changeFrequency:"weekly",priority:.9},{url:`${a}/facility`,lastModified:new Date,changeFrequency:"monthly",priority:.7},{url:`${a}/sales`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${a}/exports`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${a}/events`,lastModified:new Date,changeFrequency:"weekly",priority:.5},{url:`${a}/careers`,lastModified:new Date,changeFrequency:"weekly",priority:.5},{url:`${a}/contact`,lastModified:new Date,changeFrequency:"monthly",priority:.7},{url:`${a}/our-leadership`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${a}/leadership/prajanna-raj-adhikari`,lastModified:new Date,changeFrequency:"monthly",priority:.7},{url:`${a}/blog`,lastModified:new Date,changeFrequency:"weekly",priority:.6},{url:`${a}/privacy-policy`,lastModified:new Date,changeFrequency:"yearly",priority:.3},{url:`${a}/terms-of-service`,lastModified:new Date,changeFrequency:"yearly",priority:.3},{url:`${a}/responsible-drinking`,lastModified:new Date,changeFrequency:"yearly",priority:.4},...w.G.map(b=>({url:`${a}/blog/${b.slug}`,lastModified:b.modifiedDate?new Date(b.modifiedDate):new Date,changeFrequency:"monthly",priority:"prajanna-raj-adhikari-leading-shangrila-distillery"===b.slug?.8:.5}))]}var y=c(39582);let z={...d}.default;if("function"!=typeof z)throw Error('Default export is missing in "/home/<USER>/shangrila/src/app/sitemap.ts"');async function A(a,b){let{__metadata_id__:c,...d}=await b.params||{},e=!!c&&c.endsWith(".xml");if(c&&!e)return new v.NextResponse("Not Found",{status:404});let f=c&&e?c.slice(0,-4):void 0,g=await z({id:f}),h=(0,y.resolveRouteData)(g,"sitemap");return new v.NextResponse(h,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let B=new f.AppRouteRouteModule({definition:{kind:g.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},distDir:".next",relativeProjectDir:"",resolvedPagePath:"next-metadata-route-loader?filePath=%2Fhome%2Fpradeep%2Fshangrila%2Fsrc%2Fapp%2Fsitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:e}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,h.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/sitemap.xml/route";"/index"===e&&(e="/");let f=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:!1});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:h,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=f,E=(0,k.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new t.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,j.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{cacheComponents:!!w.experimental.cacheComponents,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,i.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:h}},N=new l.NodeNextRequest(a),O=new l.NodeNextResponse(b),P=m.NextRequestAdapter.fromNodeNextRequest(N,(0,m.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==n.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),f=async f=>{var h,j;let k=async({previousCacheEntry:g})=>{try{if(!(0,i.getRequestMeta)(a,"minimalMode")&&A&&C&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=M.renderOpts.fetchMetrics;let h=M.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,p.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,q.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[s.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=s.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=s.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:u.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,o.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:g.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(h=l.value)?void 0:h.kind)!==u.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,q.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,i.getRequestMeta)(a,"minimalMode")&&F||m.delete(s.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,r.getCacheControlHeader)(l.cacheControl)),await (0,p.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await f(L):await K.withPropagatedContext(a.headers,()=>K.trace(n.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:j.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},f))}catch(b){if(L||b instanceof t.NoFallbackError||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,o.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,p.I)(N,O,new Response(null,{status:500})),null}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47616:(a,b,c)=>{"use strict";c.d(b,{G:()=>d});let d=[{id:7,slug:"prajanna-raj-adhikari-leading-shangrila-distillery",image:"/lovable-uploads/prashanna.png",metaTitle:"Prajanna Raj Adhikari – Managing Director Shangrila Distillery Nepal | Leadership Profile",metaDescription:"Meet Prajanna Raj Adhikari, Managing Director of Shangrila Distillery Nepal. Discover his journey from Australia to leading Nepal's premier whiskey, vodka & gin distillery with global business expertise.",title:"Prajanna Raj Adhikari – Leading Shangrila Distillery into a New Era of Excellence",introduction:"When it comes to the art and business of spirits, leadership requires more than just management skills — it demands vision, industry knowledge, and a deep understanding of both craft and market. Prajanna Raj Adhikari, Managing Director of Shangrila Distillery Limited, embodies all of these qualities, bringing a rare blend of hands-on experience, academic excellence, and entrepreneurial insight to Nepal’s evolving liquor industry.",publishedDate:"2024-09-15T10:00:00.000Z",modifiedDate:"2024-09-18T15:30:00.000Z",keywords:["Prajanna Raj Adhikari","Shangrila Distillery Managing Director","Nepal Distillery CEO","Nepal Whiskey Industry Leader","Shangrila Distillery Leadership","Nepal Premium Spirits Executive","Distillery Management Nepal","Nepal Vodka Industry","Craft Spirits Nepal","Federation University Business Graduate","Victoria University MBA","Nepal Liquor Industry","Himalayan Spirits Leader","Nepal Gin Producer","Distillery Business Strategy","Nepal Alcohol Industry","Premium Spirits Nepal","Shangrila Distillery CEO","Nepal Beverage Industry Leader","Craft Distillery Management"],sections:[{title:"From Hospitality Floors to Boardroom Leadership",content:"Before stepping into his role as Managing Director at Shangrila Distillery Nepal, Prajanna immersed himself in the heart of the hospitality sector — managing pubs, restaurants, and bottle shops across Australia. This direct engagement with customers gave him a firsthand understanding of consumer preferences, service excellence, and operational efficiency in the premium spirits industry. He learned not just how to run successful outlets, but how to read the pulse of the market — knowledge that now informs his strategic decisions at Nepal's leading craft distillery."},{title:"Mastering the Craft of Blending",content:"Recognizing that great leadership in the spirits industry must be rooted in product knowledge, Prajanna pursued professional blending classes. These experiences expanded his understanding of flavor profiles, maturation, and quality control, allowing him to bridge the gap between distillation and consumer demand. His blending expertise ensures that Shangrila Distillery’s products are not only commercially viable but also competitive on a global scale."},{title:"Academic Excellence with a Global Perspective",content:"Prajanna’s academic journey reflects his commitment to business mastery. He earned his Bachelor’s in Business from Federation University, Australia, where he majored in Marketing — a field that sharpened his ability to position brands, understand market trends, and craft effective campaigns. He then went on to complete his Master of Business Administration (MBA) at Victoria University, specializing in Global Business. This education has equipped him with the strategic thinking needed to scale operations, enter new markets, and navigate complex international trade landscapes."},{title:"Driving Shangrila Distillery Forward",content:"Under Prajanna’s leadership, Shangrila Distillery is poised for expansion, both domestically and internationally. His marketing acumen ensures that each product resonates with its target audience, while his operational insights guarantee efficiency and consistency in production. By combining local heritage with global business strategies, he is positioning the distillery as a proud representative of Nepal’s craftsmanship on the world stage."},{title:"Why Prajanna Raj Adhikari is the Right Leader",content:"Proven Industry Experience – Years of hands-on work in hospitality and retail.\n\nTechnical Expertise – Formal training in blending and quality assessment.\n\nStrategic Vision – Marketing and global business knowledge from internationally recognized universities.\n\nLeadership in Action – Steering Shangrila Distillery toward growth and global recognition."}],conclusion:"Prajanna Raj Adhikari isn’t just managing Shangrila Distillery — he’s shaping its future. His unique blend of practical know-how, academic credentials, and global vision makes him the perfect leader to carry Nepal’s spirits industry into a new era of innovation and prestige."},{id:1,slug:"history-and-heritage-of-shangrila-distillery-nepal",metaTitle:"The Rich History of Shangrila Distillery Nepal – A Legacy of Excellence",metaDescription:"Discover the heritage of Shangrila Distillery Nepal, the best distillery of Nepal. From its founding to today, learn how it became a leader in premium spirits.",title:"The History and Heritage of Shangrila Distillery Nepal",introduction:"For decades, Shangrila Distillery Nepal has been synonymous with quality, tradition, and innovation in the spirits industry. Recognized as the best distillery of Nepal, it has grown from a humble local producer to a symbol of Nepalese pride.",sections:[{title:"Early Beginnings",content:"Founded with a mission to bring world-class spirits to Nepal, Shangrila Distillery began operations in Chitwan, where the clean water and favorable climate provided the ideal foundation for distillation."},{title:"Growth and Innovation",content:"Over the years, Shangrila Distillery expanded its product line and upgraded its facilities, introducing modern techniques while preserving time-honored craftsmanship."},{title:"Heritage and Cultural Connection",content:"Every bottle reflects Nepal’s cultural richness, from the choice of ingredients to the design of the packaging."}],conclusion:"Today, with its factory in Chitwan and head office in Kathmandu, Shangrila Distillery Nepal stands as a living testament to Nepal’s distilling heritage."},{id:2,slug:"inside-the-factory-how-shangrila-distillery-produces-world-class-spirits",metaTitle:"Shangrila Distillery Chitwan – Inside Nepal’s Finest Spirit Factory",metaDescription:"Explore the Chitwan factory of Shangrila Distillery, the best distillery of Nepal. Learn how premium spirits are crafted with care and tradition.",title:"Inside the Factory – How Shangrila Distillery in Chitwan Produces World-Class Spirits",introduction:"In the heart of Chitwan lies the beating heart of Shangrila Distillery Nepal — a state-of-the-art factory where tradition meets technology.",sections:[{title:"Step 1: Selecting Premium Ingredients",content:"The process begins with the careful selection of grains, botanicals, and pure water sourced locally."},{title:"Step 2: The Distillation Process",content:"Modern stills ensure purity, while expert distillers maintain flavor consistency."},{title:"Step 3: Aging and Maturation",content:"Spirits are aged in oak barrels to enhance flavor complexity."},{title:"Step 4: Bottling and Packaging",content:"Each product is elegantly packaged in eco-friendly materials."}],conclusion:"From Chitwan to the rest of Nepal, Shangrila’s factory remains a hub of quality and precision."},{id:3,slug:"top-5-reasons-why-shangrila-distillery-is-the-best-distillery-of-nepal",metaTitle:"Why Shangrila Distillery is Nepal’s Best – 5 Reasons That Set It Apart",metaDescription:"See why Shangrila Distillery is the best distillery of Nepal. From quality to sustainability, here’s what makes it unmatched.",title:"Top 5 Reasons Why Shangrila Distillery is the Best Distillery of Nepal",introduction:"If you’ve ever wondered why Shangrila Distillery Nepal has such a stellar reputation, here are the top five reasons.",sections:[{title:"Uncompromising Quality",content:"Every product meets strict standards."},{title:"Heritage and Tradition",content:"Rooted in Nepalese culture."},{title:"Innovation",content:"Adapting to global trends while preserving authenticity."},{title:"Sustainability",content:"Environmentally responsible operations."},{title:"Customer Trust",content:"A brand beloved across generations."}],conclusion:"From Chitwan’s lush landscapes to Kathmandu’s bustling markets, Shangrila continues to shine as the best distillery of Nepal."},{id:4,slug:"a-connoisseurs-guide-to-shangrila-distillerys-premium-alcoholic-beverages",metaTitle:"Shangrila Distillery Nepal – A Guide to Its Finest Spirits",metaDescription:"Explore the premium range from Shangrila Distillery Nepal. From whiskey to rum, discover the flavors that make it Nepal’s best distillery.",title:"A Connoisseur’s Guide to Shangrila Distillery’s Premium Alcoholic Beverages",introduction:"For those who appreciate fine spirits, Shangrila Distillery Nepal offers a diverse and premium selection.",sections:[{title:"Whiskey",content:"Aged to perfection with rich aroma."},{title:"Vodka",content:"Crisp, clean, and triple-distilled."},{title:"Rum",content:"Full-bodied and flavorful."},{title:"Special Editions",content:"Limited releases for collectors."}],conclusion:"Every sip from Shangrila is a celebration of Nepalese craftsmanship."},{id:5,slug:"sustainability-at-shangrila-distillery-nepal-brewing-a-greener-future",metaTitle:"Sustainable Distilling – How Shangrila Distillery Nepal Leads the Way",metaDescription:"Learn how Shangrila Distillery Nepal is reducing its environmental footprint while staying the best distillery of Nepal.",title:"Sustainability at Shangrila Distillery Nepal – Brewing a Greener Future",introduction:"Shangrila Distillery Nepal understands that great spirits should not come at the expense of the environment.",sections:[{title:"Eco-Friendly Packaging",content:"Using recyclable materials."},{title:"Water Conservation",content:"Reducing waste through efficient processes."},{title:"Waste Management",content:"Reusing and recycling by-products."}],conclusion:"Sustainability is not just a trend for Shangrila — it’s a core value."},{id:6,slug:"from-kathmandu-to-chitwan-how-shangrila-distillery-connects-nepals-culture-with-the-world",metaTitle:"Shangrila Distillery – Bridging Nepal’s Heritage from Chitwan to Kathmandu",metaDescription:"Discover how Shangrila Distillery connects Nepal’s culture and craftsmanship to the world through its locations in Chitwan and Kathmandu.",title:"From Kathmandu to Chitwan – How Shangrila Distillery Connects Nepal’s Culture with the World",introduction:"With its factory in Chitwan and head office in Kathmandu, Shangrila Distillery perfectly bridges Nepal’s traditions and modern business.",sections:[{title:"Chitwan – The Production Hub",content:"Harnessing natural resources for pure distillation."},{title:"Kathmandu – The Strategic Center",content:"Managing operations and exports."},{title:"Global Recognition",content:"Showcasing Nepalese spirits internationally."}],conclusion:"Shangrila is more than a distillery — it’s an ambassador of Nepal’s craftsmanship."}]},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,579],()=>b(b.s=1581));module.exports=c})();