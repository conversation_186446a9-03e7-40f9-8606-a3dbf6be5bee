(()=>{var a={};a.id=977,a.ids=[977],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10521:(a,b,c)=>{"use strict";c.d(b,{default:()=>cs});var d,e,f,g=c(21124),h=c(38301),i=c.t(h,2),j=c(35284),k=c(93758),l=c(68120),m=c(68254),n=c(23312);function o(a,[b,c]){return Math.min(c,Math.max(b,a))}function p(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}function q(a,b=[]){let c=[],d=()=>{let b=c.map(a=>h.createContext(a));return function(c){let d=c?.[a]||b;return h.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let e=h.createContext(d),f=c.length;c=[...c,d];let i=b=>{let{scope:c,children:d,...i}=b,j=c?.[a]?.[f]||e,k=h.useMemo(()=>i,Object.values(i));return(0,g.jsx)(j.Provider,{value:k,children:d})};return i.displayName=b+"Provider",[i,function(c,g){let i=g?.[a]?.[f]||e,j=h.useContext(i);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return h.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}function r(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function s(...a){return b=>{let c=!1,d=a.map(a=>{let d=r(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():r(a[b],null)}}}}function t(...a){return h.useCallback(s(...a),a)}function u(a){let b=function(a){let b=h.forwardRef((a,b)=>{let{children:c,...d}=a;if(h.isValidElement(c)){var e;let a,f,g=(e=c,(f=(a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.ref:(f=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref),i=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==h.Fragment&&(i.ref=b?s(b,g):g),h.cloneElement(c,i)}return h.Children.count(c)>1?h.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=h.forwardRef((a,c)=>{let{children:d,...e}=a,f=h.Children.toArray(d),i=f.find(w);if(i){let a=i.props.children,d=f.map(b=>b!==i?b:h.Children.count(a)>1?h.Children.only(null):h.isValidElement(a)?a.props.children:null);return(0,g.jsx)(b,{...e,ref:c,children:h.isValidElement(a)?h.cloneElement(a,void 0,d):null})}return(0,g.jsx)(b,{...e,ref:c,children:d})});return c.displayName=`${a}.Slot`,c}var v=Symbol("radix.slottable");function w(a){return h.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===v}var x=new WeakMap;function y(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=z(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function z(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],x.set(this,!0)}set(a,b){return x.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=z(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=y(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=y(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return y(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var A=h.createContext(void 0),B=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=u(`Primitive.${b}`),d=h.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return d.displayName=`Primitive.${b}`,{...a,[b]:d}},{});function C(a){let b=h.useRef(a);return h.useEffect(()=>{b.current=a}),h.useMemo(()=>(...a)=>b.current?.(...a),[])}var D="dismissableLayer.update",E=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),F=h.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:i,onInteractOutside:j,onDismiss:k,...l}=a,m=h.useContext(E),[n,o]=h.useState(null),q=n?.ownerDocument??globalThis?.document,[,r]=h.useState({}),s=t(b,a=>o(a)),u=Array.from(m.layers),[v]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),w=u.indexOf(v),x=n?u.indexOf(n):-1,y=m.layersWithOutsidePointerEventsDisabled.size>0,z=x>=w,A=function(a,b=globalThis?.document){let c=C(a),d=h.useRef(!1),e=h.useRef(()=>{});return h.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){H("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...m.branches].some(a=>a.contains(b));z&&!c&&(f?.(a),j?.(a),a.defaultPrevented||k?.())},q),F=function(a,b=globalThis?.document){let c=C(a),d=h.useRef(!1);return h.useEffect(()=>{let a=a=>{a.target&&!d.current&&H("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...m.branches].some(a=>a.contains(b))&&(i?.(a),j?.(a),a.defaultPrevented||k?.())},q);return!function(a,b=globalThis?.document){let c=function(a){let b=h.useRef(a);return h.useEffect(()=>{b.current=a}),h.useMemo(()=>(...a)=>b.current?.(...a),[])}(a);h.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{x===m.layers.size-1&&(d?.(a),!a.defaultPrevented&&k&&(a.preventDefault(),k()))},q),h.useEffect(()=>{if(n)return c&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(e=q.body.style.pointerEvents,q.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(n)),m.layers.add(n),G(),()=>{c&&1===m.layersWithOutsidePointerEventsDisabled.size&&(q.body.style.pointerEvents=e)}},[n,q,c,m]),h.useEffect(()=>()=>{n&&(m.layers.delete(n),m.layersWithOutsidePointerEventsDisabled.delete(n),G())},[n,m]),h.useEffect(()=>{let a=()=>r({});return document.addEventListener(D,a),()=>document.removeEventListener(D,a)},[]),(0,g.jsx)(B.div,{...l,ref:s,style:{pointerEvents:y?z?"auto":"none":void 0,...a.style},onFocusCapture:p(a.onFocusCapture,F.onFocusCapture),onBlurCapture:p(a.onBlurCapture,F.onBlurCapture),onPointerDownCapture:p(a.onPointerDownCapture,A.onPointerDownCapture)})});function G(){let a=new CustomEvent(D);document.dispatchEvent(a)}function H(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});if(b&&e.addEventListener(a,b,{once:!0}),d)e&&n.flushSync(()=>e.dispatchEvent(f));else e.dispatchEvent(f)}F.displayName="DismissableLayer",h.forwardRef((a,b)=>{let c=h.useContext(E),d=h.useRef(null),e=t(b,d);return h.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,g.jsx)(B.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var I=0;function J(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}function K(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function L(...a){return b=>{let c=!1,d=a.map(a=>{let d=K(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():K(a[b],null)}}}}var M=Symbol("radix.slottable");function N(a){return h.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===M}var O=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=function(a){let b=function(a){let b=h.forwardRef((a,b)=>{let{children:c,...d}=a;if(h.isValidElement(c)){var e;let a,f,g=(e=c,(f=(a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.ref:(f=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref),i=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==h.Fragment&&(i.ref=b?L(b,g):g),h.cloneElement(c,i)}return h.Children.count(c)>1?h.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=h.forwardRef((a,c)=>{let{children:d,...e}=a,f=h.Children.toArray(d),i=f.find(N);if(i){let a=i.props.children,d=f.map(b=>b!==i?b:h.Children.count(a)>1?h.Children.only(null):h.isValidElement(a)?a.props.children:null);return(0,g.jsx)(b,{...e,ref:c,children:h.isValidElement(a)?h.cloneElement(a,void 0,d):null})}return(0,g.jsx)(b,{...e,ref:c,children:d})});return c.displayName=`${a}.Slot`,c}(`Primitive.${b}`),d=h.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return d.displayName=`Primitive.${b}`,{...a,[b]:d}},{});function P(a){let b=h.useRef(a);return h.useEffect(()=>{b.current=a}),h.useMemo(()=>(...a)=>b.current?.(...a),[])}var Q="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},T=h.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...i}=a,[j,k]=h.useState(null),l=P(e),m=P(f),n=h.useRef(null),o=function(...a){return h.useCallback(L(...a),a)}(b,a=>k(a)),p=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(d){let a=function(a){if(p.paused||!j)return;let b=a.target;j.contains(b)?n.current=b:W(n.current,{select:!0})},b=function(a){if(p.paused||!j)return;let b=a.relatedTarget;null!==b&&(j.contains(b)||W(n.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&W(j)});return j&&c.observe(j,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,j,p.paused]),h.useEffect(()=>{if(j){X.add(p);let a=document.activeElement;if(!j.contains(a)){let b=new CustomEvent(Q,S);j.addEventListener(Q,l),j.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(W(d,{select:b}),document.activeElement!==c)return}(U(j).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&W(j))}return()=>{j.removeEventListener(Q,l),setTimeout(()=>{let b=new CustomEvent(R,S);j.addEventListener(R,m),j.dispatchEvent(b),b.defaultPrevented||W(a??document.body,{select:!0}),j.removeEventListener(R,m),X.remove(p)},0)}}},[j,l,m,p]);let q=h.useCallback(a=>{if(!c&&!d||p.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=U(a);return[V(b,a),V(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&W(f,{select:!0})):(a.preventDefault(),c&&W(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,p.paused]);return(0,g.jsx)(O.div,{tabIndex:-1,...i,ref:o,onKeyDown:q})});function U(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function V(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function W(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}T.displayName="FocusScope";var X=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=Y(a,b)).unshift(b)},remove(b){a=Y(a,b),a[0]?.resume()}}}();function Y(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var Z=globalThis?.document?h.useLayoutEffect:()=>{},$=i[" useId ".trim().toString()]||(()=>void 0),_=0;function aa(a){let[b,c]=h.useState($());return Z(()=>{a||c(a=>a??String(_++))},[a]),a||(b?`radix-${b}`:"")}var ab=c(62716),ac=c(24649),ad=h.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,g.jsx)(B.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,g.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ad.displayName="Arrow";var ae="Popper",[af,ag]=q(ae),[ah,ai]=af(ae),aj=a=>{let{__scopePopper:b,children:c}=a,[d,e]=h.useState(null);return(0,g.jsx)(ah,{scope:b,anchor:d,onAnchorChange:e,children:c})};aj.displayName=ae;var ak="PopperAnchor",al=h.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...e}=a,f=ai(ak,c),i=h.useRef(null),j=t(b,i);return h.useEffect(()=>{f.onAnchorChange(d?.current||i.current)}),d?null:(0,g.jsx)(B.div,{...e,ref:j})});al.displayName=ak;var am="PopperContent",[an,ao]=af(am),ap=h.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:e=0,align:f="center",alignOffset:i=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:l=[],collisionPadding:m=0,sticky:n="partial",hideWhenDetached:o=!1,updatePositionStrategy:p="optimized",onPlaced:q,...r}=a,s=ai(am,c),[u,v]=h.useState(null),w=t(b,a=>v(a)),[x,y]=h.useState(null),z=function(a){let[b,c]=h.useState(void 0);return Z(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}(x),A=z?.width??0,D=z?.height??0,E="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},F=Array.isArray(l)?l:[l],G=F.length>0,H={padding:E,boundary:F.filter(at),altBoundary:G},{refs:I,floatingStyles:J,placement:K,isPositioned:L,middlewareData:M}=(0,ab.we)({strategy:"fixed",placement:d+("center"!==f?"-"+f:""),whileElementsMounted:(...a)=>(0,ac.ll)(...a,{animationFrame:"always"===p}),elements:{reference:s.anchor},middleware:[(0,ab.cY)({mainAxis:e+D,alignmentAxis:i}),k&&(0,ab.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===n?(0,ab.ER)():void 0,...H}),k&&(0,ab.UU)({...H}),(0,ab.Ej)({...H,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),x&&(0,ab.UE)({element:x,padding:j}),au({arrowWidth:A,arrowHeight:D}),o&&(0,ab.jD)({strategy:"referenceHidden",...H})]}),[N,O]=av(K),P=C(q);Z(()=>{L&&P?.()},[L,P]);let Q=M.arrow?.x,R=M.arrow?.y,S=M.arrow?.centerOffset!==0,[T,U]=h.useState();return Z(()=>{u&&U(window.getComputedStyle(u).zIndex)},[u]),(0,g.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...J,transform:L?J.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:T,"--radix-popper-transform-origin":[M.transformOrigin?.x,M.transformOrigin?.y].join(" "),...M.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,g.jsx)(an,{scope:c,placedSide:N,onArrowChange:y,arrowX:Q,arrowY:R,shouldHideArrow:S,children:(0,g.jsx)(B.div,{"data-side":N,"data-align":O,...r,ref:w,style:{...r.style,animation:L?void 0:"none"}})})})});ap.displayName=am;var aq="PopperArrow",ar={top:"bottom",right:"left",bottom:"top",left:"right"},as=h.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=ao(aq,c),f=ar[e.placedSide];return(0,g.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,g.jsx)(ad,{...d,ref:b,style:{...d.style,display:"block"}})})});function at(a){return null!==a}as.displayName=aq;var au=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=av(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function av(a){let[b,c="center"]=a.split("-");return[b,c]}function aw(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var ax=Symbol("radix.slottable");function ay(a){return h.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===ax}var az=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=function(a){let b=function(a){let b=h.forwardRef((a,b)=>{let{children:c,...d}=a;if(h.isValidElement(c)){var e;let a,f,g=(e=c,(f=(a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.ref:(f=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref),i=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==h.Fragment&&(i.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=aw(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():aw(a[b],null)}}}}(b,g):g),h.cloneElement(c,i)}return h.Children.count(c)>1?h.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=h.forwardRef((a,c)=>{let{children:d,...e}=a,f=h.Children.toArray(d),i=f.find(ay);if(i){let a=i.props.children,d=f.map(b=>b!==i?b:h.Children.count(a)>1?h.Children.only(null):h.isValidElement(a)?a.props.children:null);return(0,g.jsx)(b,{...e,ref:c,children:h.isValidElement(a)?h.cloneElement(a,void 0,d):null})}return(0,g.jsx)(b,{...e,ref:c,children:d})});return c.displayName=`${a}.Slot`,c}(`Primitive.${b}`),d=h.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return d.displayName=`Primitive.${b}`,{...a,[b]:d}},{}),aA=globalThis?.document?h.useLayoutEffect:()=>{},aB=h.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=h.useState(!1);aA(()=>f(!0),[]);let i=c||e&&globalThis?.document?.body;return i?n.createPortal((0,g.jsx)(az.div,{...d,ref:b}),i):null});aB.displayName="Portal";var aC=i[" useInsertionEffect ".trim().toString()]||Z;function aD({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,f,g]=function({defaultProp:a,onChange:b}){let[c,d]=h.useState(a),e=h.useRef(c),f=h.useRef(b);return aC(()=>{f.current=b},[b]),h.useEffect(()=>{e.current!==c&&(f.current?.(c),e.current=c)},[c,e]),[c,d,f]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:e;{let b=h.useRef(void 0!==a);h.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,h.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&g.current?.(c)}else f(b)},[i,a,f,g])]}Symbol("RADIX:SYNC_STATE");var aE=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});h.forwardRef((a,b)=>(0,g.jsx)(B.span,{...a,ref:b,style:{...aE,...a.style}})).displayName="VisuallyHidden";var aF=new WeakMap,aG=new WeakMap,aH={},aI=0,aJ=function(a){return a&&(a.host||aJ(a.parentNode))},aK=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=aJ(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});aH[c]||(aH[c]=new WeakMap);var f=aH[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(aF.get(a)||0)+1,j=(f.get(a)||0)+1;aF.set(a,i),f.set(a,j),g.push(a),1===i&&e&&aG.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),aI++,function(){g.forEach(function(a){var b=aF.get(a)-1,e=f.get(a)-1;aF.set(a,b),f.set(a,e),b||(aG.has(a)||a.removeAttribute(d),aG.delete(a)),e||a.removeAttribute(c)}),--aI||(aF=new WeakMap,aF=new WeakMap,aG=new WeakMap,aH={})}},aL=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live]"))),aK(d,e,c,"aria-hidden")):function(){return null}},aM=function(){return(aM=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function aN(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;var aO="right-scroll-bar-position",aP="width-before-scroll-bar";function aQ(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var aR="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,aS=new WeakMap;function aT(a){return a}var aU=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=aT),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=aM({async:!0,ssr:!1},a),e}(),aV=function(){},aW=h.forwardRef(function(a,b){var c,d,e,f,g=h.useRef(null),i=h.useState({onScrollCapture:aV,onWheelCapture:aV,onTouchMoveCapture:aV}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=aN(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[g,b],d=function(a){return c.forEach(function(b){return aQ(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,aR(function(){var a=aS.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||aQ(a,null)}),d.forEach(function(a){b.has(a)||aQ(a,e)})}aS.set(f,c)},[c]),f),A=aM(aM({},y),j);return h.createElement(h.Fragment,null,p&&h.createElement(r,{sideCar:aU,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:g,gapMode:x}),l?h.cloneElement(h.Children.only(m),aM(aM({},A),{ref:z})):h.createElement(void 0===w?"div":w,aM({},A,{className:n,ref:z}),m))});aW.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},aW.classNames={fullWidth:aP,zeroRight:aO};var aX=function(a){var b=a.sideCar,c=aN(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,aM({},c))};aX.isSideCarExport=!0;var aY=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},aZ=function(){var a=aY();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},a$=function(){var a=aZ();return function(b){return a(b.styles,b.dynamic),null}},a_={left:0,top:0,right:0,gap:0},a0=function(a){return parseInt(a||"",10)||0},a1=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[a0(c),a0(d),a0(e)]},a2=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return a_;var b=a1(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},a3=a$(),a4="data-scroll-locked",a5=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(a4,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(aO," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(aP," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(aO," .").concat(aO," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(aP," .").concat(aP," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(a4,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},a6=function(){var a=parseInt(document.body.getAttribute(a4)||"0",10);return isFinite(a)?a:0},a7=function(){h.useEffect(function(){return document.body.setAttribute(a4,(a6()+1).toString()),function(){var a=a6()-1;a<=0?document.body.removeAttribute(a4):document.body.setAttribute(a4,a.toString())}},[])},a8=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;a7();var f=h.useMemo(function(){return a2(e)},[e]);return h.createElement(a3,{styles:a5(f,!b,e,c?"":"!important")})},a9=!1;if("undefined"!=typeof window)try{var ba=Object.defineProperty({},"passive",{get:function(){return a9=!0,!0}});window.addEventListener("test",ba,ba),window.removeEventListener("test",ba,ba)}catch(a){a9=!1}var bb=!!a9&&{passive:!1},bc=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},bd=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),be(a,d)){var e=bf(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},be=function(a,b){return"v"===a?bc(b,"overflowY"):bc(b,"overflowX")},bf=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},bg=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=bf(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&be(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},bh=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},bi=function(a){return[a.deltaX,a.deltaY]},bj=function(a){return a&&"current"in a?a.current:a},bk=0,bl=[];let bm=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(bk++)[0],f=h.useState(a$)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(bj),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=bh(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=bd(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=bd(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return bg(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(bl.length&&bl[bl.length-1]===f){var c="deltaY"in a?bi(a):bh(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(bj).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=bh(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,bi(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,bh(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return bl.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,bb),document.addEventListener("touchmove",j,bb),document.addEventListener("touchstart",l,bb),function(){bl=bl.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,bb),document.removeEventListener("touchmove",j,bb),document.removeEventListener("touchstart",l,bb)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(a8,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},aU.useMedium(d),aX);var bn=h.forwardRef(function(a,b){return h.createElement(aW,aM({},a,{ref:b,sideCar:bm}))});bn.classNames=aW.classNames;var bo=[" ","Enter","ArrowUp","ArrowDown"],bp=[" ","Enter"],bq="Select",[br,bs,bt]=function(a){let b=a+"CollectionProvider",[c,d]=q(b),[e,f]=c(b,{collectionRef:{current:null},itemMap:new Map}),i=a=>{let{scope:b,children:c}=a,d=h.useRef(null),f=h.useRef(new Map).current;return(0,g.jsx)(e,{scope:b,itemMap:f,collectionRef:d,children:c})};i.displayName=b;let j=a+"CollectionSlot",k=u(j),l=h.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=t(b,f(j,c).collectionRef);return(0,g.jsx)(k,{ref:e,children:d})});l.displayName=j;let m=a+"CollectionItemSlot",n="data-radix-collection-item",o=u(m),p=h.forwardRef((a,b)=>{let{scope:c,children:d,...e}=a,i=h.useRef(null),j=t(b,i),k=f(m,c);return h.useEffect(()=>(k.itemMap.set(i,{ref:i,...e}),()=>void k.itemMap.delete(i))),(0,g.jsx)(o,{...{[n]:""},ref:j,children:d})});return p.displayName=m,[{Provider:i,Slot:l,ItemSlot:p},function(b){let c=f(a+"CollectionConsumer",b);return h.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${n}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},d]}(bq),[bu,bv]=q(bq,[bt,ag]),bw=ag(),[bx,by]=bu(bq),[bz,bA]=bu(bq),bB=a=>{let{__scopeSelect:b,children:c,open:d,defaultOpen:e,onOpenChange:f,value:i,defaultValue:j,onValueChange:k,dir:l,name:m,autoComplete:n,disabled:o,required:p,form:q}=a,r=bw(b),[s,t]=h.useState(null),[u,v]=h.useState(null),[w,x]=h.useState(!1),y=function(a){let b=h.useContext(A);return a||b||"ltr"}(l),[z,B]=aD({prop:d,defaultProp:e??!1,onChange:f,caller:bq}),[C,D]=aD({prop:i,defaultProp:j,onChange:k,caller:bq}),E=h.useRef(null),F=!s||q||!!s.closest("form"),[G,H]=h.useState(new Set),I=Array.from(G).map(a=>a.props.value).join(";");return(0,g.jsx)(aj,{...r,children:(0,g.jsxs)(bx,{required:p,scope:b,trigger:s,onTriggerChange:t,valueNode:u,onValueNodeChange:v,valueNodeHasChildren:w,onValueNodeHasChildrenChange:x,contentId:aa(),value:C,onValueChange:D,open:z,onOpenChange:B,dir:y,triggerPointerDownPosRef:E,disabled:o,children:[(0,g.jsx)(br.Provider,{scope:b,children:(0,g.jsx)(bz,{scope:a.__scopeSelect,onNativeOptionAdd:h.useCallback(a=>{H(b=>new Set(b).add(a))},[]),onNativeOptionRemove:h.useCallback(a=>{H(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),F?(0,g.jsxs)(cc,{"aria-hidden":!0,required:p,tabIndex:-1,name:m,autoComplete:n,value:C,onChange:a=>D(a.target.value),disabled:o,form:q,children:[void 0===C?(0,g.jsx)("option",{value:""}):null,Array.from(G)]},I):null]})})};bB.displayName=bq;var bC="SelectTrigger",bD=h.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:d=!1,...e}=a,f=bw(c),i=by(bC,c),j=i.disabled||d,k=t(b,i.onTriggerChange),l=bs(c),m=h.useRef("touch"),[n,o,q]=ce(a=>{let b=l().filter(a=>!a.disabled),c=b.find(a=>a.value===i.value),d=cf(b,a,c);void 0!==d&&i.onValueChange(d.value)}),r=a=>{j||(i.onOpenChange(!0),q()),a&&(i.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,g.jsx)(al,{asChild:!0,...f,children:(0,g.jsx)(B.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:j,"data-disabled":j?"":void 0,"data-placeholder":cd(i.value)?"":void 0,...e,ref:k,onClick:p(e.onClick,a=>{a.currentTarget.focus(),"mouse"!==m.current&&r(a)}),onPointerDown:p(e.onPointerDown,a=>{m.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(r(a),a.preventDefault())}),onKeyDown:p(e.onKeyDown,a=>{let b=""!==n.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||o(a.key),(!b||" "!==a.key)&&bo.includes(a.key)&&(r(),a.preventDefault())})})})});bD.displayName=bC;var bE="SelectValue",bF=h.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:h="",...i}=a,j=by(bE,c),{onValueNodeHasChildrenChange:k}=j,l=void 0!==f,m=t(b,j.onValueNodeChange);return Z(()=>{k(l)},[k,l]),(0,g.jsx)(B.span,{...i,ref:m,style:{pointerEvents:"none"},children:cd(j.value)?(0,g.jsx)(g.Fragment,{children:h}):f})});bF.displayName=bE;var bG=h.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,g.jsx)(B.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});bG.displayName="SelectIcon";var bH=a=>(0,g.jsx)(aB,{asChild:!0,...a});bH.displayName="SelectPortal";var bI="SelectContent",bJ=h.forwardRef((a,b)=>{let c=by(bI,a.__scopeSelect),[d,e]=h.useState();return(Z(()=>{e(new DocumentFragment)},[]),c.open)?(0,g.jsx)(bN,{...a,ref:b}):d?n.createPortal((0,g.jsx)(bK,{scope:a.__scopeSelect,children:(0,g.jsx)(br.Slot,{scope:a.__scopeSelect,children:(0,g.jsx)("div",{children:a.children})})}),d):null});bJ.displayName=bI;var[bK,bL]=bu(bI),bM=u("SelectContent.RemoveScroll"),bN=h.forwardRef((a,b)=>{let{__scopeSelect:c,position:d="item-aligned",onCloseAutoFocus:e,onEscapeKeyDown:f,onPointerDownOutside:i,side:j,sideOffset:k,align:l,alignOffset:m,arrowPadding:n,collisionBoundary:o,collisionPadding:q,sticky:r,hideWhenDetached:s,avoidCollisions:u,...v}=a,w=by(bI,c),[x,y]=h.useState(null),[z,A]=h.useState(null),B=t(b,a=>y(a)),[C,D]=h.useState(null),[E,G]=h.useState(null),H=bs(c),[K,L]=h.useState(!1),M=h.useRef(!1);h.useEffect(()=>{if(x)return aL(x)},[x]),h.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??J()),document.body.insertAdjacentElement("beforeend",a[1]??J()),I++,()=>{1===I&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),I--}},[]);let N=h.useCallback(a=>{let[b,...c]=H().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&z&&(z.scrollTop=0),c===d&&z&&(z.scrollTop=z.scrollHeight),c?.focus(),document.activeElement!==e))return},[H,z]),O=h.useCallback(()=>N([C,x]),[N,C,x]);h.useEffect(()=>{K&&O()},[K,O]);let{onOpenChange:P,triggerPointerDownPosRef:Q}=w;h.useEffect(()=>{if(x){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(Q.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(Q.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():x.contains(c.target)||P(!1),document.removeEventListener("pointermove",b),Q.current=null};return null!==Q.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[x,P,Q]),h.useEffect(()=>{let a=()=>P(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[P]);let[R,S]=ce(a=>{let b=H().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=cf(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),U=h.useCallback((a,b,c)=>{let d=!M.current&&!c;(void 0!==w.value&&w.value===b||d)&&(D(a),d&&(M.current=!0))},[w.value]),V=h.useCallback(()=>x?.focus(),[x]),W=h.useCallback((a,b,c)=>{let d=!M.current&&!c;(void 0!==w.value&&w.value===b||d)&&G(a)},[w.value]),X="popper"===d?bP:bO,Y=X===bP?{side:j,sideOffset:k,align:l,alignOffset:m,arrowPadding:n,collisionBoundary:o,collisionPadding:q,sticky:r,hideWhenDetached:s,avoidCollisions:u}:{};return(0,g.jsx)(bK,{scope:c,content:x,viewport:z,onViewportChange:A,itemRefCallback:U,selectedItem:C,onItemLeave:V,itemTextRefCallback:W,focusSelectedItem:O,selectedItemText:E,position:d,isPositioned:K,searchRef:R,children:(0,g.jsx)(bn,{as:bM,allowPinchZoom:!0,children:(0,g.jsx)(T,{asChild:!0,trapped:w.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:p(e,a=>{w.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,g.jsx)(F,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:i,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:(0,g.jsx)(X,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:a=>a.preventDefault(),...v,...Y,onPlaced:()=>L(!0),ref:B,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:p(v.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||S(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=H().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>N(b)),a.preventDefault()}})})})})})})});bN.displayName="SelectContentImpl";var bO=h.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:d,...e}=a,f=by(bI,c),i=bL(bI,c),[j,k]=h.useState(null),[l,m]=h.useState(null),n=t(b,a=>m(a)),p=bs(c),q=h.useRef(!1),r=h.useRef(!0),{viewport:s,selectedItem:u,selectedItemText:v,focusSelectedItem:w}=i,x=h.useCallback(()=>{if(f.trigger&&f.valueNode&&j&&l&&s&&u&&v){let a=f.trigger.getBoundingClientRect(),b=l.getBoundingClientRect(),c=f.valueNode.getBoundingClientRect(),e=v.getBoundingClientRect();if("rtl"!==f.dir){let d=e.left-b.left,f=c.left-d,g=a.left-f,h=a.width+g,i=Math.max(h,b.width),k=o(f,[10,Math.max(10,window.innerWidth-10-i)]);j.style.minWidth=h+"px",j.style.left=k+"px"}else{let d=b.right-e.right,f=window.innerWidth-c.right-d,g=window.innerWidth-a.right-f,h=a.width+g,i=Math.max(h,b.width),k=o(f,[10,Math.max(10,window.innerWidth-10-i)]);j.style.minWidth=h+"px",j.style.right=k+"px"}let g=p(),h=window.innerHeight-20,i=s.scrollHeight,k=window.getComputedStyle(l),m=parseInt(k.borderTopWidth,10),n=parseInt(k.paddingTop,10),r=parseInt(k.borderBottomWidth,10),t=m+n+i+parseInt(k.paddingBottom,10)+r,w=Math.min(5*u.offsetHeight,t),x=window.getComputedStyle(s),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=u.offsetHeight/2,C=m+n+(u.offsetTop+B);if(C<=A){let a=g.length>0&&u===g[g.length-1].ref.current;j.style.bottom="0px";let b=Math.max(h-A,B+(a?z:0)+(l.clientHeight-s.offsetTop-s.offsetHeight)+r);j.style.height=C+b+"px"}else{let a=g.length>0&&u===g[0].ref.current;j.style.top="0px";let b=Math.max(A,m+s.offsetTop+(a?y:0)+B);j.style.height=b+(t-C)+"px",s.scrollTop=C-A+s.offsetTop}j.style.margin="10px 0",j.style.minHeight=w+"px",j.style.maxHeight=h+"px",d?.(),requestAnimationFrame(()=>q.current=!0)}},[p,f.trigger,f.valueNode,j,l,s,u,v,f.dir,d]);Z(()=>x(),[x]);let[y,z]=h.useState();Z(()=>{l&&z(window.getComputedStyle(l).zIndex)},[l]);let A=h.useCallback(a=>{a&&!0===r.current&&(x(),w?.(),r.current=!1)},[x,w]);return(0,g.jsx)(bQ,{scope:c,contentWrapper:j,shouldExpandOnScrollRef:q,onScrollButtonChange:A,children:(0,g.jsx)("div",{ref:k,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:y},children:(0,g.jsx)(B.div,{...e,ref:n,style:{boxSizing:"border-box",maxHeight:"100%",...e.style}})})})});bO.displayName="SelectItemAlignedPosition";var bP=h.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,h=bw(c);return(0,g.jsx)(ap,{...h,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});bP.displayName="SelectPopperPosition";var[bQ,bR]=bu(bI,{}),bS="SelectViewport",bT=h.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:d,...e}=a,f=bL(bS,c),i=bR(bS,c),j=t(b,f.onViewportChange),k=h.useRef(0);return(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:d}),(0,g.jsx)(br.Slot,{scope:c,children:(0,g.jsx)(B.div,{"data-radix-select-viewport":"",role:"presentation",...e,ref:j,style:{position:"relative",flex:1,overflow:"hidden auto",...e.style},onScroll:p(e.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=i;if(d?.current&&c){let a=Math.abs(k.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}k.current=b.scrollTop})})})]})});bT.displayName=bS;var bU="SelectGroup",[bV,bW]=bu(bU);h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=aa();return(0,g.jsx)(bV,{scope:c,id:e,children:(0,g.jsx)(B.div,{role:"group","aria-labelledby":e,...d,ref:b})})}).displayName=bU;var bX="SelectLabel",bY=h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=bW(bX,c);return(0,g.jsx)(B.div,{id:e.id,...d,ref:b})});bY.displayName=bX;var bZ="SelectItem",[b$,b_]=bu(bZ),b0=h.forwardRef((a,b)=>{let{__scopeSelect:c,value:d,disabled:e=!1,textValue:f,...i}=a,j=by(bZ,c),k=bL(bZ,c),l=j.value===d,[m,n]=h.useState(f??""),[o,q]=h.useState(!1),r=t(b,a=>k.itemRefCallback?.(a,d,e)),s=aa(),u=h.useRef("touch"),v=()=>{e||(j.onValueChange(d),j.onOpenChange(!1))};if(""===d)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,g.jsx)(b$,{scope:c,value:d,disabled:e,textId:s,isSelected:l,onItemTextChange:h.useCallback(a=>{n(b=>b||(a?.textContent??"").trim())},[]),children:(0,g.jsx)(br.ItemSlot,{scope:c,value:d,disabled:e,textValue:m,children:(0,g.jsx)(B.div,{role:"option","aria-labelledby":s,"data-highlighted":o?"":void 0,"aria-selected":l&&o,"data-state":l?"checked":"unchecked","aria-disabled":e||void 0,"data-disabled":e?"":void 0,tabIndex:e?void 0:-1,...i,ref:r,onFocus:p(i.onFocus,()=>q(!0)),onBlur:p(i.onBlur,()=>q(!1)),onClick:p(i.onClick,()=>{"mouse"!==u.current&&v()}),onPointerUp:p(i.onPointerUp,()=>{"mouse"===u.current&&v()}),onPointerDown:p(i.onPointerDown,a=>{u.current=a.pointerType}),onPointerMove:p(i.onPointerMove,a=>{u.current=a.pointerType,e?k.onItemLeave?.():"mouse"===u.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:p(i.onPointerLeave,a=>{a.currentTarget===document.activeElement&&k.onItemLeave?.()}),onKeyDown:p(i.onKeyDown,a=>{(k.searchRef?.current===""||" "!==a.key)&&(bp.includes(a.key)&&v()," "===a.key&&a.preventDefault())})})})})});b0.displayName=bZ;var b1="SelectItemText",b2=h.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,...f}=a,i=by(b1,c),j=bL(b1,c),k=b_(b1,c),l=bA(b1,c),[m,o]=h.useState(null),p=t(b,a=>o(a),k.onItemTextChange,a=>j.itemTextRefCallback?.(a,k.value,k.disabled)),q=m?.textContent,r=h.useMemo(()=>(0,g.jsx)("option",{value:k.value,disabled:k.disabled,children:q},k.value),[k.disabled,k.value,q]),{onNativeOptionAdd:s,onNativeOptionRemove:u}=l;return Z(()=>(s(r),()=>u(r)),[s,u,r]),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(B.span,{id:k.textId,...f,ref:p}),k.isSelected&&i.valueNode&&!i.valueNodeHasChildren?n.createPortal(f.children,i.valueNode):null]})});b2.displayName=b1;var b3="SelectItemIndicator",b4=h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return b_(b3,c).isSelected?(0,g.jsx)(B.span,{"aria-hidden":!0,...d,ref:b}):null});b4.displayName=b3;var b5="SelectScrollUpButton",b6=h.forwardRef((a,b)=>{let c=bL(b5,a.__scopeSelect),d=bR(b5,a.__scopeSelect),[e,f]=h.useState(!1),i=t(b,d.onScrollButtonChange);return Z(()=>{if(c.viewport&&c.isPositioned){let a=function(){f(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,g.jsx)(b9,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});b6.displayName=b5;var b7="SelectScrollDownButton",b8=h.forwardRef((a,b)=>{let c=bL(b7,a.__scopeSelect),d=bR(b7,a.__scopeSelect),[e,f]=h.useState(!1),i=t(b,d.onScrollButtonChange);return Z(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;f(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,g.jsx)(b9,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});b8.displayName=b7;var b9=h.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:d,...e}=a,f=bL("SelectScrollButton",c),i=h.useRef(null),j=bs(c),k=h.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return h.useEffect(()=>()=>k(),[k]),Z(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,g.jsx)(B.div,{"aria-hidden":!0,...e,ref:b,style:{flexShrink:0,...e.style},onPointerDown:p(e.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(d,50))}),onPointerMove:p(e.onPointerMove,()=>{f.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(d,50))}),onPointerLeave:p(e.onPointerLeave,()=>{k()})})}),ca=h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,g.jsx)(B.div,{"aria-hidden":!0,...d,ref:b})});ca.displayName="SelectSeparator";var cb="SelectArrow";h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=bw(c),f=by(cb,c),h=bL(cb,c);return f.open&&"popper"===h.position?(0,g.jsx)(as,{...e,...d,ref:b}):null}).displayName=cb;var cc=h.forwardRef(({__scopeSelect:a,value:b,...c},d)=>{let e=h.useRef(null),f=t(d,e),i=function(a){let b=h.useRef({value:a,previous:a});return h.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(b);return h.useEffect(()=>{let a=e.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[i,b]),(0,g.jsx)(B.select,{...c,style:{...aE,...c.style},ref:f,defaultValue:b})});function cd(a){return""===a||void 0===a}function ce(a){let b=C(a),c=h.useRef(""),d=h.useRef(0),e=h.useCallback(a=>{let e=c.current+a;b(e),function a(b){c.current=b,window.clearTimeout(d.current),""!==b&&(d.current=window.setTimeout(()=>a(""),1e3))}(e)},[b]),f=h.useCallback(()=>{c.current="",window.clearTimeout(d.current)},[]);return h.useEffect(()=>()=>window.clearTimeout(d.current),[]),[c,e,f]}function cf(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}cc.displayName="SelectBubbleInput";var cg=c(23339);let ch=(0,cg.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),ci=(0,cg.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),cj=(0,cg.A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var ck=c(44943);let cl=h.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(bD,{ref:d,className:(0,ck.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,g.jsx)(bG,{asChild:!0,children:(0,g.jsx)(ch,{className:"h-4 w-4 opacity-50"})})]}));cl.displayName=bD.displayName;let cm=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(b6,{ref:c,className:(0,ck.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,g.jsx)(ci,{className:"h-4 w-4"})}));cm.displayName=b6.displayName;let cn=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(b8,{ref:c,className:(0,ck.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,g.jsx)(ch,{className:"h-4 w-4"})}));cn.displayName=b8.displayName;let co=h.forwardRef(({className:a,children:b,position:c="popper",...d},e)=>(0,g.jsx)(bH,{children:(0,g.jsxs)(bJ,{ref:e,className:(0,ck.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...d,children:[(0,g.jsx)(cm,{}),(0,g.jsx)(bT,{className:(0,ck.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,g.jsx)(cn,{})]})}));co.displayName=bJ.displayName,h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(bY,{ref:c,className:(0,ck.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=bY.displayName;let cp=h.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(b0,{ref:d,className:(0,ck.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,g.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(b4,{children:(0,g.jsx)(cj,{className:"h-4 w-4"})})}),(0,g.jsx)(b2,{children:b})]}));cp.displayName=b0.displayName,h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(ca,{ref:c,className:(0,ck.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=ca.displayName;var cq=c(13222),cr=c(42830);let cs=()=>{let[a,b]=(0,h.useState)({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""}),[c,d]=(0,h.useState)(!1),[e,f]=(0,h.useState)({}),i=c=>{let{name:d,value:g}=c.target;b({...a,[d]:g}),e[d]&&f({...e,[d]:""})},n=async c=>{if(c.preventDefault(),(()=>{let b={};return a.firstName.trim()||(b.firstName="First name is required"),a.lastName.trim()||(b.lastName="Last name is required"),a.email?/\S+@\S+\.\S+/.test(a.email)||(b.email="Email is invalid"):b.email="Email is required",a.phone?/^[0-9\s\-+()]*$/.test(a.phone)||(b.phone="Please enter a valid phone number"):b.phone="Phone number is required",a.inquiryType||(b.inquiryType="Please select an inquiry type"),a.message.trim()||(b.message="Message is required"),f(b),0===Object.keys(b).length})()){d(!0);try{let c=await cq.A.post(`${process.env.NEXT_PUBLIC_API_URL||"https://mail.shangriladistillery.com"}/api/contact`,a,{headers:{"Content-Type":"application/json"}});if(200===c.status)cr.toast.success("Your message has been sent successfully!"),b({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""});else throw Error("Failed to send message")}catch(a){console.error("Error sending message:",a),cr.toast.error("Failed to send message. Please try again later.")}finally{d(!1)}}};return(0,g.jsxs)("div",{className:"distillery-card p-8",children:[(0,g.jsx)("h2",{className:"text-2xl font-playfair font-bold text-amber-100 mb-6",children:"Send us a Message"}),(0,g.jsxs)("form",{className:"space-y-6",onSubmit:n,children:[(0,g.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(l.J,{htmlFor:"firstName",className:"text-amber-200",children:"First Name"}),(0,g.jsx)(k.p,{id:"firstName",name:"firstName",type:"text",value:a.firstName,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${e.firstName?"border-red-500":""}`,onChange:i}),e.firstName&&(0,g.jsx)("p",{className:"mt-1 text-sm text-red-400",children:e.firstName})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(l.J,{htmlFor:"lastName",className:"text-amber-200",children:"Last Name"}),(0,g.jsx)(k.p,{id:"lastName",name:"lastName",type:"text",value:a.lastName,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${e.lastName?"border-red-500":""}`,onChange:i}),e.lastName&&(0,g.jsx)("p",{className:"mt-1 text-sm text-red-400",children:e.lastName})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(l.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,g.jsx)(k.p,{id:"email",name:"email",type:"email",value:a.email,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${e.email?"border-red-500":""}`,onChange:i}),e.email&&(0,g.jsx)("p",{className:"mt-1 text-sm text-red-400",children:e.email})]}),(0,g.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(l.J,{htmlFor:"phone",className:"text-amber-200",children:"Phone Number"}),(0,g.jsx)(k.p,{id:"phone",name:"phone",type:"tel",value:a.phone,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${e.phone?"border-red-500":""}`,onChange:i,placeholder:"+****************"}),e.phone&&(0,g.jsx)("p",{className:"mt-1 text-sm text-red-400",children:e.phone})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(l.J,{htmlFor:"inquiryType",className:"text-amber-200",children:"Inquiry Type"}),(0,g.jsxs)(bB,{value:a.inquiryType,onValueChange:c=>{b({...a,inquiryType:c}),e.inquiryType&&f({...e,inquiryType:""})},children:[(0,g.jsx)(cl,{className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${e.inquiryType?"border-red-500":""}`,children:(0,g.jsx)(bF,{placeholder:"Select inquiry type"})}),(0,g.jsxs)(co,{className:"bg-stone-800 border-amber-500/30",children:[(0,g.jsx)(cp,{value:"general",children:"General Inquiry"}),(0,g.jsx)(cp,{value:"wholesale",children:"Wholesale Inquiry"}),(0,g.jsx)(cp,{value:"partnership",children:"Partnership"}),(0,g.jsx)(cp,{value:"press",children:"Press/Media"}),(0,g.jsx)(cp,{value:"other",children:"Other"})]})]}),e.inquiryType&&(0,g.jsx)("p",{className:"mt-1 text-sm text-red-400",children:e.inquiryType})]})]}),(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(l.J,{htmlFor:"subject",className:"text-amber-200",children:"Subject (Optional)"}),(0,g.jsx)(k.p,{id:"subject",name:"subject",type:"text",value:a.subject,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400",onChange:i})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(l.J,{htmlFor:"message",className:"text-amber-200",children:"Message"}),(0,g.jsx)(m.T,{id:"message",name:"message",rows:5,value:a.message,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${e.message?"border-red-500":""}`,onChange:i}),e.message&&(0,g.jsx)("p",{className:"mt-1 text-sm text-red-400",children:e.message})]})]}),(0,g.jsx)(j.$,{type:"submit",disabled:c,className:`w-full bg-amber-600 hover:bg-amber-700 text-white py-6 text-lg font-medium transition-colors duration-200 ${c?"opacity-70 cursor-not-allowed":""}`,children:c?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,g.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,g.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):"Send Message"})]})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29351:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,10521)),Promise.resolve().then(c.bind(c,75687))},29638:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n,metadata:()=>m});var d=c(75338),e=c(24169),f=c(47361);c(74515);let g=()=>(0,d.jsxs)("div",{className:"distillery-card p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-4",children:"Find Us"}),(0,d.jsx)("div",{className:"w-full h-64 rounded-lg overflow-hidden border border-amber-500/30",children:(0,d.jsx)("iframe",{title:"Shangrila Distillery Location",src:"https://www.google.com/maps?q=27.7172,85.3240&z=15&output=embed",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade"})})]});var h=c(80450),i=c(79148),j=c(81231),k=c(64723);let l=(0,c(4290).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),m={title:"Contact Us - Visit Our Distillery",description:"Get in touch with Shangrila Distillery for inquiries, partnerships, or to learn more about our premium spirits. Visit our facility in Chitwan, Nepal.",keywords:["Contact Shangrila Distillery","Nepal Distillery Contact","Visit Distillery Nepal","Shangrila Distillery Address","Premium Spirits Inquiry"],openGraph:{title:"Contact Shangrila Distillery | Visit Us",description:"Get in touch with our team for inquiries, partnerships, or to learn more about our premium spirits.",url:"https://shangriladistillery.com/contact",images:[{url:"/lovable-uploads/favicon-shangrila.png",width:1200,height:630,alt:"Contact Shangrila Distillery"}]},alternates:{canonical:"https://shangriladistillery.com/contact"}};function n(){return(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(e.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Visit Us"}),(0,d.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Get in touch with our team for inquiries, partnerships, or to learn more about our premium spirits."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)("div",{className:"distillery-card p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)(i.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Email"}),(0,d.jsx)("p",{className:"distillery-text",children:"<EMAIL>"})]})]})}),(0,d.jsx)("div",{className:"distillery-card p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)(j.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Phone"}),(0,d.jsx)("p",{className:"distillery-text",children:"+977-1-4528118"}),(0,d.jsx)("p",{className:"distillery-text",children:"WhatsApp: +977 1-4528118"})]})]})}),(0,d.jsxs)("div",{className:"distillery-card p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)(k.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Head Office Address"}),(0,d.jsxs)("p",{className:"distillery-text",children:["Pipalbot dillibazar-29",(0,d.jsx)("br",{}),"kathmandu nepal"]})]})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)(k.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Factory Location"}),(0,d.jsxs)("p",{className:"distillery-text",children:["Brahmanagar, Rapti Nagarpalika-9",(0,d.jsx)("br",{}),"Chitwan, Nepal"]})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-amber-600 to-amber-500 rounded-lg p-6 text-stone-900",children:[(0,d.jsx)(l,{className:"h-8 w-8 text-stone-900 mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold mb-2",children:"Business Hours"}),(0,d.jsxs)("p",{className:"font-crimson",children:["Sunday - Friday: 9:00 AM - 6:00 PM",(0,d.jsx)("br",{}),"Saturday: Closed",(0,d.jsx)("br",{})]})]}),(0,d.jsx)(g,{})]}),(0,d.jsx)(f.default,{})]})]}),(0,d.jsx)(h.A,{})]})]})}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47361:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/shangrila/src/components/ContactForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/shangrila/src/components/ContactForm.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68254:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(21124),e=c(38301),f=c(44943);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},69519:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,47361)),Promise.resolve().then(c.bind(c,24169))},74075:a=>{"use strict";a.exports=require("zlib")},76848:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,29638)),"/home/<USER>/shangrila/src/app/contact/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/shangrila/src/app/contact/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/contact/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,482,373,828,529,744],()=>b(b.s=76848));module.exports=c})();