(()=>{var a={};a.id=571,a.ids=[571],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8010:(a,b,c)=>{"use strict";c.d(b,{$:()=>m});var d=c(75338),e=c(74515),f=e.forwardRef((a,b)=>{let{children:c,...f}=a,h=e.Children.toArray(c),j=h.find(i);if(j){let a=j.props.children,c=h.map(b=>b!==j?b:e.Children.count(a)>1?e.Children.only(null):e.isValidElement(a)?a.props.children:null);return(0,d.jsx)(g,{...f,ref:b,children:e.isValidElement(a)?e.cloneElement(a,void 0,c):null})}return(0,d.jsx)(g,{...f,ref:b,children:c})});f.displayName="Slot";var g=e.forwardRef((a,b)=>{let{children:c,...d}=a;if(e.isValidElement(c)){let a=function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(c);return e.cloneElement(c,{...function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{f(...a),e(...a)}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props),ref:b?function(...a){return b=>a.forEach(a=>{"function"==typeof a?a(b):null!=a&&(a.current=b)})}(b,a):a})}return e.Children.count(c)>1?e.Children.only(null):null});g.displayName="SlotClone";var h=({children:a})=>(0,d.jsx)(d.Fragment,{children:a});function i(a){return e.isValidElement(a)&&a.type===h}var j=c(86281),k=c(70673);let l=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},h)=>{let i=e?f:"button";return(0,d.jsx)(i,{className:(0,k.cn)(l({variant:b,size:c,className:a})),ref:h,...g})});m.displayName="Button"},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14577:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u,metadata:()=>t});var d=c(75338),e=c(24169);let f=()=>(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Heritage Collection"}),(0,d.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Discover our premium collection of spirits, each crafted with precision and passion in the heart of Nepal's majestic Himalayas."})]});var g=c(70009),h=c(48343),i=c(66111),j=c(8010);let k=({product:a,index:b})=>(0,d.jsxs)("div",{className:`relative overflow-hidden rounded-3xl bg-gradient-to-r ${a.bgColor} min-h-[600px] shadow-2xl`,children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-black/30"}),(0,d.jsxs)("div",{className:"relative z-10 grid lg:grid-cols-2 gap-12 items-center p-12 lg:p-16",children:[(0,d.jsxs)("div",{className:b%2==0?"order-1":"order-2",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)("span",{className:`text-lg font-crimson font-semibold ${a.textColor}/80`,children:"CRAFTED FROM HERITAGE"})}),(0,d.jsxs)("h2",{className:`text-5xl md:text-5xl font-playfair font-bold ${a.textColor} mb-8 leading-tight`,children:["Shangrila ",(0,d.jsx)("span",{className:"italic text-amber-400",children:"Collection"})]}),(0,d.jsx)("h3",{className:`text-3xl font-playfair font-bold ${a.textColor} mb-6`,children:a.name}),(0,d.jsxs)("div",{className:`text-xl font-crimson ${a.textColor}/90 mb-2`,children:[a.type," • ",a.alcohol," ABV"]}),(0,d.jsx)("p",{className:`text-lg font-crimson ${a.textColor}/80 leading-relaxed mb-8 max-w-2xl`,children:a.description}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h4",{className:`text-lg font-playfair font-semibold ${a.textColor} mb-3`,children:"Tasting Notes"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.notes.map(b=>(0,d.jsx)("span",{className:`px-4 py-2 bg-white/20 backdrop-blur-sm ${a.textColor} rounded-full text-sm border border-white/30 font-crimson`,children:b},b))})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,d.jsxs)(j.$,{variant:"outline",className:`${a.textColor} border-white/50 hover:bg-white/20 px-6 py-3 font-crimson font-semibold`,children:["Explore Legacy ",(0,d.jsx)(g.A,{className:"ml-2 h-4 w-4"})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)(j.$,{size:"icon",variant:"outline",className:`${a.textColor} border-white/50 hover:bg-white/20 rounded-full`,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{size:"icon",variant:"outline",className:`${a.textColor} border-white/50 hover:bg-white/20 rounded-full`,children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})]})]})]}),(0,d.jsx)("div",{className:b%2==0?"order-2":"order-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-80 h-96 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto border border-white/20 p-8",children:(0,d.jsx)("img",{src:a.image,alt:`Shangrila Distillery ${a.name}`,className:"max-w-full max-h-full object-contain filter drop-shadow-2xl"})}),(0,d.jsx)("div",{className:"absolute -top-4 -right-4 w-8 h-8 bg-amber-400 rounded-full opacity-60"}),(0,d.jsx)("div",{className:"absolute -bottom-4 -left-4 w-6 h-6 bg-amber-400 rounded-full opacity-40"})]})})]})]}),l=(0,c(4290).A)("Grape",[["path",{d:"M22 5V2l-5.89 5.89",key:"1eenpo"}],["circle",{cx:"16.6",cy:"15.89",r:"3",key:"xjtalx"}],["circle",{cx:"8.11",cy:"7.4",r:"3",key:"u2fv6i"}],["circle",{cx:"12.35",cy:"11.65",r:"3",key:"i6i8g7"}],["circle",{cx:"13.91",cy:"5.85",r:"3",key:"6ye0dv"}],["circle",{cx:"18.15",cy:"10.09",r:"3",key:"snx9no"}],["circle",{cx:"6.56",cy:"13.2",r:"3",key:"17x4xg"}],["circle",{cx:"10.8",cy:"17.44",r:"3",key:"1hogw9"}],["circle",{cx:"5",cy:"19",r:"3",key:"1sn6vo"}]]);var m=c(38708),n=c(66731);let o=()=>{let a=[{icon:l,title:"Premium Ingredients",description:"Sourced from the finest local and international suppliers"},{icon:m.A,title:"Modern Distillation",description:"State-of-the-art equipment ensuring consistency and purity"},{icon:n.A,title:"Master Blending",description:"Expert craftsmanship in every bottle"}];return(0,d.jsxs)("div",{className:"mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-12 text-center",children:"Our Heritage Crafting Process"}),(0,d.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:a.map(a=>(0,d.jsxs)("div",{className:"distillery-card p-8 text-center hover:scale-105 transition-all duration-300",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-amber-400"})}),(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-4",children:a.title}),(0,d.jsx)("p",{className:"distillery-text font-crimson",children:a.description})]},a.title))})]})},p=()=>(0,d.jsxs)("div",{className:"distillery-card p-12 mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Awards & Recognition"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 text-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-playfair font-bold text-amber-400 mb-2",children:"ISO"}),(0,d.jsx)("div",{className:"distillery-text font-crimson",children:"Certified Quality Standards"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-playfair font-bold text-amber-400 mb-2",children:"5★"}),(0,d.jsx)("div",{className:"distillery-text font-crimson",children:"International Quality Rating"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-playfair font-bold text-amber-400 mb-2",children:"100%"}),(0,d.jsx)("div",{className:"distillery-text font-crimson",children:"Customer Satisfaction"})]})]})]}),q=()=>(0,d.jsxs)("div",{className:"copper-gradient rounded-3xl p-12 text-center text-stone-900",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold mb-6",children:"More Exciting Heritage Awaits"}),(0,d.jsx)("p",{className:"text-lg font-crimson max-w-2xl mx-auto mb-8",children:"We're continuously developing new products to expand our heritage collection. Stay tuned for more premium spirits that will showcase the finest of Himalayan craftsmanship and tradition."}),(0,d.jsx)(j.$,{className:"bg-stone-900 hover:bg-stone-800 text-white px-8 py-4 text-lg font-crimson font-semibold",children:"Join Our Heritage Journey"})]});var r=c(80450);let s=[{name:"Tejas Gold",description:"A refined expression of craftsmanship, Tejas Gold is a smooth, premium blended whiskey crafted with precision for discerning palates. Made from carefully selected malts and neutral spirits, it embodies a perfect balance of character and smoothness. Aged and blended to perfection, it delivers gentle notes of oak, subtle spices, and a mellow, lingering finish.\n\nBlend Type: Premium style blend\n\nABV: 34.24%\n\nTasting Notes: Smooth entry, light toasted oak, hints of caramel and soft spice\n\nBest Enjoyed: Neat, on the rocks, or with your favorite mixer\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nDistiller’s Note: A celebration of heritage and modern finesse",category:"Whiskey",type:"Premium Blend",alcohol:"34.24%",notes:["Smooth entry","light toasted oak","hints of caramel and soft spice"],bgColor:"from-amber-900 to-orange-800",textColor:"text-amber-100",image:"/lovable-uploads/tejas gold.jpeg"},{name:"Tejas Black",description:"Bold, distinctive, and layered with character — Tejas Black is our signature peated blend crafted for whiskey lovers who seek depth and smokiness. Infused with gently smoked malts and matured to perfection, it offers a warm, smoky finish balanced by rich malt sweetness.\n\nBlend Type: Peated Premium Blend\n\nABV: 34.24%\n\nTasting Notes: Smoky aroma, charred oak, hints of cocoa and dried fruit, lingering peaty finish\n\nBest Enjoyed: Neat or with a splash of water to open the smoky layers\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nDistiller’s Note: For those who appreciate the darker, bolder side of whiskey",category:"Whiskey",type:"Peated Whiskey",alcohol:"34.24%",notes:["Smoky aroma","charred oak","hints of cocoa and dried fruit","lingering peaty finish"],bgColor:"from-slate-900 to-gray-800",textColor:"text-slate-100",image:"/lovable-uploads/tejas black.png"},{name:"Reef",description:"Exceptionally smooth and crystal clear, Reef is a premium grain spirit crafted through meticulous distillation and blending. Made from select grains, it offers a clean, crisp character with a soft finish — perfect for refined sipping or creative mixing.\n\nType: Premium Grain Spirit\n\nABV: 34.24%\n\nTasting Notes: Smooth and neutral palate, clean finish, light grain aroma\n\nBest Enjoyed: Over ice, with soda, or in cocktails\n\nCrafted and Bottled by: Shangrila Distillery, Nepal\n\nCraftsman’s Note: Clarity in every drop — where purity meets precision.",category:"Vodka",type:"Premium Grain Spirit",alcohol:"34.24%",notes:["Smooth and neutral palate","clean finish","light grain aroma"],bgColor:"from-blue-900 to-cyan-800",textColor:"text-blue-100",image:"/lovable-uploads/reef.png"},{name:"LOD",description:"Bold. Smooth. Unapologetically refined.\nLOD – Lord of Drinks is a high-strength premium vodka, distilled from select grains and crafted for those who lead the night. Its crisp clarity and silky texture make it the perfect base for cocktails or a commanding pour on its own.\n\nType: Premium Grain Vodka\n\nABV: 40%\n\nTasting Notes: Clean, full-bodied, slight pepper finish with silky smoothness\n\nBest Enjoyed: Straight, chilled, or in signature cocktails\n\nDistilled and Bottled by: Shangrila Distillery, Nepal\n\nTagline: For Those Who Rule the Night",category:"Vodka",type:"Premium Vodka",alcohol:"40%",notes:["Clean","full-bodied","slight pepper finish with silky smoothness"],bgColor:"from-purple-900 to-indigo-800",textColor:"text-purple-100",image:"/lovable-uploads/lod.png"},{name:"Phantom",description:"Smooth, deep, and elusive — Phantom is a finely balanced blended whiskey crafted for those who appreciate subtle power. With hints of toasted oak, soft spices, and a clean finish, Phantom delivers a refined experience wrapped in mystery.\n\nType: Blended Whiskey\n\nABV: 34.23%\n\nTasting Notes: Subtle oak, warm spice, mellow grain sweetness, smooth finish\n\nBest Enjoyed: Neat, on the rocks, or with a splash of water\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nTagline: The Spirit You Feel, Not See.",category:"Whiskey",type:"Blended Whiskey",alcohol:"34.23%",notes:["Subtle oak","warm spice","mellow grain sweetness","smooth finish"],bgColor:"from-yellow-900 to-yellow-700",textColor:"text-yellow-100",image:"/lovable-uploads/phantom.jpeg"},{name:"Royal Distinction",description:"Crafted for royalty, Royal Distinction is the pinnacle of elegance and strength. This ultra-premium whiskey is masterfully blended using the finest aged malts and select grain spirits to deliver a bold, velvety experience with a long, regal finish.\n\nType: Ultra Premium Blend\n\nABV: 42.8%\n\nTasting Notes: Rich oak, layers of caramel and spice, smooth lingering finish\n\nBest Enjoyed: Neat or with a single ice cube to unlock its depth\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nTagline: A Mark of Legacy. A Taste of Prestige.",category:"Blends",type:"Ultra Premium Blend",alcohol:"42.8%",notes:["Rich oak","layers of caramel and spice","smooth lingering finish"],bgColor:"from-red-900 to-pink-800",textColor:"text-red-100",image:"/lovable-uploads/royal.png"},{name:"Lynx Vodka",description:"Sharp. Smooth. Unleashed.\nLynx Vodka is a bold, high-proof spirit distilled from select grains and crafted for those who move with confidence. With its clean, crisp profile and silky texture, it’s designed to elevate every pour — from cocktails to straight shots.\n\nType: Premium Grain Vodka\n\nABV: 40%\n\nTasting Notes: Bold yet smooth, icy clean finish, whisper of grain warmth\n\nBest Enjoyed: Chilled, in cocktails, or as a signature shot\n\nDistilled and Bottled by: Shangrila Distillery, Nepal\n\nTagline: Hunt the Night. Drink with Precision.",category:"Vodka",type:"Premium Grain Vodka",alcohol:"40%",notes:["Bold yet smooth","icy clean finish","whisper of grain warmth"],bgColor:"from-gray-900 to-slate-800",textColor:"text-gray-100",image:"/lovable-uploads/lynxvodka.png"}],t={title:"Premium Heritage Collection - Whiskey, Vodka & Spirits",description:"Explore Shangrila Distillery's premium heritage collection. Discover our finest whiskeys, vodkas, and craft spirits made with traditional methods in Nepal.",keywords:["Shangrila Products","Nepal Whiskey Collection","Premium Vodka Nepal","Tejas Gold","Tejas Black","Reef Vodka","LOD Vodka","Craft Spirits Nepal"],openGraph:{title:"Premium Heritage Collection | Shangrila Distillery",description:"Discover our premium collection of spirits, each crafted with precision and passion in the heart of Nepal's majestic Himalayas.",url:"https://shangriladistillery.com/products",images:[{url:"/lovable-uploads/tejas gold.jpeg",width:1200,height:630,alt:"Shangrila Distillery Premium Heritage Collection"}]},alternates:{canonical:"https://shangriladistillery.com/products"}};function u(){return(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(e.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,d.jsx)(f,{}),(0,d.jsx)("div",{className:"space-y-12 mb-16",children:s.map((a,b)=>(0,d.jsx)(k,{product:a,index:b},a.name))}),(0,d.jsx)(o,{}),(0,d.jsx)(p,{}),(0,d.jsx)(q,{})]}),(0,d.jsx)(r.A,{})]})]})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},38708:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},39939:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,24169))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},54011:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,75687))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66731:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70009:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},82356:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,14577)),"/home/<USER>/shangrila/src/app/products/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/shangrila/src/app/products/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/products/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,251,583,925,744],()=>b(b.s=82356));module.exports=c})();