(()=>{var a={};a.id=170,a.ids=[170],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30599:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(75338),e=c(74515),f=c.n(e),g=c(75553),h=c(24169),i=c(80450);let j={title:"Prajanna Raj Adhikari – Managing Director at Shangrila Distillery",description:"Meet <PERSON>rajanna Raj Adhikari, Managing Director of Shangrila Distillery, Nepal's premier spirits producer. Learn about his leadership, vision, and contribution to Nepal's finest whiskey and vodka.",keywords:["Prajanna Raj Adhikari","Shangrila Distillery","Nepal Whiskey","Nepal Vodka","Managing Director","Leadership","Premium Spirits","Nepal Distillery","Craft Spirits"],openGraph:{title:"Prajanna Raj Adhikari – Managing Director at Shangrila Distillery",description:"Discover the leadership of Prajanna Raj Adhikari at Shangrila Distillery, shaping Nepal's finest whiskey and vodka with global expertise and vision.",url:"https://shangriladistillery.com/leadership/prajanna-raj-adhikari",images:[{url:"/lovable-uploads/prashanna.png",width:1200,height:630,alt:"Prajanna Raj Adhikari - Managing Director Shangrila Distillery"}],type:"profile"},twitter:{card:"summary_large_image",title:"Prajanna Raj Adhikari – Shangrila Distillery Managing Director",description:"Meet Prajanna Raj Adhikari, the visionary leader behind Nepal's premier distillery, crafting world-class spirits.",images:["/lovable-uploads/prashanna.png"]},alternates:{canonical:"https://shangriladistillery.com/leadership/prajanna-raj-adhikari"},other:{"profile:first_name":"Prajanna Raj","profile:last_name":"Adhikari","profile:username":"prajannarajadhikari"}};function k(){return(0,d.jsxs)(f().Fragment,{children:[(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Person",name:"Prajanna Raj Adhikari",jobTitle:"Managing Director",description:"Managing Director of Shangrila Distillery, Nepal's premier spirits producer. Expert in premium whiskey and vodka production with international experience.",worksFor:{"@type":"Organization",name:"Shangrila Distillery",url:"https://shangriladistillery.com",description:"Nepal's premier craft spirits distillery"},image:"https://shangriladistillery.com/lovable-uploads/prashanna.png",url:"https://shangriladistillery.com/leadership/prajanna-raj-adhikari",alumniOf:[{"@type":"EducationalOrganization",name:"Federation University",description:"Bachelor of Business Administration"},{"@type":"EducationalOrganization",name:"Victoria University",description:"Master of Business Administration"}],nationality:"Nepali",knowsAbout:["Distillery Management","Premium Spirits","Business Administration","International Trade","Whiskey Production","Vodka Production"],sameAs:["https://www.linkedin.com/in/prajannarajadhikari","https://shangriladistillery.com"]})}}),(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(h.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20",children:[(0,d.jsxs)("div",{className:"text-center mb-12 sm:mb-16",children:[(0,d.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4 sm:mb-6",children:"Prajanna Raj Adhikari – Managing Director at Shangrila Distillery"}),(0,d.jsx)("p",{className:"text-xl text-amber-300 font-semibold mb-2",children:"Leadership Excellence in Nepal's Premium Spirits Industry"}),(0,d.jsx)("p",{className:"text-amber-100/80 font-crimson",children:"Nepal's Leading Craft Distillery"})]}),(0,d.jsxs)("div",{className:"bg-white/10 rounded-xl shadow-lg border border-amber-400/30 mb-16 max-w-5xl mx-auto overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-amber-900/30 to-amber-800/30 p-6 sm:p-8",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-6",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("img",{src:"/lovable-uploads/prashanna.png",alt:"Prajanna Raj Adhikari Managing Director Shangrila Distillery Nepal Premium Spirits Leadership",className:"w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 rounded-lg border-4 border-amber-400/60 shadow-xl object-cover bg-white/5"})}),(0,d.jsxs)("div",{className:"text-center sm:text-left",children:[(0,d.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl font-playfair font-bold text-amber-100 mb-2",children:"Prajanna Raj Adhikari"}),(0,d.jsx)("p",{className:"text-lg sm:text-xl text-amber-200 font-semibold mb-2",children:"Managing Director & Visionary Leader"}),(0,d.jsx)("p",{className:"text-amber-100/80 font-crimson text-lg",children:"Shangrila Distillery - Nepal's Premier Craft Spirits"})]})]})}),(0,d.jsxs)("div",{className:"p-6 sm:p-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mb-4",children:"Professional Background"}),(0,d.jsx)("div",{className:"space-y-4 text-amber-100/90 font-crimson leading-relaxed text-lg",children:(0,d.jsxs)("p",{className:"text-justify",children:[(0,d.jsx)("strong",{children:"Prajanna Raj Adhikari"}),", the Managing Director of ",(0,d.jsx)("strong",{children:"Shangrila Distillery"}),", is a dynamic and forward-thinking leader with a deep-rooted passion for the liquor and beverage industry. He began his journey in Australia, where he gained invaluable exposure to world-class distilleries and participated in advanced blending workshops, sharpening his technical and operational understanding of premium spirits production."]})})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mb-4",children:"Education & Qualifications"}),(0,d.jsx)("div",{className:"space-y-4 text-amber-100/90 font-crimson leading-relaxed text-lg",children:(0,d.jsxs)("p",{className:"text-justify",children:["Prajanna Raj Adhikari holds a ",(0,d.jsx)("strong",{children:"Bachelor of Business Administration from Federation University"})," and a ",(0,d.jsx)("strong",{children:"Master of Business Administration from Victoria University, Australia"}),"—credentials that reflect his strong academic foundation in global business and management."]})})]}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mb-4",children:"Vision & Leadership at Shangrila Distillery"}),(0,d.jsxs)("div",{className:"space-y-4 text-amber-100/90 font-crimson leading-relaxed text-lg",children:[(0,d.jsxs)("p",{className:"text-justify",children:["With a strategic vision and entrepreneurial spirit, Prajanna Raj Adhikari is committed to establishing ",(0,d.jsx)("strong",{children:"Shangrila Distillery as a premier name in Nepal's liquor industry"}),". His ambition is not only to develop a robust domestic presence but also to position Shangrila as a competitive exporter of high-quality spirits. Under his leadership, the distillery aims to contribute significantly to Nepal's economic landscape by enhancing the country's export footprint and reputation for excellence in craftsmanship."]}),(0,d.jsxs)("p",{className:"text-justify",children:["Regarded as a rising figure in the industry, ",(0,d.jsx)("strong",{children:"Prajanna Raj Adhikari"})," brings clarity of purpose, global perspective, and relentless drive—qualities that are shaping ",(0,d.jsx)("strong",{children:"Shangrila Distillery's"})," path to becoming a distinguished force in the world of spirits."]})]})]}),(0,d.jsx)("div",{className:"mt-8 pt-6 border-t border-amber-400/20",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center",children:[(0,d.jsxs)("div",{className:"text-center sm:text-right font-semibold text-amber-200 font-crimson text-lg mb-4 sm:mb-0",children:["— Managing Director",(0,d.jsx)("br",{}),"Shangrila Distillery"]}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("a",{href:"https://www.linkedin.com/in/prajannarajadhikari",target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 bg-amber-400/10 hover:bg-amber-400/20 text-amber-100 px-4 py-2 rounded-lg transition-all duration-300 border border-amber-400/30 hover:border-amber-400/50",children:[(0,d.jsx)("span",{className:"font-bold text-blue-400",children:"in"}),(0,d.jsx)("span",{className:"font-crimson",children:"LinkedIn Profile"}),(0,d.jsx)(g.A,{className:"h-4 w-4"})]})})]})})]})]}),(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-6",children:"Leadership Vision"}),(0,d.jsx)("p",{className:"text-xl text-amber-100/80 max-w-3xl mx-auto font-crimson leading-relaxed mb-8",children:'"At Shangrila Distillery, our leadership is dedicated to crafting a legacy of quality, innovation, and responsibility. We believe in empowering our people, respecting tradition, and embracing the future with courage and creativity."'}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8",children:[(0,d.jsxs)("div",{className:"bg-white/10 border border-amber-400/20 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-amber-200 mb-3",children:"Integrity"}),(0,d.jsx)("p",{className:"text-amber-100/80 font-crimson text-sm",children:"We uphold the highest standards of honesty and transparency in all our endeavors."})]}),(0,d.jsxs)("div",{className:"bg-white/10 border border-amber-400/20 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-amber-200 mb-3",children:"Innovation"}),(0,d.jsx)("p",{className:"text-amber-100/80 font-crimson text-sm",children:"We foster creativity and embrace new ideas to lead the industry forward."})]}),(0,d.jsxs)("div",{className:"bg-white/10 border border-amber-400/20 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-amber-200 mb-3",children:"Excellence"}),(0,d.jsx)("p",{className:"text-amber-100/80 font-crimson text-sm",children:"We are committed to delivering the finest products and experiences to our customers."})]}),(0,d.jsxs)("div",{className:"bg-white/10 border border-amber-400/20 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-amber-200 mb-3",children:"Responsibility"}),(0,d.jsx)("p",{className:"text-amber-100/80 font-crimson text-sm",children:"We champion responsible drinking and sustainable business practices."})]})]})]}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("a",{href:"/our-leadership",className:"inline-block bg-amber-400 hover:bg-amber-500 text-stone-900 font-bold px-8 py-4 rounded-lg shadow-lg transition-all duration-300 font-crimson text-lg",children:"← Back to Leadership Team"})})]}),(0,d.jsx)(i.A,{})]})]})]})}},33873:a=>{"use strict";a.exports=require("path")},39939:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,24169))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44924:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},48343:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},51181:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},54011:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,75687))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64723:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},65169:(a,b,c)=>{let{createProxy:d}=c(39893);a.exports=d("/home/<USER>/shangrila/node_modules/next/dist/client/app-dir/link.js")},66111:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},75553:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},79148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},81231:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},83156:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["leadership",{children:["prajanna-raj-adhikari",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,30599)),"/home/<USER>/shangrila/src/app/leadership/prajanna-raj-adhikari/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/shangrila/src/app/leadership/prajanna-raj-adhikari/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/leadership/prajanna-raj-adhikari/page",pathname:"/leadership/prajanna-raj-adhikari",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/leadership/prajanna-raj-adhikari/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,482,373,529,744],()=>b(b.s=83156));module.exports=c})();