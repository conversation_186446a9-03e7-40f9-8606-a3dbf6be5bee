(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8010:(a,b,c)=>{"use strict";c.d(b,{$:()=>m});var d=c(75338),e=c(74515),f=e.forwardRef((a,b)=>{let{children:c,...f}=a,h=e.Children.toArray(c),j=h.find(i);if(j){let a=j.props.children,c=h.map(b=>b!==j?b:e.Children.count(a)>1?e.Children.only(null):e.isValidElement(a)?a.props.children:null);return(0,d.jsx)(g,{...f,ref:b,children:e.isValidElement(a)?e.cloneElement(a,void 0,c):null})}return(0,d.jsx)(g,{...f,ref:b,children:c})});f.displayName="Slot";var g=e.forwardRef((a,b)=>{let{children:c,...d}=a;if(e.isValidElement(c)){let a=function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(c);return e.cloneElement(c,{...function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{f(...a),e(...a)}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props),ref:b?function(...a){return b=>a.forEach(a=>{"function"==typeof a?a(b):null!=a&&(a.current=b)})}(b,a):a})}return e.Children.count(c)>1?e.Children.only(null):null});g.displayName="SlotClone";var h=({children:a})=>(0,d.jsx)(d.Fragment,{children:a});function i(a){return e.isValidElement(a)&&a.type===h}var j=c(86281),k=c(70673);let l=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},h)=>{let i=e?f:"button";return(0,d.jsx)(i,{className:(0,k.cn)(l({variant:b,size:c,className:a})),ref:h,...g})});m.displayName="Button"},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27752:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,65040)),Promise.resolve().then(c.bind(c,75687))},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},37412:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,88421)),"/home/<USER>/shangrila/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}],I=["/home/<USER>/shangrila/src/app/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},39614:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Handshake",[["path",{d:"m11 17 2 2a1 1 0 1 0 3-3",key:"efffak"}],["path",{d:"m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4",key:"9pr0kb"}],["path",{d:"m21 3 1 11h-2",key:"1tisrp"}],["path",{d:"M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3",key:"1uvwmv"}],["path",{d:"M3 4h8",key:"1ep09j"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44924:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},48343:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},51181:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63234:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},64723:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},65040:(a,b,c)=>{"use strict";c.d(b,{default:()=>O});var d=c(21124),e=c(38301);function f(a){return"[object Object]"===Object.prototype.toString.call(a)||Array.isArray(a)}function g(a,b){let c=Object.keys(a),d=Object.keys(b);return c.length===d.length&&JSON.stringify(Object.keys(a.breakpoints||{}))===JSON.stringify(Object.keys(b.breakpoints||{}))&&c.every(c=>{let d=a[c],e=b[c];return"function"==typeof d?`${d}`==`${e}`:f(d)&&f(e)?g(d,e):d===e})}function h(a){return a.concat().sort((a,b)=>a.name>b.name?1:-1).map(a=>a.options)}function i(a){return"number"==typeof a}function j(a){return"string"==typeof a}function k(a){return"boolean"==typeof a}function l(a){return"[object Object]"===Object.prototype.toString.call(a)}function m(a){return Math.abs(a)}function n(a){return Math.sign(a)}function o(a){return s(a).map(Number)}function p(a){return a[q(a)]}function q(a){return Math.max(0,a.length-1)}function r(a,b=0){return Array.from(Array(a),(a,c)=>b+c)}function s(a){return Object.keys(a)}function t(a,b){return void 0!==b.MouseEvent&&a instanceof b.MouseEvent}function u(){let a=[],b={add:function(c,d,e,f={passive:!0}){let g;return"addEventListener"in c?(c.addEventListener(d,e,f),g=()=>c.removeEventListener(d,e,f)):(c.addListener(e),g=()=>c.removeListener(e)),a.push(g),b},clear:function(){a=a.filter(a=>a())}};return b}function v(a=0,b=0){let c=m(a-b);function d(c){return c<a||c>b}return{length:c,max:b,min:a,constrain:function(c){return d(c)?c<a?a:b:c},reachedAny:d,reachedMax:function(a){return a>b},reachedMin:function(b){return b<a},removeOffset:function(a){return c?a-c*Math.ceil((a-b)/c):a}}}function w(a){let b=a;function c(a){return i(a)?a:a.get()}return{get:function(){return b},set:function(a){b=c(a)},add:function(a){b+=c(a)},subtract:function(a){b-=c(a)}}}function x(a,b){let c="x"===a.scroll?function(a){return`translate3d(${a}px,0px,0px)`}:function(a){return`translate3d(0px,${a}px,0px)`},d=b.style,e=!1;return{clear:function(){!e&&(d.transform="",b.getAttribute("style")||b.removeAttribute("style"))},to:function(b){e||(d.transform=c(a.direction(b)))},toggleActive:function(a){e=!a}}}let y={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function z(a,b,c){let d,e,f,g,h,A=a.ownerDocument,B=A.defaultView,C=function(a){function b(a,b){return function a(b,c){return[b,c].reduce((b,c)=>(s(c).forEach(d=>{let e=b[d],f=c[d],g=l(e)&&l(f);b[d]=g?a(e,f):f}),b),{})}(a,b||{})}return{mergeOptions:b,optionsAtMedia:function(c){let d=c.breakpoints||{},e=s(d).filter(b=>a.matchMedia(b).matches).map(a=>d[a]).reduce((a,c)=>b(a,c),{});return b(c,e)},optionsMediaQueries:function(b){return b.map(a=>s(a.breakpoints||{})).reduce((a,b)=>a.concat(b),[]).map(a.matchMedia)}}}(B),D=(h=[],{init:function(a,b){return(h=b.filter(({options:a})=>!1!==C.optionsAtMedia(a).active)).forEach(b=>b.init(a,C)),b.reduce((a,b)=>Object.assign(a,{[b.name]:b}),{})},destroy:function(){h=h.filter(a=>a.destroy())}}),E=u(),F=function(){let a,b={},c={init:function(b){a=b},emit:function(d){return(b[d]||[]).forEach(b=>b(a,d)),c},off:function(a,d){return b[a]=(b[a]||[]).filter(a=>a!==d),c},on:function(a,d){return b[a]=(b[a]||[]).concat([d]),c},clear:function(){b={}}};return c}(),{mergeOptions:G,optionsAtMedia:H,optionsMediaQueries:I}=C,{on:J,off:K,emit:L}=F,M=!1,N=G(y,z.globalOptions),O=G(N),P=[];function Q(b,c){if(M)return;O=H(N=G(N,b)),P=c||P;let{container:h,slides:l}=O;f=(j(h)?a.querySelector(h):h)||a.children[0];let y=j(l)?f.querySelectorAll(l):l;g=[].slice.call(y||f.children),d=function b(c){let d=function(a,b,c,d,e,f,g){let h,l,{align:y,axis:z,direction:A,startIndex:B,loop:C,duration:D,dragFree:E,dragThreshold:F,inViewThreshold:G,slidesToScroll:H,skipSnaps:I,containScroll:J,watchResize:K,watchSlides:L,watchDrag:M,watchFocus:N}=f,O={measure:function(a){let{offsetTop:b,offsetLeft:c,offsetWidth:d,offsetHeight:e}=a;return{top:b,right:c+d,bottom:b+e,left:c,width:d,height:e}}},P=O.measure(b),Q=c.map(O.measure),R=function(a,b){let c="rtl"===b,d="y"===a,e=!d&&c?-1:1;return{scroll:d?"y":"x",cross:d?"x":"y",startEdge:d?"top":c?"right":"left",endEdge:d?"bottom":c?"left":"right",measureSize:function(a){let{height:b,width:c}=a;return d?b:c},direction:function(a){return a*e}}}(z,A),S=R.measureSize(P),T={measure:function(a){return a/100*S}},U=function(a,b){let c={start:function(){return 0},center:function(a){return(b-a)/2},end:function(a){return b-a}};return{measure:function(d,e){return j(a)?c[a](d):a(b,d,e)}}}(y,S),V=!C&&!!J,{slideSizes:W,slideSizesWithGaps:X,startGap:Y,endGap:Z}=function(a,b,c,d,e,f){let{measureSize:g,startEdge:h,endEdge:i}=a,j=c[0]&&e,k=function(){if(!j)return 0;let a=c[0];return m(b[h]-a[h])}(),l=j?parseFloat(f.getComputedStyle(p(d)).getPropertyValue(`margin-${i}`)):0,n=c.map(g),o=c.map((a,b,c)=>{let d=b===q(c);return b?d?n[b]+l:c[b+1][h]-a[h]:n[b]+k}).map(m);return{slideSizes:n,slideSizesWithGaps:o,startGap:k,endGap:l}}(R,P,Q,c,C||!!J,e),$=function(a,b,c,d,e,f,g,h,j){let{startEdge:k,endEdge:l,direction:n}=a,r=i(c);return{groupSlides:function(a){return r?o(a).filter(a=>a%c==0).map(b=>a.slice(b,b+c)):a.length?o(a).reduce((c,i,j)=>{let o=p(c)||0,r=i===q(a),s=e[k]-f[o][k],t=e[k]-f[i][l],u=d||0!==o?0:n(g),v=m(t-(!d&&r?n(h):0)-(s+u));return j&&v>b+2&&c.push(i),r&&c.push(a.length),c},[]).map((b,c,d)=>{let e=Math.max(d[c-1]||0);return a.slice(e,b)}):[]}}}(R,S,H,C,P,Q,Y,Z,0),{snaps:_,snapsAligned:aa}=function(a,b,c,d,e){let{startEdge:f,endEdge:g}=a,{groupSlides:h}=e,i=h(d).map(a=>p(a)[g]-a[0][f]).map(m).map(b.measure),j=d.map(a=>c[f]-a[f]).map(a=>-m(a)),k=h(j).map(a=>a[0]).map((a,b)=>a+i[b]);return{snaps:j,snapsAligned:k}}(R,U,P,Q,$),ab=-p(_)+p(X),{snapsContained:ac,scrollContainLimit:ad}=function(a,b,c,d,e){let f=v(-b+a,0),g=c.map((a,b)=>{let{min:d,max:e}=f,g=f.constrain(a),h=b===q(c);return b?h||function(a,b){return 1>m(a-b)}(d,g)?d:function(a,b){return 1>m(a-b)}(e,g)?e:g:e}).map(a=>parseFloat(a.toFixed(3))),h=function(){let a=g[0],b=p(g);return v(g.lastIndexOf(a),g.indexOf(b)+1)}();return{snapsContained:function(){if(b<=a+2)return[f.max];if("keepSnaps"===d)return g;let{min:c,max:e}=h;return g.slice(c,e)}(),scrollContainLimit:h}}(S,ab,aa,J,0),ae=V?ac:aa,{limit:af}=function(a,b,c){let d=b[0];return{limit:v(c?d-a:p(b),d)}}(ab,ae,C),ag=function a(b,c,d){let{constrain:e}=v(0,b),f=b+1,g=h(c);function h(a){return d?m((f+a)%f):e(a)}function i(){return a(b,g,d)}let j={get:function(){return g},set:function(a){return g=h(a),j},add:function(a){return i().set(g+a)},clone:i};return j}(q(ae),B,C),ah=ag.clone(),ai=o(c),aj=function(a,b,c,d){let e=u(),f=1e3/60,g=null,h=0,i=0;function j(a){if(!i)return;g||(g=a);let e=a-g;for(g=a,h+=e;h>=f;)c(f),h-=f;d(h/f),i&&b.requestAnimationFrame(j)}function k(){b.cancelAnimationFrame(i),g=null,h=0,i=0}return{init:function(){e.add(a,"visibilitychange",()=>{a.hidden&&(g=null,h=0)})},destroy:function(){k(),e.clear()},start:function(){i||(i=b.requestAnimationFrame(j))},stop:k,update:()=>c(f),render:d}}(d,e,a=>(({dragHandler:a,scrollBody:b,scrollBounds:c,options:{loop:d}},e)=>{d||c.constrain(a.pointerDown()),b.seek(e)})(ax,a),a=>(({scrollBody:a,translate:b,location:c,offsetLocation:d,scrollLooper:e,slideLooper:f,dragHandler:g,animation:h,eventHandler:i,scrollBounds:j,options:{loop:k}},l)=>{let m=a.settled(),n=!j.shouldConstrain(),o=k?m:m&&n;o&&!g.pointerDown()&&(h.stop(),i.emit("settle")),o||i.emit("scroll");let p=c.get()*l+am.get()*(1-l);d.set(p),k&&(e.loop(a.direction()),f.loop()),b.to(d.get())})(ax,a)),ak=ae[ag.get()],al=w(ak),am=w(ak),an=w(ak),ao=w(ak),ap=function(a,b,c,d,e,f){let g=0,h=0,i=e,j=.68,k=a.get(),l=0;function o(a){return i=a,q}function p(a){return j=a,q}let q={direction:function(){return h},duration:function(){return i},velocity:function(){return g},seek:function(b){let e=b/1e3,f=i*e,m=d.get()-a.get(),o=0;return i?(c.set(a),g+=m/f,g*=j,k+=g,a.add(g*e),o=k-l):(g=0,c.set(d),a.set(d),o=m),h=n(o),l=k,q},settled:function(){return .001>m(d.get()-b.get())},useBaseFriction:function(){return p(.68)},useBaseDuration:function(){return o(e)},useFriction:p,useDuration:o};return q}(al,an,am,ao,D,.68),aq=function(a,b,c,d,e){let{reachedAny:f,removeOffset:g,constrain:h}=d;function i(a){return a.concat().sort((a,b)=>m(a)-m(b))[0]}function j(b,d){let e=[b,b+c,b-c];if(!a)return b;if(!d)return i(e);let f=e.filter(a=>n(a)===d);return f.length?i(f):p(e)-c}return{byDistance:function(c,d){let i=e.get()+c,{index:k,distance:l}=function(c){let d=a?g(c):h(c),{index:e}=b.map((a,b)=>({diff:j(a-d,0),index:b})).sort((a,b)=>m(a.diff)-m(b.diff))[0];return{index:e,distance:d}}(i),n=!a&&f(i);if(!d||n)return{index:k,distance:c};let o=c+j(b[k]-l,0);return{index:k,distance:o}},byIndex:function(a,c){let d=j(b[a]-e.get(),c);return{index:a,distance:d}},shortcut:j}}(C,ae,ab,af,ao),ar=function(a,b,c,d,e,f,g){function h(e){let h=e.distance,i=e.index!==b.get();f.add(h),h&&(d.duration()?a.start():(a.update(),a.render(1),a.update())),i&&(c.set(b.get()),b.set(e.index),g.emit("select"))}return{distance:function(a,b){h(e.byDistance(a,b))},index:function(a,c){let d=b.clone().set(a);h(e.byIndex(d.get(),c))}}}(aj,ag,ah,ap,aq,ao,g),as=function(a){let{max:b,length:c}=a;return{get:function(a){return c?-((a-b)/c):0}}}(af),at=u(),au=function(a,b,c,d){let e,f={},g=null,h=null,i=!1;return{init:function(){e=new IntersectionObserver(a=>{i||(a.forEach(a=>{f[b.indexOf(a.target)]=a}),g=null,h=null,c.emit("slidesInView"))},{root:a.parentElement,threshold:d}),b.forEach(a=>e.observe(a))},destroy:function(){e&&e.disconnect(),i=!0},get:function(a=!0){if(a&&g)return g;if(!a&&h)return h;let b=s(f).reduce((b,c)=>{let d=parseInt(c),{isIntersecting:e}=f[d];return(a&&e||!a&&!e)&&b.push(d),b},[]);return a&&(g=b),a||(h=b),b}}}(b,c,g,G),{slideRegistry:av}=function(a,b,c,d,e,f){let{groupSlides:g}=e,{min:h,max:i}=d;return{slideRegistry:function(){let d=g(f);return 1===c.length?[f]:a&&"keepSnaps"!==b?d.slice(h,i).map((a,b,c)=>{let d=b===q(c);return b?d?r(q(f)-p(c)[0]+1,p(c)[0]):a:r(p(c[0])+1)}):d}()}}(V,J,ae,ad,$,ai),aw=function(a,b,c,d,e,f,g,h){let j={passive:!0,capture:!0},l=0;function m(a){"Tab"===a.code&&(l=new Date().getTime())}return{init:function(n){h&&(f.add(document,"keydown",m,!1),b.forEach((b,m)=>{f.add(b,"focus",b=>{(k(h)||h(n,b))&&function(b){if(new Date().getTime()-l>10)return;g.emit("slideFocusStart"),a.scrollLeft=0;let f=c.findIndex(a=>a.includes(b));i(f)&&(e.useDuration(0),d.index(f,0),g.emit("slideFocus"))}(m)},j)}))}}}(a,c,av,ar,ap,at,g,N),ax={ownerDocument:d,ownerWindow:e,eventHandler:g,containerRect:P,slideRects:Q,animation:aj,axis:R,dragHandler:function(a,b,c,d,e,f,g,h,i,j,l,o,p,q,r,s,w,x,y){let{cross:z,direction:A}=a,B=["INPUT","SELECT","TEXTAREA"],C={passive:!1},D=u(),E=u(),F=v(50,225).constrain(q.measure(20)),G={mouse:300,touch:400},H={mouse:500,touch:600},I=r?43:25,J=!1,K=0,L=0,M=!1,N=!1,O=!1,P=!1;function Q(a){if(!t(a,d)&&a.touches.length>=2)return R(a);let b=f.readPoint(a),c=f.readPoint(a,z),g=m(b-K),i=m(c-L);if(!N&&!P&&(!a.cancelable||!(N=g>i)))return R(a);let k=f.pointerMove(a);g>s&&(O=!0),j.useFriction(.3).useDuration(.75),h.start(),e.add(A(k)),a.preventDefault()}function R(a){let b=l.byDistance(0,!1).index!==o.get(),c=f.pointerUp(a)*(r?H:G)[P?"mouse":"touch"],d=function(a,b){let c=o.add(-1*n(a)),d=l.byDistance(a,!r).distance;return r||m(a)<F?d:w&&b?.5*d:l.byIndex(c.get(),0).distance}(A(c),b),e=function(a,b){var c,d;if(0===a||0===b||m(a)<=m(b))return 0;let e=(c=m(a),d=m(b),m(c-d));return m(e/a)}(c,d);N=!1,M=!1,E.clear(),j.useDuration(I-10*e).useFriction(.68+e/50),i.distance(d,!r),P=!1,p.emit("pointerUp")}function S(a){O&&(a.stopPropagation(),a.preventDefault(),O=!1)}return{init:function(a){y&&D.add(b,"dragstart",a=>a.preventDefault(),C).add(b,"touchmove",()=>void 0,C).add(b,"touchend",()=>void 0).add(b,"touchstart",h).add(b,"mousedown",h).add(b,"touchcancel",R).add(b,"contextmenu",R).add(b,"click",S,!0);function h(h){(k(y)||y(a,h))&&function(a){let h=t(a,d);if((P=h,O=r&&h&&!a.buttons&&J,J=m(e.get()-g.get())>=2,!h||0===a.button)&&!function(a){let b=a.nodeName||"";return B.includes(b)}(a.target)){M=!0,f.pointerDown(a),j.useFriction(0).useDuration(0),e.set(g);let d=P?c:b;E.add(d,"touchmove",Q,C).add(d,"touchend",R).add(d,"mousemove",Q,C).add(d,"mouseup",R),K=f.readPoint(a),L=f.readPoint(a,z),p.emit("pointerDown")}}(h)}},destroy:function(){D.clear(),E.clear()},pointerDown:function(){return M}}}(R,a,d,e,ao,function(a,b){let c,d;function e(a){return a.timeStamp}function f(c,d){let e=d||a.scroll,f=`client${"x"===e?"X":"Y"}`;return(t(c,b)?c:c.touches[0])[f]}return{pointerDown:function(a){return c=a,d=a,f(a)},pointerMove:function(a){let b=f(a)-f(d),g=e(a)-e(c)>170;return d=a,g&&(c=a),b},pointerUp:function(a){if(!c||!d)return 0;let b=f(d)-f(c),g=e(a)-e(c),h=e(a)-e(d)>170,i=b/g;return g&&!h&&m(i)>.1?i:0},readPoint:f}}(R,e),al,aj,ar,ap,aq,ag,g,T,E,F,I,0,M),eventStore:at,percentOfView:T,index:ag,indexPrevious:ah,limit:af,location:al,offsetLocation:an,previousLocation:am,options:f,resizeHandler:function(a,b,c,d,e,f,g){let h,i,j=[a].concat(d),l=[],n=!1;function o(a){return e.measureSize(g.measure(a))}return{init:function(e){f&&(i=o(a),l=d.map(o),h=new ResizeObserver(c=>{(k(f)||f(e,c))&&function(c){for(let f of c){if(n)return;let c=f.target===a,g=d.indexOf(f.target),h=c?i:l[g];if(m(o(c?a:d[g])-h)>=.5){e.reInit(),b.emit("resize");break}}}(c)}),c.requestAnimationFrame(()=>{j.forEach(a=>h.observe(a))}))},destroy:function(){n=!0,h&&h.disconnect()}}}(b,g,e,c,R,K,O),scrollBody:ap,scrollBounds:function(a,b,c,d,e){let f=e.measure(10),g=e.measure(50),h=v(.1,.99),i=!1;function j(){return!i&&!!a.reachedAny(c.get())&&!!a.reachedAny(b.get())}return{shouldConstrain:j,constrain:function(e){if(!j())return;let i=a.reachedMin(b.get())?"min":"max",k=m(a[i]-b.get()),l=c.get()-b.get(),n=h.constrain(k/g);c.subtract(l*n),!e&&m(l)<f&&(c.set(a.constrain(c.get())),d.useDuration(25).useBaseFriction())},toggleActive:function(a){i=!a}}}(af,an,ao,ap,T),scrollLooper:function(a,b,c,d){let{reachedMin:e,reachedMax:f}=v(b.min+.1,b.max+.1);return{loop:function(b){if(!(1===b?f(c.get()):-1===b&&e(c.get())))return;let g=-1*b*a;d.forEach(a=>a.add(g))}}}(ab,af,an,[al,an,am,ao]),scrollProgress:as,scrollSnapList:ae.map(as.get),scrollSnaps:ae,scrollTarget:aq,scrollTo:ar,slideLooper:function(a,b,c,d,e,f,g,h,i){let j=o(e),k=o(e).reverse(),l=p(n(k,g[0]),c,!1).concat(p(n(j,b-g[0]-1),-c,!0));function m(a,b){return a.reduce((a,b)=>a-e[b],b)}function n(a,b){return a.reduce((a,c)=>m(a,b)>0?a.concat([c]):a,[])}function p(e,g,j){let k=f.map((a,c)=>({start:a-d[c]+.5+g,end:a+b-.5+g}));return e.map(b=>{let d=j?0:-c,e=j?c:0,f=k[b][j?"end":"start"];return{index:b,loopPoint:f,slideLocation:w(-1),translate:x(a,i[b]),target:()=>h.get()>f?d:e}})}return{canLoop:function(){return l.every(({index:a})=>.1>=m(j.filter(b=>b!==a),b))},clear:function(){l.forEach(a=>a.translate.clear())},loop:function(){l.forEach(a=>{let{target:b,translate:c,slideLocation:d}=a,e=b();e!==d.get()&&(c.to(e),d.set(e))})},loopPoints:l}}(R,S,ab,W,X,_,ae,an,c),slideFocus:aw,slidesHandler:(l=!1,{init:function(a){L&&(h=new MutationObserver(b=>{!l&&(k(L)||L(a,b))&&function(b){for(let c of b)if("childList"===c.type){a.reInit(),g.emit("slidesChanged");break}}(b)})).observe(b,{childList:!0})},destroy:function(){h&&h.disconnect(),l=!0}}),slidesInView:au,slideIndexes:ai,slideRegistry:av,slidesToScroll:$,target:ao,translate:x(R,b)};return ax}(a,f,g,A,B,c,F);return c.loop&&!d.slideLooper.canLoop()?b(Object.assign({},c,{loop:!1})):d}(O),I([N,...P.map(({options:a})=>a)]).forEach(a=>E.add(a,"change",R)),O.active&&(d.translate.to(d.location.get()),d.animation.init(),d.slidesInView.init(),d.slideFocus.init(V),d.eventHandler.init(V),d.resizeHandler.init(V),d.slidesHandler.init(V),d.options.loop&&d.slideLooper.loop(),f.offsetParent&&g.length&&d.dragHandler.init(V),e=D.init(V,P))}function R(a,b){let c=U();S(),Q(G({startIndex:c},a),b),F.emit("reInit")}function S(){d.dragHandler.destroy(),d.eventStore.clear(),d.translate.clear(),d.slideLooper.clear(),d.resizeHandler.destroy(),d.slidesHandler.destroy(),d.slidesInView.destroy(),d.animation.destroy(),D.destroy(),E.clear()}function T(a,b,c){O.active&&!M&&(d.scrollBody.useBaseFriction().useDuration(!0===b?0:O.duration),d.scrollTo.index(a,c||0))}function U(){return d.index.get()}let V={canScrollNext:function(){return d.index.add(1).get()!==U()},canScrollPrev:function(){return d.index.add(-1).get()!==U()},containerNode:function(){return f},internalEngine:function(){return d},destroy:function(){M||(M=!0,E.clear(),S(),F.emit("destroy"),F.clear())},off:K,on:J,emit:L,plugins:function(){return e},previousScrollSnap:function(){return d.indexPrevious.get()},reInit:R,rootNode:function(){return a},scrollNext:function(a){T(d.index.add(1).get(),a,-1)},scrollPrev:function(a){T(d.index.add(-1).get(),a,1)},scrollProgress:function(){return d.scrollProgress.get(d.location.get())},scrollSnapList:function(){return d.scrollSnapList},scrollTo:T,selectedScrollSnap:U,slideNodes:function(){return g},slidesInView:function(){return d.slidesInView.get()},slidesNotInView:function(){return d.slidesInView.get(!1)}};return Q(b,c),setTimeout(()=>F.emit("init"),0),V}function A(a={},b=[]){let c=(0,e.useRef)(a),d=(0,e.useRef)(b),[f,i]=(0,e.useState)(),[j,k]=(0,e.useState)(),l=(0,e.useCallback)(()=>{f&&f.reInit(c.current,d.current)},[f]);return(0,e.useEffect)(()=>{g(c.current,a)||(c.current=a,l())},[a,l]),(0,e.useEffect)(()=>{!function(a,b){if(a.length!==b.length)return!1;let c=h(a),d=h(b);return c.every((a,b)=>g(a,d[b]))}(d.current,b)&&(d.current=b,l())},[b,l]),(0,e.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&j){z.globalOptions=A.globalOptions;let a=z(j,c.current,d.current);return i(a),()=>a.destroy()}i(void 0)},[j,i]),[k,f]}z.globalOptions=void 0,A.globalOptions=void 0;var B=c(23339);let C=(0,B.A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),D=(0,B.A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var E=c(44943),F=c(35284);let G=e.createContext(null);function H(){let a=e.useContext(G);if(!a)throw Error("useCarousel must be used within a <Carousel />");return a}let I=e.forwardRef(({orientation:a="horizontal",opts:b,setApi:c,plugins:f,className:g,children:h,...i},j)=>{let[k,l]=A({...b,axis:"horizontal"===a?"x":"y"},f),[m,n]=e.useState(!1),[o,p]=e.useState(!1),q=e.useCallback(a=>{a&&(n(a.canScrollPrev()),p(a.canScrollNext()))},[]),r=e.useCallback(()=>{l?.scrollPrev()},[l]),s=e.useCallback(()=>{l?.scrollNext()},[l]),t=e.useCallback(a=>{"ArrowLeft"===a.key?(a.preventDefault(),r()):"ArrowRight"===a.key&&(a.preventDefault(),s())},[r,s]);return e.useEffect(()=>{l&&c&&c(l)},[l,c]),e.useEffect(()=>{if(l)return q(l),l.on("reInit",q),l.on("select",q),()=>{l?.off("select",q)}},[l,q]),(0,d.jsx)(G.Provider,{value:{carouselRef:k,api:l,opts:b,orientation:a||(b?.axis==="y"?"vertical":"horizontal"),scrollPrev:r,scrollNext:s,canScrollPrev:m,canScrollNext:o},children:(0,d.jsx)("div",{ref:j,onKeyDownCapture:t,className:(0,E.cn)("relative",g),role:"region","aria-roledescription":"carousel",...i,children:h})})});I.displayName="Carousel";let J=e.forwardRef(({className:a,...b},c)=>{let{carouselRef:e,orientation:f}=H();return(0,d.jsx)("div",{ref:e,className:"overflow-hidden",children:(0,d.jsx)("div",{ref:c,className:(0,E.cn)("flex","horizontal"===f?"-ml-4":"-mt-4 flex-col",a),...b})})});J.displayName="CarouselContent";let K=e.forwardRef(({className:a,...b},c)=>{let{orientation:e}=H();return(0,d.jsx)("div",{ref:c,role:"group","aria-roledescription":"slide",className:(0,E.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===e?"pl-4":"pt-4",a),...b})});K.displayName="CarouselItem";let L=e.forwardRef(({className:a,variant:b="outline",size:c="icon",...e},f)=>{let{orientation:g,scrollPrev:h,canScrollPrev:i}=H();return(0,d.jsxs)(F.$,{ref:f,variant:b,size:c,className:(0,E.cn)("absolute  h-8 w-8 rounded-full","horizontal"===g?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!i,onClick:h,...e,children:[(0,d.jsx)(C,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Previous slide"})]})});L.displayName="CarouselPrevious";let M=e.forwardRef(({className:a,variant:b="outline",size:c="icon",...e},f)=>{let{orientation:g,scrollNext:h,canScrollNext:i}=H();return(0,d.jsxs)(F.$,{ref:f,variant:b,size:c,className:(0,E.cn)("absolute h-8 w-8 rounded-full","horizontal"===g?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!i,onClick:h,...e,children:[(0,d.jsx)(D,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Next slide"})]})});M.displayName="CarouselNext";let N=[{name:"Tejas Gold",description:"A refined expression of craftsmanship, Tejas Gold is a smooth, premium blended whiskey crafted with precision for discerning palates. Made from carefully selected malts and neutral spirits, it embodies a perfect balance of character and smoothness. Aged and blended to perfection, it delivers gentle notes of oak, subtle spices, and a mellow, lingering finish.\n\nBlend Type: Premium style blend\n\nABV: 34.24%\n\nTasting Notes: Smooth entry, light toasted oak, hints of caramel and soft spice\n\nBest Enjoyed: Neat, on the rocks, or with your favorite mixer\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nDistiller’s Note: A celebration of heritage and modern finesse",category:"Whiskey",type:"Premium Blend",alcohol:"34.24%",notes:["Smooth entry","light toasted oak","hints of caramel and soft spice"],bgColor:"from-amber-900 to-orange-800",textColor:"text-amber-100",image:"/lovable-uploads/tejas gold.jpeg"},{name:"Tejas Black",description:"Bold, distinctive, and layered with character — Tejas Black is our signature peated blend crafted for whiskey lovers who seek depth and smokiness. Infused with gently smoked malts and matured to perfection, it offers a warm, smoky finish balanced by rich malt sweetness.\n\nBlend Type: Peated Premium Blend\n\nABV: 34.24%\n\nTasting Notes: Smoky aroma, charred oak, hints of cocoa and dried fruit, lingering peaty finish\n\nBest Enjoyed: Neat or with a splash of water to open the smoky layers\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nDistiller’s Note: For those who appreciate the darker, bolder side of whiskey",category:"Whiskey",type:"Peated Whiskey",alcohol:"34.24%",notes:["Smoky aroma","charred oak","hints of cocoa and dried fruit","lingering peaty finish"],bgColor:"from-slate-900 to-gray-800",textColor:"text-slate-100",image:"/lovable-uploads/tejas black.png"},{name:"Reef",description:"Exceptionally smooth and crystal clear, Reef is a premium grain spirit crafted through meticulous distillation and blending. Made from select grains, it offers a clean, crisp character with a soft finish — perfect for refined sipping or creative mixing.\n\nType: Premium Grain Spirit\n\nABV: 34.24%\n\nTasting Notes: Smooth and neutral palate, clean finish, light grain aroma\n\nBest Enjoyed: Over ice, with soda, or in cocktails\n\nCrafted and Bottled by: Shangrila Distillery, Nepal\n\nCraftsman’s Note: Clarity in every drop — where purity meets precision.",category:"Vodka",type:"Premium Grain Spirit",alcohol:"34.24%",notes:["Smooth and neutral palate","clean finish","light grain aroma"],bgColor:"from-blue-900 to-cyan-800",textColor:"text-blue-100",image:"/lovable-uploads/reef.png"},{name:"LOD",description:"Bold. Smooth. Unapologetically refined.\nLOD – Lord of Drinks is a high-strength premium vodka, distilled from select grains and crafted for those who lead the night. Its crisp clarity and silky texture make it the perfect base for cocktails or a commanding pour on its own.\n\nType: Premium Grain Vodka\n\nABV: 40%\n\nTasting Notes: Clean, full-bodied, slight pepper finish with silky smoothness\n\nBest Enjoyed: Straight, chilled, or in signature cocktails\n\nDistilled and Bottled by: Shangrila Distillery, Nepal\n\nTagline: For Those Who Rule the Night",category:"Vodka",type:"Premium Vodka",alcohol:"40%",notes:["Clean","full-bodied","slight pepper finish with silky smoothness"],bgColor:"from-purple-900 to-indigo-800",textColor:"text-purple-100",image:"/lovable-uploads/lod.png"},{name:"Phantom",description:"Smooth, deep, and elusive — Phantom is a finely balanced blended whiskey crafted for those who appreciate subtle power. With hints of toasted oak, soft spices, and a clean finish, Phantom delivers a refined experience wrapped in mystery.\n\nType: Blended Whiskey\n\nABV: 34.23%\n\nTasting Notes: Subtle oak, warm spice, mellow grain sweetness, smooth finish\n\nBest Enjoyed: Neat, on the rocks, or with a splash of water\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nTagline: The Spirit You Feel, Not See.",category:"Whiskey",type:"Blended Whiskey",alcohol:"34.23%",notes:["Subtle oak","warm spice","mellow grain sweetness","smooth finish"],bgColor:"from-yellow-900 to-yellow-700",textColor:"text-yellow-100",image:"/lovable-uploads/phantom.jpeg"},{name:"Royal Distinction",description:"Crafted for royalty, Royal Distinction is the pinnacle of elegance and strength. This ultra-premium whiskey is masterfully blended using the finest aged malts and select grain spirits to deliver a bold, velvety experience with a long, regal finish.\n\nType: Ultra Premium Blend\n\nABV: 42.8%\n\nTasting Notes: Rich oak, layers of caramel and spice, smooth lingering finish\n\nBest Enjoyed: Neat or with a single ice cube to unlock its depth\n\nBlended and Bottled by: Shangrila Distillery, Nepal\n\nTagline: A Mark of Legacy. A Taste of Prestige.",category:"Blends",type:"Ultra Premium Blend",alcohol:"42.8%",notes:["Rich oak","layers of caramel and spice","smooth lingering finish"],bgColor:"from-red-900 to-pink-800",textColor:"text-red-100",image:"/lovable-uploads/royal.png"},{name:"Lynx Vodka",description:"Sharp. Smooth. Unleashed.\nLynx Vodka is a bold, high-proof spirit distilled from select grains and crafted for those who move with confidence. With its clean, crisp profile and silky texture, it’s designed to elevate every pour — from cocktails to straight shots.\n\nType: Premium Grain Vodka\n\nABV: 40%\n\nTasting Notes: Bold yet smooth, icy clean finish, whisper of grain warmth\n\nBest Enjoyed: Chilled, in cocktails, or as a signature shot\n\nDistilled and Bottled by: Shangrila Distillery, Nepal\n\nTagline: Hunt the Night. Drink with Precision.",category:"Vodka",type:"Premium Grain Vodka",alcohol:"40%",notes:["Bold yet smooth","icy clean finish","whisper of grain warmth"],bgColor:"from-gray-900 to-slate-800",textColor:"text-gray-100",image:"/lovable-uploads/lynxvodka.png"}],O=()=>{let[a,b]=(0,e.useState)();return((a,b=3e3)=>{(0,e.useEffect)(()=>{if(!a)return;let c=setInterval(()=>{a.scrollNext()},b);return()=>clearInterval(c)},[a,b])})(a,3e3),(0,d.jsx)("div",{className:"py-10 md:py-16 bg-transparent",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 relative z-10",children:(0,d.jsxs)(I,{setApi:b,className:"w-full",opts:{align:"center",loop:!0},children:[(0,d.jsx)(J,{className:"-ml-1 md:-ml-4",children:N.map(a=>(0,d.jsx)(K,{className:"pl-1 md:pl-4 basis-2/3 sm:basis-1/2 md:basis-1/4 lg:basis-1/5",children:(0,d.jsx)("div",{className:"relative group",children:(0,d.jsx)("div",{className:"w-40 h-56 md:w-48 md:h-64 bg-white/5 rounded-xl flex items-center justify-center border border-white/10 p-3 md:p-6 hover:bg-white/10 transition-all duration-300",children:(0,d.jsx)("img",{src:a.image,alt:a.name,className:"max-w-full max-h-full object-contain filter drop-shadow-xl group-hover:scale-110 transition-transform duration-300"})})})},a.name))}),(0,d.jsx)(L,{className:"hidden md:flex -left-16 bg-amber-900/50 border-amber-400/50 text-amber-300 hover:bg-amber-600"}),(0,d.jsx)(M,{className:"hidden md:flex -right-16 bg-amber-900/50 border-amber-400/50 text-amber-300 hover:bg-amber-600"})]})})})}},65169:(a,b,c)=>{let{createProxy:d}=c(39893);a.exports=d("/home/<USER>/shangrila/node_modules/next/dist/client/app-dir/link.js")},66111:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},66731:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70009:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},73515:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},75553:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},77572:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/shangrila/src/components/home/<USER>" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/shangrila/src/components/home/<USER>","default")},79148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},79574:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},80552:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,77572)),Promise.resolve().then(c.bind(c,24169))},81231:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88421:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>F,metadata:()=>E});var d=c(75338),e=c(24169),f=c(65169),g=c.n(f),h=c(4290);let i=(0,h.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var j=c(70009),k=c(8010);let l=()=>(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20",children:(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 lg:gap-12 items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-playfair font-bold text-amber-100 mb-4 sm:mb-6 leading-tight",children:["Welcome to Shangrila Distillery – Nepal's Premium Craft Spirits",(0,d.jsx)("span",{className:"block bg-gradient-to-r from-amber-400 via-amber-300 to-amber-500 bg-clip-text text-transparent",children:"Blending Excellence."})]}),(0,d.jsx)("p",{className:"text-base sm:text-lg lg:text-xl text-amber-100/80 mb-6 sm:mb-8 leading-relaxed font-crimson",children:"From the heart of Nepal, we source the finest malts and botanicals, expertly blending them in our state-of-the-art facility to craft spirits of unmatched character and clarity. Where tradition meets technique, every blend tells a story of purity, precision, and passion. Crafted in Nepal. Savoured Around the World."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:[(0,d.jsx)(g(),{href:"/products",children:(0,d.jsxs)(k.$,{className:"premium-button px-8 py-4 text-lg font-baskerville tracking-wide",children:[(0,d.jsx)(i,{className:"mr-2 h-5 w-5"}),"Discover Our Collection"]})}),(0,d.jsx)(g(),{href:"/contact",children:(0,d.jsxs)(k.$,{variant:"outline",className:"hidden sm:inline-flex border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-8 py-4 text-lg font-baskerville backdrop-blur-sm",children:["Visit Our Heritage",(0,d.jsx)(j.A,{className:"ml-2 h-5 w-5"})]})})]})]}),(0,d.jsxs)("div",{className:"relative flex flex-col md:flex-row items-center justify-center gap-4 md:gap-6 min-h-[150px] sm:min-h-[200px]",children:[(0,d.jsx)("img",{src:"/lovable-uploads/fontlogo.jpeg",alt:"Shangrila Distillery Logo",className:"h-48 w-auto sm:h-64 md:h-80 lg:h-[28rem] max-w-[90vw] sm:max-w-none rounded-lg shadow-2xl border-2 border-amber-400/30 bg-white/10 backdrop-blur-sm object-cover"}),(0,d.jsx)("div",{className:"absolute -top-4 -right-4 w-8 h-8 bg-amber-400 rounded-full opacity-60 animate-pulse"}),(0,d.jsx)("div",{className:"absolute -bottom-4 -left-4 w-6 h-6 bg-amber-400 rounded-full opacity-40 animate-pulse delay-1000"})]})]})});var m=c(77572),n=c(79574),o=c(73515);let p=(0,h.A)("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]),q=()=>(0,d.jsxs)("div",{className:"aged-paper py-20 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-distillery-texture opacity-30"}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)("span",{className:"text-lg font-semibold text-amber-900 font-baskerville italic",children:"Heritage & Craftsmanship"})}),(0,d.jsxs)("h2",{className:"text-5xl md:text-7xl font-playfair font-bold text-stone-900 mb-8 leading-tight",children:["Crafting",(0,d.jsx)("br",{}),(0,d.jsxs)("span",{className:"text-amber-700 relative",children:["Legendary",(0,d.jsx)("div",{className:"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-amber-600 to-amber-400 rounded-full"})]}),(0,d.jsx)("br",{}),"Experiences"]}),(0,d.jsx)("p",{className:"text-xl text-stone-700 leading-relaxed mb-12 font-crimson max-w-4xl mx-auto",children:"At Shangrila Distillery, we honour the noble art of blending — where time, technique, and tradition converge. Each bottle is a testament to our unwavering dedication, carrying the legacy of master blenders through generations. From hand-selected malts to precise blending methods, we craft spirits that are rich in character and timeless in quality."}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-center items-center gap-8 mb-12 max-w-2xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center group w-full md:w-1/2",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto",children:(0,d.jsx)(n.A,{className:"h-8 w-8 text-amber-800"})}),(0,d.jsx)("h3",{className:"text-3xl font-playfair font-bold text-amber-800 mb-2",children:"Master"}),(0,d.jsx)("p",{className:"text-stone-600 font-crimson",children:"blender craft "})]}),(0,d.jsxs)("div",{className:"text-center group w-full md:w-1/2",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto",children:(0,d.jsx)(o.A,{className:"h-8 w-8 text-amber-800"})}),(0,d.jsx)("h3",{className:"text-3xl font-playfair font-bold text-amber-800 mb-2",children:"Legacy"}),(0,d.jsx)("p",{className:"text-stone-600 font-crimson",children:"Generations Deep"})]})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,d.jsx)(g(),{href:"/products",children:(0,d.jsxs)(k.$,{className:"bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 text-amber-50 px-8 py-4 text-lg font-baskerville shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:[(0,d.jsx)(p,{className:"mr-2 h-5 w-5"}),"Explore Our Heritage Collection"]})}),(0,d.jsx)(g(),{href:"/about",children:(0,d.jsx)(k.$,{className:"bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 text-amber-50 border-2 border-amber-700 px-8 py-4 text-lg font-baskerville shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:"Discover Our Story"})})]})]})})]}),r=()=>null;var s=c(66731),t=c(48343),u=c(51181);let v=()=>(0,d.jsxs)("div",{className:"py-20 bg-gradient-to-br from-stone-800 via-amber-900/30 to-stone-900 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-distillery-texture opacity-10"}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("span",{className:"text-amber-400 font-baskerville italic text-lg tracking-wide mb-4 block",children:"Our Distinguished Collection"}),(0,d.jsx)("h3",{className:"text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6",children:"Premium Heritage Spirits"}),(0,d.jsx)("p",{className:"text-xl text-amber-100/70 max-w-3xl mx-auto font-crimson leading-relaxed",children:"Each bottle in our collection represents decades of refinement, carrying the soul of the Himalayas and the expertise of our master distillers."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:"Tejas Gold",description:"A distinguished blend of the finest aged malts, crafted with time-honored traditions",category:"Premium Whiskey",image:"/lovable-uploads/tejas gold.jpeg"},{name:"Tejas Black",description:"Bold and smoky, with the depth of character that defines true craftsmanship",category:"Peated Whiskey",image:"/lovable-uploads/tejas black.png"},{name:"Reef Vodka",description:"Crystal pure vodka, distilled to perfection from the finest mountain spring water",category:"Premium Vodka",image:"/lovable-uploads/reef.png"},{name:"LOD Vodka",description:"Ultra-premium vodka representing the pinnacle of our distilling excellence",category:"Luxury Vodka",image:"/lovable-uploads/lod.png"},{name:"Phantom",description:"A bold new expression in our whiskey lineup, Phantom delivers a smooth yet powerful character crafted for the domestic connoisseur.",category:"40UP Whiskey",image:"/lovable-uploads/phantom.jpeg"},{name:"Royal Distinction",description:"A regal blend that offers a smooth, rich taste, fit for connoisseurs seeking a truly royal experience.",category:"Premium Whiskey",image:"/lovable-uploads/royal.png"},{name:"0 Degree",description:"A crisp and clean spirit, perfect for cocktails or sipping neat, offering a refreshing and pure taste.",category:"Vodka",image:"/lovable-uploads/vodka.png"},{name:"Lynx",description:"A smooth and sophisticated vodka, crafted to deliver a premium experience with every sip.",category:"Premium Vodka",image:"/lovable-uploads/lynxvodka.png"}].map(a=>(0,d.jsxs)("div",{className:"group distillery-card p-6 text-center hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-amber-500/20",children:[(0,d.jsxs)("div",{className:"relative mb-6 overflow-hidden rounded-lg",children:[(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-32 h-48 object-contain mx-auto shadow-md group-hover:scale-110 transition-transform duration-500 filter sepia-[0.2]"}),(0,d.jsx)("div",{className:"absolute -top-2 -right-2 bg-amber-500 text-amber-950 p-2 rounded-full",children:(0,d.jsx)(s.A,{className:"h-4 w-4"})})]}),(0,d.jsx)("h4",{className:"text-2xl font-playfair font-bold text-amber-100 mb-2",children:a.name}),(0,d.jsx)("p",{className:"text-sm font-semibold text-amber-400 mb-3 uppercase tracking-wide font-baskerville",children:a.category}),(0,d.jsx)("p",{className:"text-amber-100/70 leading-relaxed font-crimson text-sm",children:a.description}),(0,d.jsxs)("div",{className:"mt-6 pt-4 border-t border-amber-500/30",children:[(0,d.jsx)(g(),{href:"/contact",children:(0,d.jsxs)(k.$,{variant:"ghost",className:"text-amber-400 hover:text-amber-300 font-baskerville font-semibold group-hover:bg-amber-500/10 mb-4",children:["Visit Us",(0,d.jsx)(j.A,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform"})]})}),(0,d.jsxs)("div",{className:"flex space-x-3 justify-center",children:[(0,d.jsx)("a",{href:"https://www.facebook.com/profile.php?id=61577339984580&mibextid=wwXIfr&rdid=NeM8ITYNkZt9W2LF&share_url=https%3A%2F%2Fwww.facebook.com%2Fshare%2F18zeJbDG6E%2F%3Fmibextid%3DwwXIfr",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)(k.$,{size:"icon",variant:"outline",className:"text-amber-400 border-amber-500/50 hover:bg-amber-500/20 hover:border-amber-300 rounded-full",children:(0,d.jsx)(t.A,{className:"h-4 w-4"})})}),(0,d.jsx)("a",{href:"https://www.linkedin.com/company/shangrila-distillery/",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)(k.$,{size:"icon",variant:"outline",className:"text-amber-400 border-amber-500/50 hover:bg-amber-500/20 hover:border-amber-300 rounded-full",children:(0,d.jsx)(u.A,{className:"h-4 w-4"})})})]})]})]},a.name))}),(0,d.jsx)("div",{className:"text-center mt-16",children:(0,d.jsxs)("div",{className:"inline-flex items-center space-x-4 bg-amber-900/30 backdrop-blur-sm border border-amber-500/30 rounded-full px-6 py-3",children:[(0,d.jsx)("span",{className:"text-amber-200 font-crimson",children:"Connect with us:"}),(0,d.jsxs)("a",{href:"https://www.linkedin.com/company/shangrila-distillery/",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-2 text-amber-300 hover:text-amber-100 transition-colors",children:[(0,d.jsx)(u.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-baskerville font-semibold",children:"Shangrila Distillery"})]})]})})]})]}),w=(0,h.A)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var x=c(63234);let y=()=>(0,d.jsxs)("div",{className:"bg-gradient-to-br from-stone-700 via-amber-800 to-stone-700 py-20 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-distillery-texture opacity-20"}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10",children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,d.jsx)("span",{className:"text-amber-300 font-baskerville italic text-lg tracking-wide mb-4 block",children:"Experience the Legend"}),(0,d.jsx)("h3",{className:"text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6",children:"Taste the Himalayan Heritage"}),(0,d.jsx)("p",{className:"text-xl text-amber-100/80 mb-8 font-crimson leading-relaxed",children:"Join an exclusive circle of connoisseurs who appreciate the finest spirits. Discover why our heritage continues to captivate palates across the globe."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,d.jsx)(g(),{href:"/products",children:(0,d.jsxs)(k.$,{className:"premium-button px-10 py-4 text-lg font-baskerville tracking-wide",children:[(0,d.jsx)(w,{className:"mr-2 h-5 w-5"}),"Explore Our Spirits"]})}),(0,d.jsx)(g(),{href:"/contact",children:(0,d.jsxs)(k.$,{variant:"outline",className:"border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-10 py-4 text-lg font-baskerville backdrop-blur-sm",children:[(0,d.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"Become a Partner"]})})]})]})})]});var z=c(39614),A=c(75553);let B=[{name:"Trade Vision Partners",location:"Australia",description:"Our flagship partnership brings decades of international distribution expertise, opening doors to premium markets across Australia and beyond. Together, we're setting new standards for Nepalese spirits on the global stage.",link:"https://tradevisionpartners.com/"},{name:"ShyamBaba Group",location:"Nepal",description:"Nepal's foremost diversified business group with interests in Food Products, Cement, Construction, Manufacturing and Trading. With over 2000 employees and 10,000 direct customers, ShyamBaba Group touches lives across Nepal through its vast offering of market-leading high quality consumer products.",link:"https://sbgcompanies.com/"}],C=()=>(0,d.jsx)("div",{className:"bg-stone-900 py-20 sm:py-28",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-4xl sm:text-5xl font-playfair font-bold text-amber-100",children:"Our Export Partners"}),(0,d.jsx)("p",{className:"mt-4 text-lg text-amber-100/70 font-crimson",children:"Collaborating with the best to bring our spirits to the world."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 gap-10",children:B.map(a=>(0,d.jsxs)("div",{className:"distillery-card p-8 text-center",children:[(0,d.jsx)(z.A,{className:"h-16 w-16 mx-auto mb-6 text-amber-400 animate-float"}),(0,d.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,d.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mr-3",children:a.name}),(0,d.jsx)("a",{href:a.link,target:"_blank",rel:"noopener noreferrer",className:"text-amber-400 hover:text-amber-300 transition-colors",children:(0,d.jsx)(A.A,{className:"h-6 w-6"})})]}),(0,d.jsx)("p",{className:"text-amber-300 mb-4 font-crimson",children:a.location}),(0,d.jsx)("p",{className:"text-lg distillery-text",children:a.description})]},a.name))}),(0,d.jsx)("div",{className:"mt-16 text-center",children:(0,d.jsx)(g(),{href:"/exports",children:(0,d.jsxs)(k.$,{variant:"outline",className:"border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-8 py-4 text-lg font-baskerville backdrop-blur-sm",children:[(0,d.jsx)("span",{children:"Become a Partner"}),(0,d.jsx)(j.A,{className:"ml-2 h-5 w-5"})]})})})]})});var D=c(80450);let E={title:"Shangrila Distillery | Leading Distillery in Nepal - Premium Craft Spirits",description:"Shangrila Distillery is Nepal's premier craft spirits producer. Discover our premium whiskey, vodka, and gin collection crafted in the heart of the Himalayas.",keywords:["Shangrila Distillery","Nepal Distillery","Premium Spirits Nepal","Craft Whiskey Nepal","Nepal Vodka","Himalayan Spirits","Best Distillery Nepal"],openGraph:{title:"Shangrila Distillery | Leading Distillery in Nepal",description:"Discover Nepal's premier craft spirits from Shangrila Distillery. Premium whiskey, vodka, and gin crafted with Himalayan purity.",url:"https://shangriladistillery.com",siteName:"Shangrila Distillery",images:[{url:"/lovable-uploads/fontlogo.jpeg",width:1200,height:630,alt:"Shangrila Distillery - Premium Craft Spirits from Nepal"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"Shangrila Distillery | Nepal's Premier Craft Distillery",description:"Crafting premium spirits in the heart of the Himalayas. Discover our heritage collection.",images:["/lovable-uploads/fontlogo.jpeg"]},alternates:{canonical:"https://shangriladistillery.com"}};function F(){return(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(e.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsx)(l,{}),(0,d.jsx)(m.default,{}),(0,d.jsx)(q,{}),(0,d.jsx)(r,{}),(0,d.jsx)(v,{}),(0,d.jsx)(y,{}),(0,d.jsx)(C,{}),(0,d.jsx)(D.A,{})]})]})}}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[195,482,373,529,744],()=>b(b.s=37412));module.exports=c})();