(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8010:(a,b,c)=>{"use strict";c.d(b,{$:()=>m});var d=c(75338),e=c(74515),f=e.forwardRef((a,b)=>{let{children:c,...f}=a,h=e.Children.toArray(c),j=h.find(i);if(j){let a=j.props.children,c=h.map(b=>b!==j?b:e.Children.count(a)>1?e.Children.only(null):e.isValidElement(a)?a.props.children:null);return(0,d.jsx)(g,{...f,ref:b,children:e.isValidElement(a)?e.cloneElement(a,void 0,c):null})}return(0,d.jsx)(g,{...f,ref:b,children:c})});f.displayName="Slot";var g=e.forwardRef((a,b)=>{let{children:c,...d}=a;if(e.isValidElement(c)){let a=function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(c);return e.cloneElement(c,{...function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{f(...a),e(...a)}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props),ref:b?function(...a){return b=>a.forEach(a=>{"function"==typeof a?a(b):null!=a&&(a.current=b)})}(b,a):a})}return e.Children.count(c)>1?e.Children.only(null):null});g.displayName="SlotClone";var h=({children:a})=>(0,d.jsx)(d.Fragment,{children:a});function i(a){return e.isValidElement(a)&&a.type===h}var j=c(86281),k=c(70673);let l=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},h)=>{let i=e?f:"button";return(0,d.jsx)(i,{className:(0,k.cn)(l({variant:b,size:c,className:a})),ref:h,...g})});m.displayName="Button"},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35329:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,75687))},37412:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,88421)),"/home/<USER>/shangrila/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}],I=["/home/<USER>/shangrila/src/app/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},39614:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Handshake",[["path",{d:"m11 17 2 2a1 1 0 1 0 3-3",key:"efffak"}],["path",{d:"m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4",key:"9pr0kb"}],["path",{d:"m21 3 1 11h-2",key:"1tisrp"}],["path",{d:"M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3",key:"1uvwmv"}],["path",{d:"M3 4h8",key:"1ep09j"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63234:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},66731:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70009:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},73515:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},75553:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},77572:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m You\'re importing a component that needs `useState`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.\n  \x1b[31m|\x1b[0m\n  \x1b[31m|\x1b[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client\n  \x1b[31m|\x1b[0m\n\n   ,-[\x1b[36;1;4m/home/<USER>/shangrila/src/components/home/<USER>"react";\n   : \x1b[35;1m         ^^^^^^^^\x1b[0m\n \x1b[2m2\x1b[0m | import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi } from "@/components/ui/carousel";\n \x1b[2m3\x1b[0m | import { products } from "@/data/products";\n \x1b[2m4\x1b[0m | import { useAutoCarousel } from "@/hooks/useAutoCarousel";\n   `----\n')},79574:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88421:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>G,metadata:()=>F});var d=c(75338),e=c(24169),f=c(65169),g=c.n(f),h=c(4290);let i=(0,h.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var j=c(70009),k=c(8010);let l=()=>(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20",children:(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 lg:gap-12 items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-playfair font-bold text-amber-100 mb-4 sm:mb-6 leading-tight",children:["Welcome to Shangrila Distillery – Nepal's Premium Craft Spirits",(0,d.jsx)("span",{className:"block bg-gradient-to-r from-amber-400 via-amber-300 to-amber-500 bg-clip-text text-transparent",children:"Blending Excellence."})]}),(0,d.jsx)("p",{className:"text-base sm:text-lg lg:text-xl text-amber-100/80 mb-6 sm:mb-8 leading-relaxed font-crimson",children:"From the heart of Nepal, we source the finest malts and botanicals, expertly blending them in our state-of-the-art facility to craft spirits of unmatched character and clarity. Where tradition meets technique, every blend tells a story of purity, precision, and passion. Crafted in Nepal. Savoured Around the World."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:[(0,d.jsx)(g(),{href:"/products",children:(0,d.jsxs)(k.$,{className:"premium-button px-8 py-4 text-lg font-baskerville tracking-wide",children:[(0,d.jsx)(i,{className:"mr-2 h-5 w-5"}),"Discover Our Collection"]})}),(0,d.jsx)(g(),{href:"/contact",children:(0,d.jsxs)(k.$,{variant:"outline",className:"hidden sm:inline-flex border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-8 py-4 text-lg font-baskerville backdrop-blur-sm",children:["Visit Our Heritage",(0,d.jsx)(j.A,{className:"ml-2 h-5 w-5"})]})})]})]}),(0,d.jsxs)("div",{className:"relative flex flex-col md:flex-row items-center justify-center gap-4 md:gap-6 min-h-[150px] sm:min-h-[200px]",children:[(0,d.jsx)("img",{src:"/lovable-uploads/fontlogo.jpeg",alt:"Shangrila Distillery Logo",className:"h-48 w-auto sm:h-64 md:h-80 lg:h-[28rem] max-w-[90vw] sm:max-w-none rounded-lg shadow-2xl border-2 border-amber-400/30 bg-white/10 backdrop-blur-sm object-cover"}),(0,d.jsx)("div",{className:"absolute -top-4 -right-4 w-8 h-8 bg-amber-400 rounded-full opacity-60 animate-pulse"}),(0,d.jsx)("div",{className:"absolute -bottom-4 -left-4 w-6 h-6 bg-amber-400 rounded-full opacity-40 animate-pulse delay-1000"})]})]})});var m=c(77572),n=c.n(m),o=c(79574),p=c(73515);let q=(0,h.A)("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]),r=()=>(0,d.jsxs)("div",{className:"aged-paper py-20 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-distillery-texture opacity-30"}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)("span",{className:"text-lg font-semibold text-amber-900 font-baskerville italic",children:"Heritage & Craftsmanship"})}),(0,d.jsxs)("h2",{className:"text-5xl md:text-7xl font-playfair font-bold text-stone-900 mb-8 leading-tight",children:["Crafting",(0,d.jsx)("br",{}),(0,d.jsxs)("span",{className:"text-amber-700 relative",children:["Legendary",(0,d.jsx)("div",{className:"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-amber-600 to-amber-400 rounded-full"})]}),(0,d.jsx)("br",{}),"Experiences"]}),(0,d.jsx)("p",{className:"text-xl text-stone-700 leading-relaxed mb-12 font-crimson max-w-4xl mx-auto",children:"At Shangrila Distillery, we honour the noble art of blending — where time, technique, and tradition converge. Each bottle is a testament to our unwavering dedication, carrying the legacy of master blenders through generations. From hand-selected malts to precise blending methods, we craft spirits that are rich in character and timeless in quality."}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-center items-center gap-8 mb-12 max-w-2xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center group w-full md:w-1/2",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto",children:(0,d.jsx)(o.A,{className:"h-8 w-8 text-amber-800"})}),(0,d.jsx)("h3",{className:"text-3xl font-playfair font-bold text-amber-800 mb-2",children:"Master"}),(0,d.jsx)("p",{className:"text-stone-600 font-crimson",children:"blender craft "})]}),(0,d.jsxs)("div",{className:"text-center group w-full md:w-1/2",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300 mx-auto",children:(0,d.jsx)(p.A,{className:"h-8 w-8 text-amber-800"})}),(0,d.jsx)("h3",{className:"text-3xl font-playfair font-bold text-amber-800 mb-2",children:"Legacy"}),(0,d.jsx)("p",{className:"text-stone-600 font-crimson",children:"Generations Deep"})]})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,d.jsx)(g(),{href:"/products",children:(0,d.jsxs)(k.$,{className:"bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 text-amber-50 px-8 py-4 text-lg font-baskerville shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:[(0,d.jsx)(q,{className:"mr-2 h-5 w-5"}),"Explore Our Heritage Collection"]})}),(0,d.jsx)(g(),{href:"/about",children:(0,d.jsx)(k.$,{className:"bg-gradient-to-r from-amber-700 to-amber-600 hover:from-amber-600 hover:to-amber-500 text-amber-50 border-2 border-amber-700 px-8 py-4 text-lg font-baskerville shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:"Discover Our Story"})})]})]})})]}),s=()=>null;var t=c(66731),u=c(48343),v=c(51181);let w=()=>(0,d.jsxs)("div",{className:"py-20 bg-gradient-to-br from-stone-800 via-amber-900/30 to-stone-900 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-distillery-texture opacity-10"}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("span",{className:"text-amber-400 font-baskerville italic text-lg tracking-wide mb-4 block",children:"Our Distinguished Collection"}),(0,d.jsx)("h3",{className:"text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6",children:"Premium Heritage Spirits"}),(0,d.jsx)("p",{className:"text-xl text-amber-100/70 max-w-3xl mx-auto font-crimson leading-relaxed",children:"Each bottle in our collection represents decades of refinement, carrying the soul of the Himalayas and the expertise of our master distillers."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:"Tejas Gold",description:"A distinguished blend of the finest aged malts, crafted with time-honored traditions",category:"Premium Whiskey",image:"/lovable-uploads/tejas gold.jpeg"},{name:"Tejas Black",description:"Bold and smoky, with the depth of character that defines true craftsmanship",category:"Peated Whiskey",image:"/lovable-uploads/tejas black.png"},{name:"Reef Vodka",description:"Crystal pure vodka, distilled to perfection from the finest mountain spring water",category:"Premium Vodka",image:"/lovable-uploads/reef.png"},{name:"LOD Vodka",description:"Ultra-premium vodka representing the pinnacle of our distilling excellence",category:"Luxury Vodka",image:"/lovable-uploads/lod.png"},{name:"Phantom",description:"A bold new expression in our whiskey lineup, Phantom delivers a smooth yet powerful character crafted for the domestic connoisseur.",category:"40UP Whiskey",image:"/lovable-uploads/phantom.jpeg"},{name:"Royal Distinction",description:"A regal blend that offers a smooth, rich taste, fit for connoisseurs seeking a truly royal experience.",category:"Premium Whiskey",image:"/lovable-uploads/royal.png"},{name:"0 Degree",description:"A crisp and clean spirit, perfect for cocktails or sipping neat, offering a refreshing and pure taste.",category:"Vodka",image:"/lovable-uploads/vodka.png"},{name:"Lynx",description:"A smooth and sophisticated vodka, crafted to deliver a premium experience with every sip.",category:"Premium Vodka",image:"/lovable-uploads/lynxvodka.png"}].map(a=>(0,d.jsxs)("div",{className:"group distillery-card p-6 text-center hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-amber-500/20",children:[(0,d.jsxs)("div",{className:"relative mb-6 overflow-hidden rounded-lg",children:[(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-32 h-48 object-contain mx-auto shadow-md group-hover:scale-110 transition-transform duration-500 filter sepia-[0.2]"}),(0,d.jsx)("div",{className:"absolute -top-2 -right-2 bg-amber-500 text-amber-950 p-2 rounded-full",children:(0,d.jsx)(t.A,{className:"h-4 w-4"})})]}),(0,d.jsx)("h4",{className:"text-2xl font-playfair font-bold text-amber-100 mb-2",children:a.name}),(0,d.jsx)("p",{className:"text-sm font-semibold text-amber-400 mb-3 uppercase tracking-wide font-baskerville",children:a.category}),(0,d.jsx)("p",{className:"text-amber-100/70 leading-relaxed font-crimson text-sm",children:a.description}),(0,d.jsxs)("div",{className:"mt-6 pt-4 border-t border-amber-500/30",children:[(0,d.jsx)(g(),{href:"/contact",children:(0,d.jsxs)(k.$,{variant:"ghost",className:"text-amber-400 hover:text-amber-300 font-baskerville font-semibold group-hover:bg-amber-500/10 mb-4",children:["Visit Us",(0,d.jsx)(j.A,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform"})]})}),(0,d.jsxs)("div",{className:"flex space-x-3 justify-center",children:[(0,d.jsx)("a",{href:"https://www.facebook.com/profile.php?id=61577339984580&mibextid=wwXIfr&rdid=NeM8ITYNkZt9W2LF&share_url=https%3A%2F%2Fwww.facebook.com%2Fshare%2F18zeJbDG6E%2F%3Fmibextid%3DwwXIfr",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)(k.$,{size:"icon",variant:"outline",className:"text-amber-400 border-amber-500/50 hover:bg-amber-500/20 hover:border-amber-300 rounded-full",children:(0,d.jsx)(u.A,{className:"h-4 w-4"})})}),(0,d.jsx)("a",{href:"https://www.linkedin.com/company/shangrila-distillery/",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)(k.$,{size:"icon",variant:"outline",className:"text-amber-400 border-amber-500/50 hover:bg-amber-500/20 hover:border-amber-300 rounded-full",children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})})]})]})]},a.name))}),(0,d.jsx)("div",{className:"text-center mt-16",children:(0,d.jsxs)("div",{className:"inline-flex items-center space-x-4 bg-amber-900/30 backdrop-blur-sm border border-amber-500/30 rounded-full px-6 py-3",children:[(0,d.jsx)("span",{className:"text-amber-200 font-crimson",children:"Connect with us:"}),(0,d.jsxs)("a",{href:"https://www.linkedin.com/company/shangrila-distillery/",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-2 text-amber-300 hover:text-amber-100 transition-colors",children:[(0,d.jsx)(v.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-baskerville font-semibold",children:"Shangrila Distillery"})]})]})})]})]}),x=(0,h.A)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var y=c(63234);let z=()=>(0,d.jsxs)("div",{className:"bg-gradient-to-br from-stone-700 via-amber-800 to-stone-700 py-20 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-distillery-texture opacity-20"}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10",children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,d.jsx)("span",{className:"text-amber-300 font-baskerville italic text-lg tracking-wide mb-4 block",children:"Experience the Legend"}),(0,d.jsx)("h3",{className:"text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-6",children:"Taste the Himalayan Heritage"}),(0,d.jsx)("p",{className:"text-xl text-amber-100/80 mb-8 font-crimson leading-relaxed",children:"Join an exclusive circle of connoisseurs who appreciate the finest spirits. Discover why our heritage continues to captivate palates across the globe."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,d.jsx)(g(),{href:"/products",children:(0,d.jsxs)(k.$,{className:"premium-button px-10 py-4 text-lg font-baskerville tracking-wide",children:[(0,d.jsx)(x,{className:"mr-2 h-5 w-5"}),"Explore Our Spirits"]})}),(0,d.jsx)(g(),{href:"/contact",children:(0,d.jsxs)(k.$,{variant:"outline",className:"border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-10 py-4 text-lg font-baskerville backdrop-blur-sm",children:[(0,d.jsx)(y.A,{className:"mr-2 h-5 w-5"}),"Become a Partner"]})})]})]})})]});var A=c(39614),B=c(75553);let C=[{name:"Trade Vision Partners",location:"Australia",description:"Our flagship partnership brings decades of international distribution expertise, opening doors to premium markets across Australia and beyond. Together, we're setting new standards for Nepalese spirits on the global stage.",link:"https://tradevisionpartners.com/"},{name:"ShyamBaba Group",location:"Nepal",description:"Nepal's foremost diversified business group with interests in Food Products, Cement, Construction, Manufacturing and Trading. With over 2000 employees and 10,000 direct customers, ShyamBaba Group touches lives across Nepal through its vast offering of market-leading high quality consumer products.",link:"https://sbgcompanies.com/"}],D=()=>(0,d.jsx)("div",{className:"bg-stone-900 py-20 sm:py-28",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-4xl sm:text-5xl font-playfair font-bold text-amber-100",children:"Our Export Partners"}),(0,d.jsx)("p",{className:"mt-4 text-lg text-amber-100/70 font-crimson",children:"Collaborating with the best to bring our spirits to the world."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 gap-10",children:C.map(a=>(0,d.jsxs)("div",{className:"distillery-card p-8 text-center",children:[(0,d.jsx)(A.A,{className:"h-16 w-16 mx-auto mb-6 text-amber-400 animate-float"}),(0,d.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,d.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mr-3",children:a.name}),(0,d.jsx)("a",{href:a.link,target:"_blank",rel:"noopener noreferrer",className:"text-amber-400 hover:text-amber-300 transition-colors",children:(0,d.jsx)(B.A,{className:"h-6 w-6"})})]}),(0,d.jsx)("p",{className:"text-amber-300 mb-4 font-crimson",children:a.location}),(0,d.jsx)("p",{className:"text-lg distillery-text",children:a.description})]},a.name))}),(0,d.jsx)("div",{className:"mt-16 text-center",children:(0,d.jsx)(g(),{href:"/exports",children:(0,d.jsxs)(k.$,{variant:"outline",className:"border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-8 py-4 text-lg font-baskerville backdrop-blur-sm",children:[(0,d.jsx)("span",{children:"Become a Partner"}),(0,d.jsx)(j.A,{className:"ml-2 h-5 w-5"})]})})})]})});var E=c(80450);let F={title:"Shangrila Distillery | Leading Distillery in Nepal - Premium Craft Spirits",description:"Shangrila Distillery is Nepal's premier craft spirits producer. Discover our premium whiskey, vodka, and gin collection crafted in the heart of the Himalayas.",keywords:["Shangrila Distillery","Nepal Distillery","Premium Spirits Nepal","Craft Whiskey Nepal","Nepal Vodka","Himalayan Spirits","Best Distillery Nepal"],openGraph:{title:"Shangrila Distillery | Leading Distillery in Nepal",description:"Discover Nepal's premier craft spirits from Shangrila Distillery. Premium whiskey, vodka, and gin crafted with Himalayan purity.",url:"https://shangriladistillery.com",siteName:"Shangrila Distillery",images:[{url:"/lovable-uploads/fontlogo.jpeg",width:1200,height:630,alt:"Shangrila Distillery - Premium Craft Spirits from Nepal"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"Shangrila Distillery | Nepal's Premier Craft Distillery",description:"Crafting premium spirits in the heart of the Himalayas. Discover our heritage collection.",images:["/lovable-uploads/fontlogo.jpeg"]},alternates:{canonical:"https://shangriladistillery.com"}};function G(){return(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(e.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsx)(l,{}),(0,d.jsx)(n(),{}),(0,d.jsx)(r,{}),(0,d.jsx)(s,{}),(0,d.jsx)(w,{}),(0,d.jsx)(z,{}),(0,d.jsx)(D,{}),(0,d.jsx)(E.A,{})]})]})}},98881:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,24169))}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[195,251,583,925,744],()=>b(b.s=37412));module.exports=c})();