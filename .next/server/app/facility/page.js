(()=>{var a={};a.id=54,a.ids=[54],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},38708:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},39939:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,24169))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},50163:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},54011:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,75687))},62312:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["facility",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,73514)),"/home/<USER>/shangrila/src/app/facility/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/shangrila/src/app/facility/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/facility/page",pathname:"/facility",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/facility/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63234:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},64214:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},73514:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>z,metadata:()=>y});var d=c(75338),e=c(24169);let f=({})=>(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Our State-of-the-Art Facility"}),(0,d.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"A world-class distillery facility combining traditional craftsmanship with modern technology in the heart of Nepal's majestic Himalayas."})]}),g=()=>(0,d.jsxs)("div",{className:"mb-20",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[(0,d.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,d.jsx)("img",{src:"/lovable-uploads/d30150e7-f7de-46a2-bee7-4d4d9a82caff.png",alt:"Shangrila Distillery Facility - Mountain View",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Himalayan Setting"}),(0,d.jsx)("p",{className:"text-amber-100/80",children:"Nestled in the pristine mountains of Nepal"})]})]}),(0,d.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,d.jsx)("img",{src:"/lovable-uploads/25cb54a2-cb09-4306-91bf-d1d6cbf98ec7.png",alt:"Shangrila Distillery Main Building",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Modern Architecture"}),(0,d.jsx)("p",{className:"text-amber-100/80",children:"Contemporary design meets traditional craftsmanship"})]})]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,d.jsx)("img",{src:"/lovable-uploads/3f437121-b350-4cd1-8796-babca2f34cb6.png",alt:"Shangrila Distillery Production Wing",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Production Facility"}),(0,d.jsx)("p",{className:"text-amber-100/80",children:"Advanced manufacturing capabilities"})]})]}),(0,d.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,d.jsx)("img",{src:"/lovable-uploads/7a56bbca-5184-47f8-bb85-623b34c87dde.png",alt:"Shangrila Distillery Infrastructure",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Infrastructure Excellence"}),(0,d.jsx)("p",{className:"text-amber-100/80",children:"Built for scalability and efficiency"})]})]})]})]});var h=c(4290);let i=(0,h.A)("Factory",[["path",{d:"M2 20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8l-7 5V8l-7 5V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"159hny"}],["path",{d:"M17 18h1",key:"uldtlt"}],["path",{d:"M12 18h1",key:"s9uhes"}],["path",{d:"M7 18h1",key:"1neino"}]]),j=(0,h.A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),k=(0,h.A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),l=(0,h.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),m=(0,h.A)("Cog",[["path",{d:"M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z",key:"sobvz5"}],["path",{d:"M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z",key:"11i496"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 22v-2",key:"1osdcq"}],["path",{d:"m17 20.66-1-1.73",key:"eq3orb"}],["path",{d:"M11 10.27 7 3.34",key:"16pf9h"}],["path",{d:"m20.66 17-1.73-1",key:"sg0v6f"}],["path",{d:"m3.34 7 1.73 1",key:"1ulond"}],["path",{d:"M14 12h8",key:"4f43i9"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"m20.66 7-1.73 1",key:"1ow05n"}],["path",{d:"m3.34 17 1.73-1",key:"nuk764"}],["path",{d:"m17 3.34-1 1.73",key:"2wel8s"}],["path",{d:"m11 13.73-4 6.93",key:"794ttg"}]]);var n=c(73515),o=c(64723);let p=(0,h.A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),q=()=>{let a=[{icon:m,title:"Production Technology",details:["Automated control systems","Temperature monitoring","Precision measurement"]},{icon:n.A,title:"Expert Team",details:["Master distillers","Quality assurance","Production specialists"]},{icon:o.A,title:"Strategic Location",details:["Himalayan water source","Optimal climate","Transportation access"]},{icon:p,title:"Continuous Operation",details:["24/7 monitoring","Shift operations","Quality control"]}];return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20",children:[{title:"Blending Tanks",description:"State-of-the-art stainless steel tanks for precise blending and maturation",icon:i},{title:"Bottling Lines",description:"Automated bottling systems ensuring consistency and quality control",icon:j},{title:"Storage Facilities",description:"Climate-controlled warehouses for optimal aging and inventory management",icon:k},{title:"Quality Lab",description:"Advanced testing laboratory ensuring every batch meets our standards",icon:l}].map(a=>(0,d.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-amber-400"})}),(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:a.title}),(0,d.jsx)("p",{className:"distillery-text",children:a.description})]},a.title))}),(0,d.jsxs)("div",{className:"mb-20",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-12 text-center",children:"Facility Specifications"}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:a.map(a=>(0,d.jsxs)("div",{className:"distillery-card p-6 hover:scale-105 transition-all duration-300",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-amber-400/20 rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-amber-400"})}),(0,d.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-3",children:a.title}),(0,d.jsx)("ul",{className:"space-y-1",children:a.details.map(a=>(0,d.jsxs)("li",{className:"distillery-text text-sm",children:["• ",a]},a))})]},a.title))})]})]})},r=()=>(0,d.jsxs)("div",{className:"bg-gradient-to-r from-amber-900/50 to-stone-900/50 rounded-lg p-8 border border-amber-500/30 backdrop-blur-sm text-amber-100 mb-20",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold mb-6 text-center",children:"Environmental Commitment"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-xl font-crimson font-semibold text-amber-300 mb-2",children:"Sustainable Practices"}),(0,d.jsx)("p",{className:"distillery-text",children:"Eco-friendly production methods and waste reduction"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-xl font-crimson font-semibold text-amber-300 mb-2",children:"Water Conservation"}),(0,d.jsx)("p",{className:"distillery-text",children:"Advanced water treatment and recycling systems"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-xl font-crimson font-semibold text-amber-300 mb-2",children:"Energy Efficiency"}),(0,d.jsx)("p",{className:"distillery-text",children:"Modern equipment optimized for minimal energy consumption"})]})]})]});var s=c(50163),t=c(63234),u=c(64214),v=c(38708);let w=()=>{let a=[{icon:s.A,title:"Storage Capacity Expansion",description:"2,00,000+ litres storage capacity with climate-controlled facilities for optimal aging and inventory management.",timeline:"Current & Expanding"},{icon:t.A,title:"Phase 1: Oceania Markets",description:"Export to Oceania Markets & achieve significant market share in the domestic market with our premium spirits.",timeline:"2024-2025"},{icon:u.A,title:"Phase 2: Global Expansion",description:"Expand to more continents and introduce more premium and different products to diversify our portfolio.",timeline:"2025-2027"},{icon:v.A,title:"Technology Upgrade",description:"Implementing advanced automation and quality monitoring systems for enhanced precision and consistency.",timeline:"2025"}];return(0,d.jsxs)("div",{className:"mb-20",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-playfair font-bold text-amber-100 mb-6",children:"Future Expansion Plans"}),(0,d.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Our vision extends beyond today's achievements. We're committed to continuous growth and innovation while maintaining the highest standards of quality and craftsmanship."})]}),(0,d.jsxs)("div",{className:"distillery-card p-8 mb-16 text-center",children:[(0,d.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-4",children:"2,00,000+ litres storage capacity"}),(0,d.jsx)("p",{className:"text-lg distillery-text max-w-2xl mx-auto",children:"and planning on more"})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 gap-8",children:a.map(a=>(0,d.jsx)("div",{className:"distillery-card p-8 hover:scale-105 transition-all duration-300",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-amber-400/20 rounded-full flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-amber-400"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100",children:a.title}),(0,d.jsx)("span",{className:"text-sm text-amber-400 font-crimson font-semibold",children:a.timeline})]}),(0,d.jsx)("p",{className:"distillery-text font-crimson leading-relaxed",children:a.description})]})]})},a.title))}),(0,d.jsx)("div",{className:"mt-12 text-center",children:(0,d.jsxs)("div",{className:"distillery-card p-8 max-w-4xl mx-auto",children:[(0,d.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mb-4",children:"Our Commitment to Excellence"}),(0,d.jsx)("p",{className:"distillery-text font-crimson text-lg leading-relaxed",children:"Every expansion plan is designed with sustainability, quality, and innovation at its core. We're not just growing our capacity – we're elevating the entire industry standard for premium spirit production in Nepal and beyond."})]})})]})};var x=c(80450);let y={title:"Our State-of-the-Art Distillery Facility",description:"Tour Shangrila Distillery's world-class facility in Nepal. Discover our advanced production capabilities, quality standards, and sustainable practices.",keywords:["Shangrila Distillery Facility","Nepal Distillery Tour","Craft Spirits Production","Distillery Technology","Quality Standards Nepal"],openGraph:{title:"Master Distillery Facility | Shangrila Distillery",description:"A world-class distillery facility combining traditional craftsmanship with modern technology in the heart of Nepal's majestic Himalayas.",url:"https://shangriladistillery.com/facility",images:[{url:"/lovable-uploads/d30150e7-f7de-46a2-bee7-4d4d9a82caff.png",width:1200,height:630,alt:"Shangrila Distillery Facility"}]},alternates:{canonical:"https://shangriladistillery.com/facility"}};function z(){return(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(e.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,d.jsx)(f,{}),(0,d.jsx)(g,{}),(0,d.jsx)(q,{}),(0,d.jsx)(r,{}),(0,d.jsx)(w,{})]}),(0,d.jsx)(x.A,{})]})]})}},73515:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,251,583,925,744],()=>b(b.s=62312));module.exports=c})();