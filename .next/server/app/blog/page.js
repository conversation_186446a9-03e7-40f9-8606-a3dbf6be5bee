(()=>{var a={};a.id=831,a.ids=[831],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8010:(a,b,c)=>{"use strict";c.d(b,{$:()=>m});var d=c(75338),e=c(74515),f=e.forwardRef((a,b)=>{let{children:c,...f}=a,h=e.Children.toArray(c),j=h.find(i);if(j){let a=j.props.children,c=h.map(b=>b!==j?b:e.Children.count(a)>1?e.Children.only(null):e.isValidElement(a)?a.props.children:null);return(0,d.jsx)(g,{...f,ref:b,children:e.isValidElement(a)?e.cloneElement(a,void 0,c):null})}return(0,d.jsx)(g,{...f,ref:b,children:c})});f.displayName="Slot";var g=e.forwardRef((a,b)=>{let{children:c,...d}=a;if(e.isValidElement(c)){let a=function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(c);return e.cloneElement(c,{...function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{f(...a),e(...a)}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props),ref:b?function(...a){return b=>a.forEach(a=>{"function"==typeof a?a(b):null!=a&&(a.current=b)})}(b,a):a})}return e.Children.count(c)>1?e.Children.only(null):null});g.displayName="SlotClone";var h=({children:a})=>(0,d.jsx)(d.Fragment,{children:a});function i(a){return e.isValidElement(a)&&a.type===h}var j=c(86281),k=c(70673);let l=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},h)=>{let i=e?f:"button";return(0,d.jsx)(i,{className:(0,k.cn)(l({variant:b,size:c,className:a})),ref:h,...g})});m.displayName="Button"},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35329:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.bind(c,75687))},35900:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,67531)),"/home/<USER>/shangrila/src/app/blog/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,20839)),"/home/<USER>/shangrila/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/shangrila/src/app/blog/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/blog/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44924:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},47616:(a,b,c)=>{"use strict";c.d(b,{G:()=>d});let d=[{id:7,slug:"prajanna-raj-adhikari-leading-shangrila-distillery",image:"/lovable-uploads/prashanna.png",metaTitle:"Prajanna Raj Adhikari – Managing Director Shangrila Distillery Nepal | Leadership Profile",metaDescription:"Meet Prajanna Raj Adhikari, Managing Director of Shangrila Distillery Nepal. Discover his journey from Australia to leading Nepal's premier whiskey, vodka & gin distillery with global business expertise.",title:"Prajanna Raj Adhikari – Leading Shangrila Distillery into a New Era of Excellence",introduction:"When it comes to the art and business of spirits, leadership requires more than just management skills — it demands vision, industry knowledge, and a deep understanding of both craft and market. Prajanna Raj Adhikari, Managing Director of Shangrila Distillery Limited, embodies all of these qualities, bringing a rare blend of hands-on experience, academic excellence, and entrepreneurial insight to Nepal’s evolving liquor industry.",publishedDate:"2024-09-15T10:00:00.000Z",modifiedDate:"2024-09-18T15:30:00.000Z",keywords:["Prajanna Raj Adhikari","Shangrila Distillery Managing Director","Nepal Distillery CEO","Nepal Whiskey Industry Leader","Shangrila Distillery Leadership","Nepal Premium Spirits Executive","Distillery Management Nepal","Nepal Vodka Industry","Craft Spirits Nepal","Federation University Business Graduate","Victoria University MBA","Nepal Liquor Industry","Himalayan Spirits Leader","Nepal Gin Producer","Distillery Business Strategy","Nepal Alcohol Industry","Premium Spirits Nepal","Shangrila Distillery CEO","Nepal Beverage Industry Leader","Craft Distillery Management"],sections:[{title:"From Hospitality Floors to Boardroom Leadership",content:"Before stepping into his role as Managing Director at Shangrila Distillery Nepal, Prajanna immersed himself in the heart of the hospitality sector — managing pubs, restaurants, and bottle shops across Australia. This direct engagement with customers gave him a firsthand understanding of consumer preferences, service excellence, and operational efficiency in the premium spirits industry. He learned not just how to run successful outlets, but how to read the pulse of the market — knowledge that now informs his strategic decisions at Nepal's leading craft distillery."},{title:"Mastering the Craft of Blending",content:"Recognizing that great leadership in the spirits industry must be rooted in product knowledge, Prajanna pursued professional blending classes. These experiences expanded his understanding of flavor profiles, maturation, and quality control, allowing him to bridge the gap between distillation and consumer demand. His blending expertise ensures that Shangrila Distillery’s products are not only commercially viable but also competitive on a global scale."},{title:"Academic Excellence with a Global Perspective",content:"Prajanna’s academic journey reflects his commitment to business mastery. He earned his Bachelor’s in Business from Federation University, Australia, where he majored in Marketing — a field that sharpened his ability to position brands, understand market trends, and craft effective campaigns. He then went on to complete his Master of Business Administration (MBA) at Victoria University, specializing in Global Business. This education has equipped him with the strategic thinking needed to scale operations, enter new markets, and navigate complex international trade landscapes."},{title:"Driving Shangrila Distillery Forward",content:"Under Prajanna’s leadership, Shangrila Distillery is poised for expansion, both domestically and internationally. His marketing acumen ensures that each product resonates with its target audience, while his operational insights guarantee efficiency and consistency in production. By combining local heritage with global business strategies, he is positioning the distillery as a proud representative of Nepal’s craftsmanship on the world stage."},{title:"Why Prajanna Raj Adhikari is the Right Leader",content:"Proven Industry Experience – Years of hands-on work in hospitality and retail.\n\nTechnical Expertise – Formal training in blending and quality assessment.\n\nStrategic Vision – Marketing and global business knowledge from internationally recognized universities.\n\nLeadership in Action – Steering Shangrila Distillery toward growth and global recognition."}],conclusion:"Prajanna Raj Adhikari isn’t just managing Shangrila Distillery — he’s shaping its future. His unique blend of practical know-how, academic credentials, and global vision makes him the perfect leader to carry Nepal’s spirits industry into a new era of innovation and prestige."},{id:1,slug:"history-and-heritage-of-shangrila-distillery-nepal",metaTitle:"The Rich History of Shangrila Distillery Nepal – A Legacy of Excellence",metaDescription:"Discover the heritage of Shangrila Distillery Nepal, the best distillery of Nepal. From its founding to today, learn how it became a leader in premium spirits.",title:"The History and Heritage of Shangrila Distillery Nepal",introduction:"For decades, Shangrila Distillery Nepal has been synonymous with quality, tradition, and innovation in the spirits industry. Recognized as the best distillery of Nepal, it has grown from a humble local producer to a symbol of Nepalese pride.",sections:[{title:"Early Beginnings",content:"Founded with a mission to bring world-class spirits to Nepal, Shangrila Distillery began operations in Chitwan, where the clean water and favorable climate provided the ideal foundation for distillation."},{title:"Growth and Innovation",content:"Over the years, Shangrila Distillery expanded its product line and upgraded its facilities, introducing modern techniques while preserving time-honored craftsmanship."},{title:"Heritage and Cultural Connection",content:"Every bottle reflects Nepal’s cultural richness, from the choice of ingredients to the design of the packaging."}],conclusion:"Today, with its factory in Chitwan and head office in Kathmandu, Shangrila Distillery Nepal stands as a living testament to Nepal’s distilling heritage."},{id:2,slug:"inside-the-factory-how-shangrila-distillery-produces-world-class-spirits",metaTitle:"Shangrila Distillery Chitwan – Inside Nepal’s Finest Spirit Factory",metaDescription:"Explore the Chitwan factory of Shangrila Distillery, the best distillery of Nepal. Learn how premium spirits are crafted with care and tradition.",title:"Inside the Factory – How Shangrila Distillery in Chitwan Produces World-Class Spirits",introduction:"In the heart of Chitwan lies the beating heart of Shangrila Distillery Nepal — a state-of-the-art factory where tradition meets technology.",sections:[{title:"Step 1: Selecting Premium Ingredients",content:"The process begins with the careful selection of grains, botanicals, and pure water sourced locally."},{title:"Step 2: The Distillation Process",content:"Modern stills ensure purity, while expert distillers maintain flavor consistency."},{title:"Step 3: Aging and Maturation",content:"Spirits are aged in oak barrels to enhance flavor complexity."},{title:"Step 4: Bottling and Packaging",content:"Each product is elegantly packaged in eco-friendly materials."}],conclusion:"From Chitwan to the rest of Nepal, Shangrila’s factory remains a hub of quality and precision."},{id:3,slug:"top-5-reasons-why-shangrila-distillery-is-the-best-distillery-of-nepal",metaTitle:"Why Shangrila Distillery is Nepal’s Best – 5 Reasons That Set It Apart",metaDescription:"See why Shangrila Distillery is the best distillery of Nepal. From quality to sustainability, here’s what makes it unmatched.",title:"Top 5 Reasons Why Shangrila Distillery is the Best Distillery of Nepal",introduction:"If you’ve ever wondered why Shangrila Distillery Nepal has such a stellar reputation, here are the top five reasons.",sections:[{title:"Uncompromising Quality",content:"Every product meets strict standards."},{title:"Heritage and Tradition",content:"Rooted in Nepalese culture."},{title:"Innovation",content:"Adapting to global trends while preserving authenticity."},{title:"Sustainability",content:"Environmentally responsible operations."},{title:"Customer Trust",content:"A brand beloved across generations."}],conclusion:"From Chitwan’s lush landscapes to Kathmandu’s bustling markets, Shangrila continues to shine as the best distillery of Nepal."},{id:4,slug:"a-connoisseurs-guide-to-shangrila-distillerys-premium-alcoholic-beverages",metaTitle:"Shangrila Distillery Nepal – A Guide to Its Finest Spirits",metaDescription:"Explore the premium range from Shangrila Distillery Nepal. From whiskey to rum, discover the flavors that make it Nepal’s best distillery.",title:"A Connoisseur’s Guide to Shangrila Distillery’s Premium Alcoholic Beverages",introduction:"For those who appreciate fine spirits, Shangrila Distillery Nepal offers a diverse and premium selection.",sections:[{title:"Whiskey",content:"Aged to perfection with rich aroma."},{title:"Vodka",content:"Crisp, clean, and triple-distilled."},{title:"Rum",content:"Full-bodied and flavorful."},{title:"Special Editions",content:"Limited releases for collectors."}],conclusion:"Every sip from Shangrila is a celebration of Nepalese craftsmanship."},{id:5,slug:"sustainability-at-shangrila-distillery-nepal-brewing-a-greener-future",metaTitle:"Sustainable Distilling – How Shangrila Distillery Nepal Leads the Way",metaDescription:"Learn how Shangrila Distillery Nepal is reducing its environmental footprint while staying the best distillery of Nepal.",title:"Sustainability at Shangrila Distillery Nepal – Brewing a Greener Future",introduction:"Shangrila Distillery Nepal understands that great spirits should not come at the expense of the environment.",sections:[{title:"Eco-Friendly Packaging",content:"Using recyclable materials."},{title:"Water Conservation",content:"Reducing waste through efficient processes."},{title:"Waste Management",content:"Reusing and recycling by-products."}],conclusion:"Sustainability is not just a trend for Shangrila — it’s a core value."},{id:6,slug:"from-kathmandu-to-chitwan-how-shangrila-distillery-connects-nepals-culture-with-the-world",metaTitle:"Shangrila Distillery – Bridging Nepal’s Heritage from Chitwan to Kathmandu",metaDescription:"Discover how Shangrila Distillery connects Nepal’s culture and craftsmanship to the world through its locations in Chitwan and Kathmandu.",title:"From Kathmandu to Chitwan – How Shangrila Distillery Connects Nepal’s Culture with the World",introduction:"With its factory in Chitwan and head office in Kathmandu, Shangrila Distillery perfectly bridges Nepal’s traditions and modern business.",sections:[{title:"Chitwan – The Production Hub",content:"Harnessing natural resources for pure distillation."},{title:"Kathmandu – The Strategic Center",content:"Managing operations and exports."},{title:"Global Recognition",content:"Showcasing Nepalese spirits internationally."}],conclusion:"Shangrila is more than a distillery — it’s an ambassador of Nepal’s craftsmanship."}]},48343:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},51181:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64723:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},65169:(a,b,c)=>{let{createProxy:d}=c(39893);a.exports=d("/home/<USER>/shangrila/node_modules/next/dist/client/app-dir/link.js")},66111:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},67531:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(75338),e=c(24169),f=c(80450),g=c(47616),h=c(65169),i=c.n(h),j=c(8010);let k=({slug:a,title:b,introduction:c})=>(0,d.jsxs)("div",{className:"distillery-card p-6 hover:scale-105 transition-all duration-300 flex flex-col",children:[(0,d.jsx)("h3",{className:"text-xl font-playfair font-bold text-amber-100 mb-4",children:b}),(0,d.jsx)("p",{className:"distillery-text mb-6 flex-grow",children:c}),(0,d.jsx)(i(),{href:`/blog/${a}`,children:(0,d.jsx)(j.$,{className:"w-full premium-button font-crimson font-semibold",children:"Read More"})})]}),l={title:"Blog - Stories from Shangrila Distillery",description:"Discover the stories behind Shangrila Distillery, from our heritage to our sustainable practices. Read about Nepal's premier craft spirits producer.",keywords:["Shangrila Distillery Blog","Nepal Distillery Stories","Craft Spirits Blog","Distillery Heritage","Premium Spirits News"],openGraph:{title:"Blog | Shangrila Distillery Stories",description:"Discover the stories behind Shangrila Distillery, from our heritage to our sustainable practices.",url:"https://shangriladistillery.com/blog",images:[{url:"/lovable-uploads/prashanna.png",width:1200,height:630,alt:"Shangrila Distillery Blog"}]},alternates:{canonical:"https://shangriladistillery.com/blog"}};function m(){return(0,d.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,d.jsx)(e.default,{}),(0,d.jsxs)("div",{className:"pt-20",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Our Blog"}),(0,d.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Discover the stories behind Shangrila Distillery, from our heritage to our sustainable practices."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:g.G.map(a=>(0,d.jsx)(k,{slug:a.slug,title:a.title,introduction:a.introduction},a.id))})]}),(0,d.jsx)(f.A,{})]})]})}},79148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},81231:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},98881:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.bind(c,24169))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[195,482,373,529,744],()=>b(b.s=35900));module.exports=c})();