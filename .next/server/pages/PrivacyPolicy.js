"use strict";(()=>{var a={};a.id=110,a.ids=[110,839],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21651:(a,b,c)=>{c.r(b),c.d(b,{config:()=>u,default:()=>q,getServerSideProps:()=>t,getStaticPaths:()=>s,getStaticProps:()=>r,handler:()=>C,reportWebVitals:()=>v,routeModule:()=>B,unstable_getServerProps:()=>z,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>x,unstable_getStaticProps:()=>w});var d={};c.r(d),c.d(d,{default:()=>o});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(8732),m=c(88204),n=c(32829);let o=()=>(0,l.jsxs)("div",{className:"min-h-screen bg-stone-900",children:[(0,l.jsx)(m.A,{}),(0,l.jsx)("main",{className:"pt-20",children:(0,l.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,l.jsxs)("div",{className:"distillery-card p-8",children:[(0,l.jsx)("h1",{className:"text-4xl font-playfair font-bold text-amber-100 mb-8",children:"Privacy Policy"}),(0,l.jsxs)("div",{className:"space-y-6 text-amber-100/80 font-crimson leading-relaxed",children:[(0,l.jsx)("p",{className:"text-amber-200/60 text-sm",children:"Last updated: December 2024"}),(0,l.jsxs)("section",{children:[(0,l.jsx)("h2",{className:"text-2xl font-playfair font-semibold text-amber-200 mb-4",children:"Introduction"}),(0,l.jsx)("p",{children:'Shangrila Distillery ("we," "our," or "us") respects your privacy and is committed to protecting your personal data. This privacy policy explains how we collect, use, and safeguard your information when you visit our website or engage with our services.'})]}),(0,l.jsxs)("section",{children:[(0,l.jsx)("h2",{className:"text-2xl font-playfair font-semibold text-amber-200 mb-4",children:"Information We Collect"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-amber-300",children:"Personal Information"}),(0,l.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,l.jsx)("li",{children:"Age verification information (date of birth)"}),(0,l.jsx)("li",{children:"Contact information (name, email, phone number)"}),(0,l.jsx)("li",{children:"Shipping and billing addresses"}),(0,l.jsx)("li",{children:"Communication preferences"})]}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-amber-300 mt-4",children:"Usage Information"}),(0,l.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,l.jsx)("li",{children:"Website usage data and analytics"}),(0,l.jsx)("li",{children:"Device information and IP addresses"}),(0,l.jsx)("li",{children:"Cookies and similar tracking technologies"})]})]})]}),(0,l.jsxs)("section",{children:[(0,l.jsx)("h2",{className:"text-2xl font-playfair font-semibold text-amber-200 mb-4",children:"How We Use Your Information"}),(0,l.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,l.jsx)("li",{children:"To verify legal drinking age in accordance with local laws"}),(0,l.jsx)("li",{children:"To process orders and manage customer relationships"}),(0,l.jsx)("li",{children:"To improve our website and services"}),(0,l.jsx)("li",{children:"To communicate about products, events, and promotions"}),(0,l.jsx)("li",{children:"To comply with legal obligations"})]})]}),(0,l.jsxs)("section",{children:[(0,l.jsx)("h2",{className:"text-2xl font-playfair font-semibold text-amber-200 mb-4",children:"Data Security"}),(0,l.jsx)("p",{children:"We implement appropriate technical and organizational security measures to protect your personal data against unauthorized access, alteration, disclosure, or destruction. However, no internet transmission is completely secure."})]}),(0,l.jsxs)("section",{children:[(0,l.jsx)("h2",{className:"text-2xl font-playfair font-semibold text-amber-200 mb-4",children:"Your Rights"}),(0,l.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,l.jsx)("li",{children:"Access your personal data"}),(0,l.jsx)("li",{children:"Correct inaccurate information"}),(0,l.jsx)("li",{children:"Request deletion of your data"}),(0,l.jsx)("li",{children:"Opt-out of marketing communications"}),(0,l.jsx)("li",{children:"Data portability where applicable"})]})]}),(0,l.jsxs)("section",{children:[(0,l.jsx)("h2",{className:"text-2xl font-playfair font-semibold text-amber-200 mb-4",children:"Contact Us"}),(0,l.jsxs)("p",{children:["For questions about this privacy policy or to exercise your rights, contact us at:",(0,l.jsx)("br",{}),"Email: <EMAIL>",(0,l.jsx)("br",{}),"Address:  Brahmanagar, Rapti Nagarpalika-8, Chitwan, Nepal"]})]})]})]})})}),(0,l.jsx)(n.A,{})]});var p=c(12289);let q=(0,g.M)(d,"default"),r=(0,g.M)(d,"getStaticProps"),s=(0,g.M)(d,"getStaticPaths"),t=(0,g.M)(d,"getServerSideProps"),u=(0,g.M)(d,"config"),v=(0,g.M)(d,"reportWebVitals"),w=(0,g.M)(d,"unstable_getStaticProps"),x=(0,g.M)(d,"unstable_getStaticPaths"),y=(0,g.M)(d,"unstable_getStaticParams"),z=(0,g.M)(d,"unstable_getServerProps"),A=(0,g.M)(d,"unstable_getServerSideProps"),B=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/PrivacyPolicy",pathname:"/PrivacyPolicy",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:d}),C=(0,p.U)({srcPage:"/PrivacyPolicy",config:u,userland:d,routeModule:B,getStaticPaths:s,getStaticProps:r,getServerSideProps:t})},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82015:a=>{a.exports=require("react")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157,952,277,696],()=>b(b.s=21651));module.exports=c})();