"use strict";(()=>{var a={};a.id=38,a.ids=[38,839],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},54822:a=>{a.exports=require("react-router-dom")},82015:a=>{a.exports=require("react")},96362:(a,b,c)=>{c.r(b),c.d(b,{config:()=>u,default:()=>q,getServerSideProps:()=>t,getStaticPaths:()=>s,getStaticProps:()=>r,handler:()=>C,reportWebVitals:()=>v,routeModule:()=>B,unstable_getServerProps:()=>z,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>x,unstable_getStaticProps:()=>w});var d={};c.r(d),c.d(d,{default:()=>o});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(8732),m=c(54822),n=c(82015);let o=()=>{let a=(0,m.useLocation)();return(0,n.useEffect)(()=>{console.error("404 Error: User attempted to access non-existent route:",a.pathname)},[a.pathname]),(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"404"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),(0,l.jsx)("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})};var p=c(12289);let q=(0,g.M)(d,"default"),r=(0,g.M)(d,"getStaticProps"),s=(0,g.M)(d,"getStaticPaths"),t=(0,g.M)(d,"getServerSideProps"),u=(0,g.M)(d,"config"),v=(0,g.M)(d,"reportWebVitals"),w=(0,g.M)(d,"unstable_getStaticProps"),x=(0,g.M)(d,"unstable_getStaticPaths"),y=(0,g.M)(d,"unstable_getStaticParams"),z=(0,g.M)(d,"unstable_getServerProps"),A=(0,g.M)(d,"unstable_getServerSideProps"),B=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/NotFound",pathname:"/NotFound",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:d}),C=(0,p.U)({srcPage:"/NotFound",config:u,userland:d,routeModule:B,getStaticPaths:s,getStaticProps:r,getServerSideProps:t})}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157],()=>b(b.s=96362));module.exports=c})();