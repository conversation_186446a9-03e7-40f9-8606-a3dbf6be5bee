"use strict";(()=>{var a={};a.id=353,a.ids=[353,839],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},50799:(a,b,c)=>{c.r(b),c.d(b,{config:()=>w,default:()=>s,getServerSideProps:()=>v,getStaticPaths:()=>u,getStaticProps:()=>t,handler:()=>E,reportWebVitals:()=>x,routeModule:()=>D,unstable_getServerProps:()=>B,unstable_getServerSideProps:()=>C,unstable_getStaticParams:()=>A,unstable_getStaticPaths:()=>z,unstable_getStaticProps:()=>y});var d={};c.r(d),c.d(d,{default:()=>q});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(8732),m=c(88204),n=c(32829),o=c(74952);let p=[{title:"Sales Executive",icon:o.ZHz,summary:"Drive sales growth, build relationships with distributors and retailers, and expand our market presence across Nepal.",responsibilities:["Identify and pursue new sales opportunities","Build and maintain strong client relationships","Achieve monthly and annual sales targets","Represent the brand at events and trade shows"],requirements:["Bachelor's degree in Business or related field","1+ years of sales experience (FMCG or spirits preferred)","Excellent communication and negotiation skills"],benefits:["Competitive salary and incentives","Travel allowance","Professional growth opportunities"]},{title:"Production Operator",icon:o.wB_,summary:"Operate and maintain distillery equipment, ensuring quality and efficiency in every batch.",responsibilities:["Operate blending, bottling, and packaging machinery","Monitor production processes for quality and safety","Perform routine equipment maintenance","Follow SOPs and safety protocols"],requirements:["High school diploma or equivalent","Experience in manufacturing or food/beverage industry preferred","Attention to detail and reliability"],benefits:["Stable employment","On-the-job training","Supportive team environment"]},{title:"Quality Assurance Officer",icon:o.oxo,summary:"Monitor production standards, conduct quality checks, and help maintain our reputation for excellence.",responsibilities:["Conduct inspections and quality tests at various production stages","Document and report quality issues","Ensure compliance with regulatory standards","Collaborate with production and R&D teams"],requirements:["Bachelor's degree in Science, Food Technology, or related field","Experience in quality control/assurance preferred","Strong analytical and documentation skills"],benefits:["Competitive compensation","Learning and development opportunities","Contribution to product excellence"]},{title:"Marketing Specialist",icon:o.R03,summary:"Develop and execute marketing campaigns to promote our brands and engage customers.",responsibilities:["Plan and implement digital and offline marketing campaigns","Create engaging content for social media and print","Analyze campaign performance and report results","Coordinate with sales and creative teams"],requirements:["Bachelor's degree in Marketing, Communications, or related field","Experience in marketing or advertising","Creativity and strong communication skills"],benefits:["Dynamic work environment","Opportunities for creativity","Performance-based bonuses"]},{title:"Logistics Coordinator",icon:o.eMd,summary:"Manage supply chain operations, coordinate shipments, and ensure timely delivery of our products.",responsibilities:["Coordinate inbound and outbound logistics","Manage inventory and warehouse operations","Liaise with transporters and vendors","Optimize delivery routes and schedules"],requirements:["Bachelor's degree or diploma in Logistics, Supply Chain, or related field","Experience in logistics or warehouse management","Organizational and problem-solving skills"],benefits:["Competitive salary","Travel and meal allowances","Career advancement opportunities"]}],q=()=>(0,l.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,l.jsx)(m.A,{}),(0,l.jsxs)("div",{className:"pt-20",children:[(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Careers at Shangrila Distillery"}),(0,l.jsx)("p",{className:"text-xl distillery-text max-w-2xl mx-auto",children:"Join our passionate team and help shape the future of Nepalese spirits. We value innovation, dedication, and a commitment to excellence."})]}),(0,l.jsxs)("div",{className:"mb-16",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Current Openings"}),(0,l.jsx)("div",{className:"grid gap-10",children:p.map(a=>(0,l.jsxs)("div",{className:"distillery-card p-8 bg-stone-900/70 rounded-2xl shadow-xl flex flex-col md:flex-row items-center md:items-start gap-8 hover:scale-[1.02] transition-transform duration-300",children:[(0,l.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center w-20 h-20 rounded-full bg-amber-900/30 border-2 border-amber-400/30 mb-4 md:mb-0",children:(0,l.jsx)(a.icon,{className:"h-10 w-10 text-amber-400"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold text-amber-100 mb-2 font-playfair",children:a.title}),(0,l.jsx)("p",{className:"text-amber-100/80 font-crimson text-base mb-4",children:a.summary}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-left",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-amber-200 mb-2",children:"Responsibilities"}),(0,l.jsx)("ul",{className:"list-disc list-inside text-amber-100/70 text-sm font-crimson space-y-1",children:a.responsibilities.map((a,b)=>(0,l.jsx)("li",{children:a},b))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-amber-200 mb-2",children:"Requirements"}),(0,l.jsx)("ul",{className:"list-disc list-inside text-amber-100/70 text-sm font-crimson space-y-1",children:a.requirements.map((a,b)=>(0,l.jsx)("li",{children:a},b))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-amber-200 mb-2",children:"Benefits"}),(0,l.jsx)("ul",{className:"list-disc list-inside text-amber-100/70 text-sm font-crimson space-y-1",children:a.benefits.map((a,b)=>(0,l.jsx)("li",{children:a},b))})]})]})]})]},a.title))})]}),(0,l.jsxs)("div",{className:"text-center mt-12",children:[(0,l.jsx)("span",{className:"text-amber-200 font-crimson text-lg",children:"To apply or inquire, email us at "}),(0,l.jsx)("a",{href:"mailto:<EMAIL>",className:"text-amber-400 underline font-bold font-baskerville hover:text-amber-300 transition-colors",children:"<EMAIL>"})]})]}),(0,l.jsx)(n.A,{})]})]});var r=c(12289);let s=(0,g.M)(d,"default"),t=(0,g.M)(d,"getStaticProps"),u=(0,g.M)(d,"getStaticPaths"),v=(0,g.M)(d,"getServerSideProps"),w=(0,g.M)(d,"config"),x=(0,g.M)(d,"reportWebVitals"),y=(0,g.M)(d,"unstable_getStaticProps"),z=(0,g.M)(d,"unstable_getStaticPaths"),A=(0,g.M)(d,"unstable_getStaticParams"),B=(0,g.M)(d,"unstable_getServerProps"),C=(0,g.M)(d,"unstable_getServerSideProps"),D=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/Careers",pathname:"/Careers",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:d}),E=(0,r.U)({srcPage:"/Careers",config:w,userland:d,routeModule:D,getStaticPaths:u,getStaticProps:t,getServerSideProps:v})},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82015:a=>{a.exports=require("react")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157,952,277,696],()=>b(b.s=50799));module.exports=c})();