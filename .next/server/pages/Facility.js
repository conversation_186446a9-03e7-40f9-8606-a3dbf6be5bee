"use strict";(()=>{var a={};a.id=783,a.ids=[783,839],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},81162:(a,b,c)=>{c.r(b),c.d(b,{config:()=>A,default:()=>w,getServerSideProps:()=>z,getStaticPaths:()=>y,getStaticProps:()=>x,handler:()=>I,reportWebVitals:()=>B,routeModule:()=>H,unstable_getServerProps:()=>F,unstable_getServerSideProps:()=>G,unstable_getStaticParams:()=>E,unstable_getStaticPaths:()=>D,unstable_getStaticProps:()=>C});var d={};c.r(d),c.d(d,{default:()=>u});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(8732),m=c(88204);let n=({})=>(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Our State-of-the-Art Facility"}),(0,l.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"A world-class distillery facility combining traditional craftsmanship with modern technology in the heart of Nepal's majestic Himalayas."})]}),o=()=>(0,l.jsxs)("div",{className:"mb-20",children:[(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[(0,l.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,l.jsx)("img",{src:"/lovable-uploads/d30150e7-f7de-46a2-bee7-4d4d9a82caff.png",alt:"Shangrila Distillery Facility - Mountain View",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,l.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,l.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Himalayan Setting"}),(0,l.jsx)("p",{className:"text-amber-100/80",children:"Nestled in the pristine mountains of Nepal"})]})]}),(0,l.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,l.jsx)("img",{src:"/lovable-uploads/25cb54a2-cb09-4306-91bf-d1d6cbf98ec7.png",alt:"Shangrila Distillery Main Building",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,l.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,l.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Modern Architecture"}),(0,l.jsx)("p",{className:"text-amber-100/80",children:"Contemporary design meets traditional craftsmanship"})]})]})]}),(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,l.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,l.jsx)("img",{src:"/lovable-uploads/3f437121-b350-4cd1-8796-babca2f34cb6.png",alt:"Shangrila Distillery Production Wing",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,l.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,l.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Production Facility"}),(0,l.jsx)("p",{className:"text-amber-100/80",children:"Advanced manufacturing capabilities"})]})]}),(0,l.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-2xl",children:[(0,l.jsx)("img",{src:"/lovable-uploads/7a56bbca-5184-47f8-bb85-623b34c87dde.png",alt:"Shangrila Distillery Infrastructure",className:"w-full h-80 object-cover hover:scale-105 transition-transform duration-500"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-stone-900/60 to-transparent"}),(0,l.jsxs)("div",{className:"absolute bottom-4 left-4 text-amber-100",children:[(0,l.jsx)("h3",{className:"text-xl font-playfair font-bold",children:"Infrastructure Excellence"}),(0,l.jsx)("p",{className:"text-amber-100/80",children:"Built for scalability and efficiency"})]})]})]})]});var p=c(74952);let q=()=>{let a=[{title:"Blending Tanks",description:"State-of-the-art stainless steel tanks for precise blending and maturation",icon:p.$7D},{title:"Bottling Lines",description:"Automated bottling systems ensuring consistency and quality control",icon:p.VH9},{title:"Storage Facilities",description:"Climate-controlled warehouses for optimal aging and inventory management",icon:p.ekZ},{title:"Quality Lab",description:"Advanced testing laboratory ensuring every batch meets our standards",icon:p.ntg}],b=[{icon:p.mg3,title:"Production Technology",details:["Automated control systems","Temperature monitoring","Precision measurement"]},{icon:p.zWC,title:"Expert Team",details:["Master distillers","Quality assurance","Production specialists"]},{icon:p.sDd,title:"Strategic Location",details:["Himalayan water source","Optimal climate","Transportation access"]},{icon:p.zD7,title:"Continuous Operation",details:["24/7 monitoring","Shift operations","Quality control"]}];return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20",children:a.map(a=>(0,l.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(a.icon,{className:"h-8 w-8 text-amber-400"})}),(0,l.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:a.title}),(0,l.jsx)("p",{className:"distillery-text",children:a.description})]},a.title))}),(0,l.jsxs)("div",{className:"mb-20",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-12 text-center",children:"Facility Specifications"}),(0,l.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:b.map(a=>(0,l.jsxs)("div",{className:"distillery-card p-6 hover:scale-105 transition-all duration-300",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-amber-400/20 rounded-full flex items-center justify-center mb-4",children:(0,l.jsx)(a.icon,{className:"h-6 w-6 text-amber-400"})}),(0,l.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-3",children:a.title}),(0,l.jsx)("ul",{className:"space-y-1",children:a.details.map(a=>(0,l.jsxs)("li",{className:"distillery-text text-sm",children:["• ",a]},a))})]},a.title))})]})]})},r=()=>(0,l.jsxs)("div",{className:"bg-gradient-to-r from-amber-900/50 to-stone-900/50 rounded-lg p-8 border border-amber-500/30 backdrop-blur-sm text-amber-100 mb-20",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold mb-6 text-center",children:"Environmental Commitment"}),(0,l.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-xl font-crimson font-semibold text-amber-300 mb-2",children:"Sustainable Practices"}),(0,l.jsx)("p",{className:"distillery-text",children:"Eco-friendly production methods and waste reduction"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-xl font-crimson font-semibold text-amber-300 mb-2",children:"Water Conservation"}),(0,l.jsx)("p",{className:"distillery-text",children:"Advanced water treatment and recycling systems"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-xl font-crimson font-semibold text-amber-300 mb-2",children:"Energy Efficiency"}),(0,l.jsx)("p",{className:"distillery-text",children:"Modern equipment optimized for minimal energy consumption"})]})]})]}),s=()=>{let a=[{icon:p.tIP,title:"Storage Capacity Expansion",description:"2,00,000+ litres storage capacity with climate-controlled facilities for optimal aging and inventory management.",timeline:"Current & Expanding"},{icon:p.qzq,title:"Phase 1: Oceania Markets",description:"Export to Oceania Markets & achieve significant market share in the domestic market with our premium spirits.",timeline:"2024-2025"},{icon:p.DTr,title:"Phase 2: Global Expansion",description:"Expand to more continents and introduce more premium and different products to diversify our portfolio.",timeline:"2025-2027"},{icon:p.KqI,title:"Technology Upgrade",description:"Implementing advanced automation and quality monitoring systems for enhanced precision and consistency.",timeline:"2025"}];return(0,l.jsxs)("div",{className:"mb-20",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("h2",{className:"text-3xl md:text-4xl font-playfair font-bold text-amber-100 mb-6",children:"Future Expansion Plans"}),(0,l.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Our vision extends beyond today's achievements. We're committed to continuous growth and innovation while maintaining the highest standards of quality and craftsmanship."})]}),(0,l.jsxs)("div",{className:"distillery-card p-8 mb-16 text-center",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-4",children:"2,00,000+ litres storage capacity"}),(0,l.jsx)("p",{className:"text-lg distillery-text max-w-2xl mx-auto",children:"and planning on more"})]}),(0,l.jsx)("div",{className:"grid md:grid-cols-2 gap-8",children:a.map(a=>(0,l.jsx)("div",{className:"distillery-card p-8 hover:scale-105 transition-all duration-300",children:(0,l.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-amber-400/20 rounded-full flex items-center justify-center flex-shrink-0",children:(0,l.jsx)(a.icon,{className:"h-6 w-6 text-amber-400"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,l.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100",children:a.title}),(0,l.jsx)("span",{className:"text-sm text-amber-400 font-crimson font-semibold",children:a.timeline})]}),(0,l.jsx)("p",{className:"distillery-text font-crimson leading-relaxed",children:a.description})]})]})},a.title))}),(0,l.jsx)("div",{className:"mt-12 text-center",children:(0,l.jsxs)("div",{className:"distillery-card p-8 max-w-4xl mx-auto",children:[(0,l.jsx)("h3",{className:"text-2xl font-playfair font-bold text-amber-100 mb-4",children:"Our Commitment to Excellence"}),(0,l.jsx)("p",{className:"distillery-text font-crimson text-lg leading-relaxed",children:"Every expansion plan is designed with sustainability, quality, and innovation at its core. We're not just growing our capacity – we're elevating the entire industry standard for premium spirit production in Nepal and beyond."})]})})]})};var t=c(32829);let u=()=>(0,l.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,l.jsx)(m.A,{}),(0,l.jsxs)("div",{className:"pt-20",children:[(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,l.jsx)(n,{}),(0,l.jsx)(o,{}),(0,l.jsx)(q,{}),(0,l.jsx)(r,{}),(0,l.jsx)(s,{})]}),(0,l.jsx)(t.A,{})]})]});var v=c(12289);let w=(0,g.M)(d,"default"),x=(0,g.M)(d,"getStaticProps"),y=(0,g.M)(d,"getStaticPaths"),z=(0,g.M)(d,"getServerSideProps"),A=(0,g.M)(d,"config"),B=(0,g.M)(d,"reportWebVitals"),C=(0,g.M)(d,"unstable_getStaticProps"),D=(0,g.M)(d,"unstable_getStaticPaths"),E=(0,g.M)(d,"unstable_getStaticParams"),F=(0,g.M)(d,"unstable_getServerProps"),G=(0,g.M)(d,"unstable_getServerSideProps"),H=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/Facility",pathname:"/Facility",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:d}),I=(0,v.U)({srcPage:"/Facility",config:A,userland:d,routeModule:H,getStaticPaths:y,getStaticProps:x,getServerSideProps:z})},82015:a=>{a.exports=require("react")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157,952,277,696],()=>b(b.s=81162));module.exports=c})();