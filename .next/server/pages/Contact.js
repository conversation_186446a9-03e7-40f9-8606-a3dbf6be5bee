"use strict";(()=>{var a={};a.id=892,a.ids=[839,892],a.modules={1428:a=>{a.exports=import("axios")},1664:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(8732);c(82015);let e=()=>(0,d.jsxs)("div",{className:"distillery-card p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-4",children:"Find Us"}),(0,d.jsx)("div",{className:"w-full h-64 rounded-lg overflow-hidden border border-amber-500/30",children:(0,d.jsx)("iframe",{title:"Shangrila Distillery Location",src:"https://www.google.com/maps?q=27.7172,85.3240&z=15&output=embed",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade"})})]})},3327:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{config:()=>s,default:()=>o,getServerSideProps:()=>r,getStaticPaths:()=>q,getStaticProps:()=>p,handler:()=>A,reportWebVitals:()=>t,routeModule:()=>z,unstable_getServerProps:()=>x,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>u});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(33604),m=c(12289),n=a([l]);l=(n.then?(await n)():n)[0];let o=(0,g.M)(l,"default"),p=(0,g.M)(l,"getStaticProps"),q=(0,g.M)(l,"getStaticPaths"),r=(0,g.M)(l,"getServerSideProps"),s=(0,g.M)(l,"config"),t=(0,g.M)(l,"reportWebVitals"),u=(0,g.M)(l,"unstable_getStaticProps"),v=(0,g.M)(l,"unstable_getStaticPaths"),w=(0,g.M)(l,"unstable_getStaticParams"),x=(0,g.M)(l,"unstable_getServerProps"),y=(0,g.M)(l,"unstable_getServerSideProps"),z=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/Contact",pathname:"/Contact",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:l}),A=(0,m.U)({srcPage:"/Contact",config:s,userland:l,routeModule:z,getStaticPaths:q,getStaticProps:p,getServerSideProps:r});d()}catch(a){d(a)}})},8732:a=>{a.exports=require("react/jsx-runtime")},8938:a=>{a.exports=import("class-variance-authority")},9640:a=>{a.exports=import("@radix-ui/react-slot")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20598:a=>{a.exports=import("@radix-ui/react-label")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33604:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>s});var e=c(8732),f=c(88204),g=c(74952),h=c(66345),i=c(78059),j=c(40273),k=c(87967),l=c(57049),m=c(1664),n=c(32829),o=c(82015),p=c(1428),q=c(41415),r=a([h,i,j,k,l,p,q]);[h,i,j,k,l,p,q]=r.then?(await r)():r;let s=()=>{let[a,b]=(0,o.useState)({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""}),[c,d]=(0,o.useState)(!1),[r,s]=(0,o.useState)({}),t=c=>{let{name:d,value:e}=c.target;b({...a,[d]:e}),r[d]&&s({...r,[d]:""})},u=async c=>{if(c.preventDefault(),(()=>{let b={};return a.firstName.trim()||(b.firstName="First name is required"),a.lastName.trim()||(b.lastName="Last name is required"),a.email?/\S+@\S+\.\S+/.test(a.email)||(b.email="Email is invalid"):b.email="Email is required",a.phone?/^[0-9\s\-+()]*$/.test(a.phone)||(b.phone="Please enter a valid phone number"):b.phone="Phone number is required",a.inquiryType||(b.inquiryType="Please select an inquiry type"),a.message.trim()||(b.message="Message is required"),s(b),0===Object.keys(b).length})()){d(!0);try{let c=await p.default.post(`${(void 0).VITE_API_URL||"https://mail.shangriladistillery.com"}/api/contact`,a,{headers:{"Content-Type":"application/json"}});if(200===c.status)q.toast.success("Your message has been sent successfully!"),b({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""});else throw Error("Failed to send message")}catch(a){console.error("Error sending message:",a),q.toast.error(a.response?.data?.message||"Failed to send message. Please try again later.")}finally{d(!1)}}};return(0,e.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,e.jsx)(f.A,{}),(0,e.jsxs)("div",{className:"pt-20",children:[(0,e.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,e.jsxs)("div",{className:"text-center mb-16",children:[(0,e.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Visit Us"}),(0,e.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Get in touch with our team for inquiries, partnerships, or to learn more about our premium spirits."})]}),(0,e.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,e.jsxs)("div",{className:"space-y-8",children:[(0,e.jsx)("div",{className:"distillery-card p-6",children:(0,e.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,e.jsx)(g.gE4,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,e.jsxs)("div",{children:[(0,e.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Email"}),(0,e.jsx)("p",{className:"distillery-text",children:"<EMAIL>"})]})]})}),(0,e.jsx)("div",{className:"distillery-card p-6",children:(0,e.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,e.jsx)(g.bcf,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,e.jsxs)("div",{children:[(0,e.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Phone"}),(0,e.jsx)("p",{className:"distillery-text",children:"+977-1-4528118"}),(0,e.jsx)("p",{className:"distillery-text",children:"WhatsApp: +977 1-4528118"})]})]})}),(0,e.jsxs)("div",{className:"distillery-card p-6 space-y-6",children:[(0,e.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,e.jsx)(g.sDd,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,e.jsxs)("div",{children:[(0,e.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Head Office Address"}),(0,e.jsxs)("p",{className:"distillery-text",children:["Pipalbot dillibazar-29",(0,e.jsx)("br",{}),"kathmandu nepal"]})]})]}),(0,e.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,e.jsx)(g.sDd,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,e.jsxs)("div",{children:[(0,e.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Factory Location"}),(0,e.jsxs)("p",{className:"distillery-text",children:["Brahmanagar, Rapti Nagarpalika-9",(0,e.jsx)("br",{}),"Chitwan, Nepal"]})]})]})]}),(0,e.jsxs)("div",{className:"bg-gradient-to-r from-amber-600 to-amber-500 rounded-lg p-6 text-stone-900",children:[(0,e.jsx)(g.vEG,{className:"h-8 w-8 text-stone-900 mb-4"}),(0,e.jsx)("h3",{className:"text-xl font-playfair font-semibold mb-2",children:"Business Hours"}),(0,e.jsxs)("p",{className:"font-crimson",children:["Sunday - Friday: 9:00 AM - 6:00 PM",(0,e.jsx)("br",{}),"Saturday: Closed",(0,e.jsx)("br",{})]})]}),(0,e.jsx)(m.A,{})]}),(0,e.jsxs)("div",{className:"distillery-card p-8",children:[(0,e.jsx)("h2",{className:"text-2xl font-playfair font-bold text-amber-100 mb-6",children:"Send us a Message"}),(0,e.jsxs)("form",{className:"space-y-6",onSubmit:u,children:[(0,e.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)(j.J,{htmlFor:"firstName",className:"text-amber-200",children:"First Name"}),(0,e.jsx)(i.p,{id:"firstName",name:"firstName",type:"text",value:a.firstName,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${r.firstName?"border-red-500":""}`,onChange:t}),r.firstName&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.firstName})]}),(0,e.jsxs)("div",{children:[(0,e.jsx)(j.J,{htmlFor:"lastName",className:"text-amber-200",children:"Last Name"}),(0,e.jsx)(i.p,{id:"lastName",name:"lastName",type:"text",value:a.lastName,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${r.lastName?"border-red-500":""}`,onChange:t}),r.lastName&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.lastName})]})]}),(0,e.jsxs)("div",{children:[(0,e.jsx)(j.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,e.jsx)(i.p,{id:"email",name:"email",type:"email",value:a.email,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${r.email?"border-red-500":""}`,onChange:t}),r.email&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.email})]}),(0,e.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)(j.J,{htmlFor:"phone",className:"text-amber-200",children:"Phone Number"}),(0,e.jsx)(i.p,{id:"phone",name:"phone",type:"tel",value:a.phone,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${r.phone?"border-red-500":""}`,onChange:t,placeholder:"+****************"}),r.phone&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.phone})]}),(0,e.jsxs)("div",{children:[(0,e.jsx)(j.J,{htmlFor:"inquiryType",className:"text-amber-200",children:"Inquiry Type"}),(0,e.jsxs)(l.l6,{value:a.inquiryType,onValueChange:c=>{b({...a,inquiryType:c}),r.inquiryType&&s({...r,inquiryType:""})},children:[(0,e.jsx)(l.bq,{className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${r.inquiryType?"border-red-500":""}`,children:(0,e.jsx)(l.yv,{placeholder:"Select inquiry type"})}),(0,e.jsxs)(l.gC,{className:"bg-stone-800 border-amber-500/30",children:[(0,e.jsx)(l.eb,{value:"general",children:"General Inquiry"}),(0,e.jsx)(l.eb,{value:"wholesale",children:"Wholesale Inquiry"}),(0,e.jsx)(l.eb,{value:"partnership",children:"Partnership"}),(0,e.jsx)(l.eb,{value:"press",children:"Press/Media"}),(0,e.jsx)(l.eb,{value:"other",children:"Other"})]})]}),r.inquiryType&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.inquiryType})]})]}),(0,e.jsxs)("div",{className:"space-y-4",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)(j.J,{htmlFor:"subject",className:"text-amber-200",children:"Subject (Optional)"}),(0,e.jsx)(i.p,{id:"subject",name:"subject",type:"text",value:a.subject,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400",onChange:t})]}),(0,e.jsxs)("div",{children:[(0,e.jsx)(j.J,{htmlFor:"message",className:"text-amber-200",children:"Message"}),(0,e.jsx)(k.T,{id:"message",name:"message",rows:5,value:a.message,className:`mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ${r.message?"border-red-500":""}`,onChange:t}),r.message&&(0,e.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.message})]})]}),(0,e.jsx)(h.$,{type:"submit",disabled:c,className:`w-full bg-amber-600 hover:bg-amber-700 text-white py-6 text-lg font-medium transition-colors duration-200 ${c?"opacity-70 cursor-not-allowed":""}`,children:c?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,e.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,e.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):"Send Message"})]})]})]})]}),(0,e.jsx)(n.A,{})]})]})};d()}catch(a){d(a)}})},33873:a=>{a.exports=require("path")},40273:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{J:()=>l});var e=c(8732),f=c(82015),g=c(20598),h=c(8938),i=c(87092),j=a([g,h,i]);[g,h,i]=j.then?(await j)():j;let k=(0,h.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=f.forwardRef(({className:a,...b},c)=>(0,e.jsx)(g.Root,{ref:c,className:(0,i.cn)(k(),a),...b}));l.displayName=g.Root.displayName,d()}catch(a){d(a)}})},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},41415:a=>{a.exports=import("sonner")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},47860:a=>{a.exports=import("@radix-ui/react-select")},50802:a=>{a.exports=import("clsx")},57049:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var e=c(8732),f=c(82015),g=c(47860),h=c(74952),i=c(87092),j=a([g,i]);[g,i]=j.then?(await j)():j;let k=g.Root;g.Group;let l=g.Value,m=f.forwardRef(({className:a,children:b,...c},d)=>(0,e.jsxs)(g.Trigger,{ref:d,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,e.jsx)(g.Icon,{asChild:!0,children:(0,e.jsx)(h.yQN,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=g.Trigger.displayName;let n=f.forwardRef(({className:a,...b},c)=>(0,e.jsx)(g.ScrollUpButton,{ref:c,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,e.jsx)(h.rXn,{className:"h-4 w-4"})}));n.displayName=g.ScrollUpButton.displayName;let o=f.forwardRef(({className:a,...b},c)=>(0,e.jsx)(g.ScrollDownButton,{ref:c,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,e.jsx)(h.yQN,{className:"h-4 w-4"})}));o.displayName=g.ScrollDownButton.displayName;let p=f.forwardRef(({className:a,children:b,position:c="popper",...d},f)=>(0,e.jsx)(g.Portal,{children:(0,e.jsxs)(g.Content,{ref:f,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...d,children:[(0,e.jsx)(n,{}),(0,e.jsx)(g.Viewport,{className:(0,i.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,e.jsx)(o,{})]})}));p.displayName=g.Content.displayName,f.forwardRef(({className:a,...b},c)=>(0,e.jsx)(g.Label,{ref:c,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=g.Label.displayName;let q=f.forwardRef(({className:a,children:b,...c},d)=>(0,e.jsxs)(g.Item,{ref:d,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,e.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,e.jsx)(g.ItemIndicator,{children:(0,e.jsx)(h.Jlk,{className:"h-4 w-4"})})}),(0,e.jsx)(g.ItemText,{children:b})]}));q.displayName=g.Item.displayName,f.forwardRef(({className:a,...b},c)=>(0,e.jsx)(g.Separator,{ref:c,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=g.Separator.displayName,d()}catch(a){d(a)}})},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66345:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{$:()=>l});var e=c(8732),f=c(82015),g=c(9640),h=c(8938),i=c(87092),j=a([g,h,i]);[g,h,i]=j.then?(await j)():j;let k=(0,h.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=f.forwardRef(({className:a,variant:b,size:c,asChild:d=!1,...f},h)=>{let j=d?g.Slot:"button";return(0,e.jsx)(j,{className:(0,i.cn)(k({variant:b,size:c,className:a})),ref:h,...f})});l.displayName="Button",d()}catch(a){d(a)}})},75979:a=>{a.exports=import("tailwind-merge")},78059:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{p:()=>i});var e=c(8732),f=c(82015),g=c(87092),h=a([g]);g=(h.then?(await h)():h)[0];let i=f.forwardRef(({className:a,type:b,...c},d)=>(0,e.jsx)("input",{type:b,className:(0,g.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:d,...c}));i.displayName="Input",d()}catch(a){d(a)}})},82015:a=>{a.exports=require("react")},87092:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{cn:()=>h});var e=c(50802),f=c(75979),g=a([e,f]);function h(...a){return(0,f.twMerge)((0,e.clsx)(a))}[e,f]=g.then?(await g)():g,d()}catch(a){d(a)}})},87967:(a,b,c)=>{c.a(a,async(a,d)=>{try{c.d(b,{T:()=>i});var e=c(8732),f=c(82015),g=c(87092),h=a([g]);g=(h.then?(await h)():h)[0];let i=f.forwardRef(({className:a,...b},c)=>(0,e.jsx)("textarea",{className:(0,g.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));i.displayName="Textarea",d()}catch(a){d(a)}})}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157,952,277,696],()=>b(b.s=3327));module.exports=c})();