"use strict";(()=>{var a={};a.id=489,a.ids=[489,839],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},54822:a=>{a.exports=require("react-router-dom")},54976:(a,b,c)=>{c.r(b),c.d(b,{config:()=>v,default:()=>r,getServerSideProps:()=>u,getStaticPaths:()=>t,getStaticProps:()=>s,handler:()=>D,reportWebVitals:()=>w,routeModule:()=>C,unstable_getServerProps:()=>A,unstable_getServerSideProps:()=>B,unstable_getStaticParams:()=>z,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>x});var d={};c.r(d),c.d(d,{default:()=>p});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(8732),m=c(54822),n=c(74952);let o=({title:a,description:b})=>(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50",children:[(0,l.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-amber-200 z-50",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,l.jsx)(m.Link,{to:"/",className:"flex items-center space-x-2",children:(0,l.jsx)("img",{src:"/lovable-uploads/cfc50220-4059-4960-a0ea-0b328b8473e8.png",alt:"Shangrila Distillery",className:"h-16 w-auto"})}),(0,l.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,l.jsx)(m.Link,{to:"/",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Home"}),(0,l.jsx)(m.Link,{to:"/about",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"About"}),(0,l.jsx)(m.Link,{to:"/products",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Products"}),(0,l.jsx)(m.Link,{to:"/distillery",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Distillery"}),(0,l.jsx)(m.Link,{to:"/tours",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Tours"}),(0,l.jsx)(m.Link,{to:"/events",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Events"}),(0,l.jsx)(m.Link,{to:"/contact",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Contact"})]})]})})}),(0,l.jsx)("div",{className:"pt-20 flex items-center justify-center min-h-screen",children:(0,l.jsxs)("div",{className:"text-center max-w-2xl mx-auto px-4",children:[(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsx)("img",{src:"/lovable-uploads/cfc50220-4059-4960-a0ea-0b328b8473e8.png",alt:"Shangrila Distillery",className:"h-32 w-auto mx-auto mb-6 opacity-80"})}),(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-amber-900 mb-4",children:a}),(0,l.jsx)("h2",{className:"text-2xl md:text-3xl font-light text-amber-700 mb-6",children:"Coming Soon"}),(0,l.jsx)("p",{className:"text-lg text-amber-600 mb-8",children:b||"We're crafting something special for you. Stay tuned for updates on this exciting new addition to our distillery experience."}),(0,l.jsx)("div",{className:"bg-white/70 backdrop-blur-sm rounded-lg p-6 mb-8",children:(0,l.jsx)("p",{className:"text-amber-800 font-medium",children:"In the meantime, explore our current offerings and learn about our craft distilling process in the Himalayas."})}),(0,l.jsxs)(m.Link,{to:"/",className:"inline-flex items-center space-x-2 bg-amber-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-800 transition-colors shadow-lg",children:[(0,l.jsx)(n.nkM,{className:"h-5 w-5"}),(0,l.jsx)("span",{children:"Back to Home"})]})]})})]}),p=()=>(0,l.jsx)(o,{title:"The Distillery",description:"Take a virtual tour of our facilities, learn about our distilling process, and see the equipment and techniques that make our spirits extraordinary."});var q=c(12289);let r=(0,g.M)(d,"default"),s=(0,g.M)(d,"getStaticProps"),t=(0,g.M)(d,"getStaticPaths"),u=(0,g.M)(d,"getServerSideProps"),v=(0,g.M)(d,"config"),w=(0,g.M)(d,"reportWebVitals"),x=(0,g.M)(d,"unstable_getStaticProps"),y=(0,g.M)(d,"unstable_getStaticPaths"),z=(0,g.M)(d,"unstable_getStaticParams"),A=(0,g.M)(d,"unstable_getServerProps"),B=(0,g.M)(d,"unstable_getServerSideProps"),C=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/Distillery",pathname:"/Distillery",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:d}),D=(0,q.U)({srcPage:"/Distillery",config:v,userland:d,routeModule:C,getStaticPaths:t,getStaticProps:s,getServerSideProps:u})},82015:a=>{a.exports=require("react")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157,952],()=>b(b.s=54976));module.exports=c})();