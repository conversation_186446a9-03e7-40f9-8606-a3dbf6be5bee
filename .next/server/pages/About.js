"use strict";(()=>{var a={};a.id=919,a.ids=[839,919],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19676:(a,b,c)=>{c.r(b),c.d(b,{config:()=>D,default:()=>z,getServerSideProps:()=>C,getStaticPaths:()=>B,getStaticProps:()=>A,handler:()=>L,reportWebVitals:()=>E,routeModule:()=>K,unstable_getServerProps:()=>I,unstable_getServerSideProps:()=>J,unstable_getStaticParams:()=>H,unstable_getStaticPaths:()=>G,unstable_getStaticProps:()=>F});var d={};c.r(d),c.d(d,{default:()=>x});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(8732),m=c(88204),n=c(32829);let o=()=>(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Our Story"}),(0,l.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Shangrila Distillery represents the pinnacle of Nepalese spirit craftsmanship, where tradition meets innovation in the heart of the Himalayas."})]});var p=c(74952);let q=()=>(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 mb-20",children:[(0,l.jsxs)("div",{className:"distillery-card p-8",children:[(0,l.jsxs)("div",{className:"flex items-center mb-6",children:[(0,l.jsx)(p.DTr,{className:"h-8 w-8 text-amber-400 mr-3"}),(0,l.jsx)("h2",{className:"text-2xl font-playfair font-bold text-amber-100",children:"Our Mission"})]}),(0,l.jsx)("p",{className:"distillery-text text-lg leading-relaxed",children:"To craft exceptional quality spirits in Nepal using traditional methods combined with modern technology, creating products that represent the finest of Nepalese craftsmanship and capture the essence of our Himalayan heritage through expert blending and innovation."})]}),(0,l.jsxs)("div",{className:"distillery-card p-8",children:[(0,l.jsxs)("div",{className:"flex items-center mb-6",children:[(0,l.jsx)(p.kU3,{className:"h-8 w-8 text-amber-400 mr-3"}),(0,l.jsx)("h2",{className:"text-2xl font-playfair font-bold text-amber-100",children:"Our Vision"})]}),(0,l.jsx)("p",{className:"distillery-text text-lg leading-relaxed",children:"To be recognized as a premium liquor brand both in Nepal's domestic market and international markets worldwide, setting new standards for quality and authenticity while proudly representing Nepal's rich distilling heritage on the global stage."})]})]}),r=()=>{let a=[{icon:p.wAm,title:"Quality Excellence",description:"Uncompromising commitment to the highest standards"},{icon:p.zWC,title:"Craftsmanship",description:"Traditional techniques combined with modern innovation"},{icon:p.sDd,title:"Nepalese Heritage",description:"Proudly representing Nepal's rich cultural traditions"}];return(0,l.jsxs)("div",{className:"mb-20",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-12 text-center",children:"Our Values"}),(0,l.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:a.map(a=>(0,l.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(a.icon,{className:"h-8 w-8 text-amber-400"})}),(0,l.jsx)("h3",{className:"text-xl font-playfair font-semibold text-amber-100 mb-2",children:a.title}),(0,l.jsx)("p",{className:"distillery-text",children:a.description})]},a.title))})]})},s=()=>(0,l.jsxs)("div",{className:"mb-20",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-12 text-center",children:"Our Journey"}),(0,l.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{year:"2023",title:"Foundation",description:"Our group acquired the company and started building our facilities"},{year:"2024",title:"Partnership",description:"Strategic alliance with Trade Vision Partners, Australia"},{year:"2025",title:"Launch",description:"First product line launch and domestic market entry"},{year:"2026",title:"Expansion",description:"International market expansion and capacity increase"}].map(a=>(0,l.jsxs)("div",{className:"distillery-card p-6 text-center hover:scale-105 transition-all duration-300",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-amber-400 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(p.VvS,{className:"h-6 w-6 text-stone-900"})}),(0,l.jsx)("div",{className:"text-2xl font-playfair font-bold text-amber-400 mb-2",children:a.year}),(0,l.jsx)("h3",{className:"text-lg font-crimson font-semibold text-amber-100 mb-2",children:a.title}),(0,l.jsx)("p",{className:"distillery-text text-sm",children:a.description})]},a.year))})]}),t=()=>(0,l.jsxs)("div",{className:"distillery-card p-8 mb-20",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Company Background"}),(0,l.jsxs)("div",{className:"space-y-6 distillery-text",children:[(0,l.jsx)("p",{className:"text-lg leading-relaxed",children:"Founded with a vision to elevate Nepalese spirits to international standards, Shangrila Distillery began as a dream to showcase the unique terroir and craftsmanship capabilities of Nepal to the world. Our commitment to quality and blending expertise sets us apart in the industry."}),(0,l.jsx)("p",{className:"text-lg leading-relaxed",children:"Our state-of-the-art facility combines traditional distilling techniques passed down through generations with cutting-edge technology and innovative blending methods to ensure consistency, quality, and scalability without compromising on the artisanal character that makes our spirits unique."}),(0,l.jsx)("p",{className:"text-lg leading-relaxed",children:"Through continuous innovation and unwavering focus on quality, we are committed to expanding our portfolio for both domestic and international markets, establishing Shangrila as a globally recognized premium spirits brand while maintaining our commitment to authentic Nepalese heritage."})]})]});var u=c(19918),v=c.n(u);let w=()=>(0,l.jsx)("div",{className:"py-20",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsx)("div",{className:"text-center mb-12",children:(0,l.jsx)("h2",{className:"text-4xl md:text-5xl font-playfair font-bold text-amber-100",children:"A Word From Our Managing Director"})}),(0,l.jsx)("div",{className:"max-w-xl mx-auto",children:(0,l.jsx)(v(),{href:"/blog/prajanna-raj-adhikari-leading-shangrila-distillery",children:(0,l.jsx)("img",{src:"/lovable-uploads/prashanna.png",alt:"Prajanna Raj Adhikari",className:"rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"})})})]})}),x=()=>(0,l.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,l.jsx)(m.A,{}),(0,l.jsxs)("div",{className:"pt-20",children:[(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,l.jsx)(o,{}),(0,l.jsx)(q,{}),(0,l.jsx)(w,{}),(0,l.jsx)(r,{}),(0,l.jsx)(s,{}),(0,l.jsx)(t,{}),(0,l.jsxs)("div",{className:"mb-20",children:[(0,l.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Strategic Partnerships"}),(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,l.jsxs)("div",{className:"distillery-card p-8 text-center",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,l.jsx)("span",{className:"text-xl font-bold text-amber-200 mr-3",children:"Trade Vision Partners"}),(0,l.jsx)("a",{href:"https://tradevisionpartners.com/",target:"_blank",rel:"noopener noreferrer",className:"text-amber-400 hover:text-amber-300 transition-colors",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2h6m5-1l2 2m0 0l-7 7m7-7V3m0 4h-4"})})})]}),(0,l.jsx)("span",{className:"block text-amber-300 mb-2",children:"Australia"}),(0,l.jsx)("span",{className:"block distillery-text text-base mb-2",children:"Our flagship partnership brings decades of international distribution expertise, opening doors to premium markets across Australia and beyond. Together, we're setting new standards for Nepalese spirits on the global stage."})]}),(0,l.jsxs)("div",{className:"distillery-card p-8 text-center",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,l.jsx)("span",{className:"text-xl font-bold text-amber-200 mr-3",children:"ShyamBaba Group"}),(0,l.jsx)("a",{href:"https://sbgcompanies.com/",target:"_blank",rel:"noopener noreferrer",className:"text-amber-400 hover:text-amber-300 transition-colors",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 inline",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2h6m5-1l2 2m0 0l-7 7m7-7V3m0 4h-4"})})})]}),(0,l.jsx)("span",{className:"block text-amber-300 mb-2",children:"Nepal"}),(0,l.jsx)("span",{className:"block distillery-text text-base mb-2",children:"Nepal's foremost diversified business group with interests in Food Products, Cement, Construction, Manufacturing and Trading."}),(0,l.jsx)("span",{className:"block distillery-text text-base",children:"With over 2000 employees and 10,000 direct customers, ShyamBaba Group touches lives across Nepal through its vast offering of market-leading high quality consumer products."})]})]})]})]}),(0,l.jsx)(n.A,{})]})]});var y=c(12289);let z=(0,g.M)(d,"default"),A=(0,g.M)(d,"getStaticProps"),B=(0,g.M)(d,"getStaticPaths"),C=(0,g.M)(d,"getServerSideProps"),D=(0,g.M)(d,"config"),E=(0,g.M)(d,"reportWebVitals"),F=(0,g.M)(d,"unstable_getStaticProps"),G=(0,g.M)(d,"unstable_getStaticPaths"),H=(0,g.M)(d,"unstable_getStaticParams"),I=(0,g.M)(d,"unstable_getServerProps"),J=(0,g.M)(d,"unstable_getServerSideProps"),K=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/About",pathname:"/About",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:d}),L=(0,y.U)({srcPage:"/About",config:D,userland:d,routeModule:K,getStaticPaths:B,getStaticProps:A,getServerSideProps:C})},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82015:a=>{a.exports=require("react")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157,952,277,696],()=>b(b.s=19676));module.exports=c})();