"use strict";(()=>{var a={};a.id=657,a.ids=[657,839],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},46060:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external.js")},50296:(a,b,c)=>{c.r(b),c.d(b,{config:()=>x,default:()=>t,getServerSideProps:()=>w,getStaticPaths:()=>v,getStaticProps:()=>u,handler:()=>F,reportWebVitals:()=>y,routeModule:()=>E,unstable_getServerProps:()=>C,unstable_getServerSideProps:()=>D,unstable_getStaticParams:()=>B,unstable_getStaticPaths:()=>A,unstable_getStaticProps:()=>z});var d={};c.r(d),c.d(d,{default:()=>r});var e=c(63885),f=c(80237),g=c(81413),h=c(65611),i=c.n(h),j=c(625),k=c.n(j),l=c(8732),m=c(82015),n=c(54822),o=c(88204),p=c(32829),q=c(74952);let r=()=>{let[a,b]=(0,m.useState)(null),c=[{id:1,name:"Prajanna Raj Adhikari",position:"Managing Director",image:"/lovable-uploads/prashanna.png",shortDescription:"Prajanna Raj Adhikari is a dynamic and forward-thinking leader with a deep-rooted passion for the liquor and beverage industry.",fullDescription:`Prajanna Raj Adhikari, the Managing Director of Shangrila Distillery, is a dynamic and forward-thinking leader with a deep-rooted passion for the liquor and beverage industry. He began his journey in Australia, where he gained invaluable exposure to world-class distilleries and participated in advanced blending workshops, sharpening his technical and operational understanding of premium spirits production.

He holds a Bachelor of Business Administration from Federation University and a Master of Business Administration from Victoria University, Australia—credentials that reflect his strong academic foundation in global business and management.

With a strategic vision and entrepreneurial spirit, he is committed to establishing Shangrila Distillery as a premier name in Nepal's liquor industry. His ambition is not only to develop a robust domestic presence but also to position Shangrila as a competitive exporter of high-quality spirits. Under his leadership, the distillery aims to contribute significantly to Nepal's economic landscape by enhancing the country's export footprint and reputation for excellence in craftsmanship.

Regarded as a rising figure in the industry, Prajanna Raj Adhikari brings clarity of purpose, global perspective, and relentless drive—qualities that are shaping Shangrila Distillery's path to becoming a distinguished force in the world of spirits.`},{id:2,name:"Prameshwor Raj Adhikari",position:"Executive Director",image:"/lovable-uploads/favicon-shangrila.png",shortDescription:"With decades of experience in the construction industry, Prameshwor Raj Adhikari brings deep business insight and a strong foundation in infrastructure development.",fullDescription:`Prameshwor Raj Adhikari brings decades of experience in the construction industry, providing deep business insight and a strong foundation in infrastructure development to Shangrila Distillery. His extensive background in construction and project management has been instrumental in shaping the physical foundation of our distillery operations.

He single-handedly designed the entire layout and facility of Shangrila Distillery, blending practical engineering with visionary planning. His expertise in infrastructure development ensures that our distillery operates with maximum efficiency while maintaining the highest standards of safety and quality.

With his comprehensive understanding of business operations and construction management, Prameshwor Raj Adhikari plays a crucial role in the strategic planning and execution of our expansion projects. His vision extends beyond just building structures; he creates environments where craftsmanship and innovation can flourish.

His leadership in facility design and operational planning has positioned Shangrila Distillery as a state-of-the-art production facility that honors traditional distilling methods while embracing modern efficiency and sustainability practices.`},{id:3,name:"Sanskar Agrawal",position:"Executive Director",image:"/lovable-uploads/favicon-shangrila.png",shortDescription:"Sanskar Agrawal brings strategic insight and operational excellence to drive innovation and growth at Shangrila Distillery.",fullDescription:`Sanskar Agrawal serves as Executive Director at Shangrila Distillery, bringing a wealth of strategic insight and operational excellence to the organization. His leadership style combines analytical thinking with innovative approaches to business development and operational efficiency.

With a strong background in business strategy and operations management, Sanskar plays a pivotal role in driving the company's growth initiatives and ensuring operational excellence across all departments. His expertise in strategic planning and business development has been instrumental in positioning Shangrila Distillery for sustainable growth and market expansion.

Sanskar's commitment to innovation and quality excellence aligns perfectly with Shangrila Distillery's mission to produce world-class spirits. He oversees various operational aspects of the business, ensuring that every process meets the highest standards of quality and efficiency.

His forward-thinking approach and dedication to continuous improvement make him an invaluable member of the leadership team, contributing significantly to the company's vision of becoming a leading name in the global spirits industry.`}],d=()=>{b(null),document.body.style.overflow="auto"},e=a?c.find(b=>b.id===a):null;return(0,l.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,l.jsx)(o.A,{}),(0,l.jsxs)("div",{className:"pt-20",children:[(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20",children:[(0,l.jsxs)("div",{className:"text-center mb-12 sm:mb-16",children:[(0,l.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4 sm:mb-6",children:"Our Leadership"}),(0,l.jsx)("p",{className:"text-base sm:text-lg lg:text-xl text-amber-100/70 max-w-3xl mx-auto font-crimson leading-relaxed px-2",children:"Meet the visionaries guiding Shangrila Distillery to new heights of excellence and innovation."})]}),(0,l.jsx)("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:c.map(a=>1===a.id?(0,l.jsxs)(n.Link,{to:"/leadership/prajanna-raj-adhikari",className:"group cursor-pointer distillery-card p-6 text-center hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-amber-500/20 block",children:[(0,l.jsx)("div",{className:"relative mb-6 overflow-hidden rounded-lg mx-auto w-32 h-32 sm:w-40 sm:h-40",children:(0,l.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 border-4 border-amber-400/40 rounded-lg"})}),(0,l.jsx)("h3",{className:"text-xl sm:text-2xl font-playfair font-bold text-amber-100 mb-2",children:a.name}),(0,l.jsx)("p",{className:"text-amber-300 font-medium font-crimson mb-4",children:a.position}),(0,l.jsx)("p",{className:"text-amber-100/80 font-crimson text-sm leading-relaxed",children:a.shortDescription}),(0,l.jsx)("div",{className:"mt-4 text-amber-400 text-sm font-medium",children:"Learn more about Prajanna Raj Adhikari →"})]},a.id):(0,l.jsxs)("div",{onClick:()=>{b(a.id),document.body.style.overflow="hidden"},className:"group cursor-pointer distillery-card p-6 text-center hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-amber-500/20",children:[(0,l.jsx)("div",{className:"relative mb-6 overflow-hidden rounded-lg mx-auto w-32 h-32 sm:w-40 sm:h-40",children:(0,l.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 border-4 border-amber-400/40 rounded-lg"})}),(0,l.jsx)("h3",{className:"text-xl sm:text-2xl font-playfair font-bold text-amber-100 mb-2",children:a.name}),(0,l.jsx)("p",{className:"text-amber-300 font-medium font-crimson mb-4",children:a.position}),(0,l.jsx)("p",{className:"text-amber-100/80 font-crimson text-sm leading-relaxed",children:a.shortDescription}),(0,l.jsx)("div",{className:"mt-4 text-amber-400 text-sm font-medium",children:"Click to read more →"})]},a.id))})]}),(0,l.jsx)(p.A,{})]}),null!==a&&e&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",onClick:d,children:(0,l.jsxs)("div",{className:"relative bg-stone-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-amber-400/30 shadow-2xl shadow-amber-500/10",onClick:a=>a.stopPropagation(),children:[(0,l.jsx)("button",{onClick:d,className:"absolute top-4 right-4 text-amber-100 hover:text-amber-300 transition-colors p-2 z-10",children:(0,l.jsx)(q.X,{className:"h-6 w-6"})}),(0,l.jsx)("div",{className:"bg-gradient-to-r from-amber-900/30 to-amber-800/30 p-6 sm:p-8",children:(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-6",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("img",{src:e.image,alt:e.name,className:"w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 rounded-lg border-4 border-amber-400/60 shadow-xl object-cover bg-white/5"})}),(0,l.jsxs)("div",{className:"text-center sm:text-left",children:[(0,l.jsx)("h2",{className:"text-2xl sm:text-3xl font-playfair font-bold text-amber-100 mb-2",children:e.name}),(0,l.jsx)("p",{className:"text-lg text-amber-200 font-semibold mb-2",children:e.position}),(0,l.jsx)("p",{className:"text-amber-100/80 font-crimson",children:"Shangrila Distillery"})]})]})}),(0,l.jsxs)("div",{className:"p-6 sm:p-8",children:[(0,l.jsx)("div",{className:"space-y-4 text-amber-100/90 font-crimson leading-relaxed",children:e.fullDescription.split("\n\n").map((a,b)=>(0,l.jsx)("p",{className:"text-justify",children:a},b))}),(0,l.jsx)("div",{className:"mt-8 pt-6 border-t border-amber-400/20",children:(0,l.jsxs)("div",{className:"text-right font-semibold text-amber-200 font-crimson",children:["— ",e.position,(0,l.jsx)("br",{}),"Shangrila Distillery"]})})]})]})})]})};var s=c(12289);let t=(0,g.M)(d,"default"),u=(0,g.M)(d,"getStaticProps"),v=(0,g.M)(d,"getStaticPaths"),w=(0,g.M)(d,"getServerSideProps"),x=(0,g.M)(d,"config"),y=(0,g.M)(d,"reportWebVitals"),z=(0,g.M)(d,"unstable_getStaticProps"),A=(0,g.M)(d,"unstable_getStaticPaths"),B=(0,g.M)(d,"unstable_getStaticParams"),C=(0,g.M)(d,"unstable_getServerProps"),D=(0,g.M)(d,"unstable_getServerSideProps"),E=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/Leadership",pathname:"/Leadership",bundlePath:"",filename:""},distDir:".next",relativeProjectDir:"",components:{App:k(),Document:i()},userland:d}),F=(0,s.U)({srcPage:"/Leadership",config:x,userland:d,routeModule:E,getStaticPaths:v,getStaticProps:u,getServerSideProps:w})},54822:a=>{a.exports=require("react-router-dom")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82015:a=>{a.exports=require("react")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[611,157,952,277,696],()=>b(b.s=50296));module.exports=c})();