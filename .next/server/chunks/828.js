exports.id=828,exports.ids=[828],exports.modules={1042:(a,b,c)=>{a.exports=c(84933)},1189:(a,b,c)=>{"use strict";var d=c(73007);a.exports=function(a){return d(a)||0===a?a:a<0?-1:1}},4245:a=>{"use strict";a.exports=Object.getOwnPropertyDescriptor},7864:a=>{"use strict";a.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},8767:a=>{"use strict";a.exports=TypeError},9148:(a,b,c)=>{var d=c(57455),e=c(46449);a.exports=function(a){Object.keys(this.jobs).length&&(this.index=this.size,d(this),e(a)(null,this.results))}},9841:(a,b,c)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?a.exports=c(19777):a.exports=c(73097)},10153:(a,b,c)=>{var d=c(28354),e=c(27910).Stream,f=c(11701);function g(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}a.exports=g,d.inherits(g,e),g.create=function(a){var b=new this;for(var c in a=a||{})b[c]=a[c];return b},g.isStreamLike=function(a){return"function"!=typeof a&&"string"!=typeof a&&"boolean"!=typeof a&&"number"!=typeof a&&!Buffer.isBuffer(a)},g.prototype.append=function(a){if(g.isStreamLike(a)){if(!(a instanceof f)){var b=f.create(a,{maxDataSize:1/0,pauseStream:this.pauseStreams});a.on("data",this._checkDataSize.bind(this)),a=b}this._handleErrors(a),this.pauseStreams&&a.pause()}return this._streams.push(a),this},g.prototype.pipe=function(a,b){return e.prototype.pipe.call(this,a,b),this.resume(),a},g.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},g.prototype._realGetNext=function(){var a=this._streams.shift();return void 0===a?void this.end():"function"!=typeof a?void this._pipeNext(a):void a((function(a){g.isStreamLike(a)&&(a.on("data",this._checkDataSize.bind(this)),this._handleErrors(a)),this._pipeNext(a)}).bind(this))},g.prototype._pipeNext=function(a){if(this._currentStream=a,g.isStreamLike(a)){a.on("end",this._getNext.bind(this)),a.pipe(this,{end:!1});return}this.write(a),this._getNext()},g.prototype._handleErrors=function(a){var b=this;a.on("error",function(a){b._emitError(a)})},g.prototype.write=function(a){this.emit("data",a)},g.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},g.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},g.prototype.end=function(){this._reset(),this.emit("end")},g.prototype.destroy=function(){this._reset(),this.emit("close")},g.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},g.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var a="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(a))}},g.prototype._updateDataSize=function(){this.dataSize=0;var a=this;this._streams.forEach(function(b){b.dataSize&&(a.dataSize+=b.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},g.prototype._emitError=function(a){this._reset(),this.emit("error",a)}},11701:(a,b,c)=>{var d=c(27910).Stream,e=c(28354);function f(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}a.exports=f,e.inherits(f,d),f.create=function(a,b){var c=new this;for(var d in b=b||{})c[d]=b[d];c.source=a;var e=a.emit;return a.emit=function(){return c._handleEmit(arguments),e.apply(a,arguments)},a.on("error",function(){}),c.pauseStream&&a.pause(),c},Object.defineProperty(f.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),f.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},f.prototype.resume=function(){this._released||this.release(),this.source.resume()},f.prototype.pause=function(){this.source.pause()},f.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(a){this.emit.apply(this,a)}).bind(this)),this._bufferedEvents=[]},f.prototype.pipe=function(){var a=d.prototype.pipe.apply(this,arguments);return this.resume(),a},f.prototype._handleEmit=function(a){if(this._released)return void this.emit.apply(this,a);"data"===a[0]&&(this.dataSize+=a[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(a)},f.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var a="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(a))}}},13222:(a,b,c)=>{"use strict";let d;c.d(b,{A:()=>bN});var e,f,g,h={};function i(a,b){return function(){return a.apply(b,arguments)}}c.r(h),c.d(h,{hasBrowserEnv:()=>ao,hasStandardBrowserEnv:()=>aq,hasStandardBrowserWebWorkerEnv:()=>ar,navigator:()=>ap,origin:()=>as});let{toString:j}=Object.prototype,{getPrototypeOf:k}=Object,{iterator:l,toStringTag:m}=Symbol,n=(a=>b=>{let c=j.call(b);return a[c]||(a[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),o=a=>(a=a.toLowerCase(),b=>n(b)===a),p=a=>b=>typeof b===a,{isArray:q}=Array,r=p("undefined"),s=o("ArrayBuffer"),t=p("string"),u=p("function"),v=p("number"),w=a=>null!==a&&"object"==typeof a,x=a=>{if("object"!==n(a))return!1;let b=k(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&!(m in a)&&!(l in a)},y=o("Date"),z=o("File"),A=o("Blob"),B=o("FileList"),C=o("URLSearchParams"),[D,E,F,G]=["ReadableStream","Request","Response","Headers"].map(o);function H(a,b,{allOwnKeys:c=!1}={}){let d,e;if(null!=a)if("object"!=typeof a&&(a=[a]),q(a))for(d=0,e=a.length;d<e;d++)b.call(null,a[d],d,a);else{let e,f=c?Object.getOwnPropertyNames(a):Object.keys(a),g=f.length;for(d=0;d<g;d++)e=f[d],b.call(null,a[e],e,a)}}function I(a,b){let c;b=b.toLowerCase();let d=Object.keys(a),e=d.length;for(;e-- >0;)if(b===(c=d[e]).toLowerCase())return c;return null}let J="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,K=a=>!r(a)&&a!==J,L=(a=>b=>a&&b instanceof a)("undefined"!=typeof Uint8Array&&k(Uint8Array)),M=o("HTMLFormElement"),N=(({hasOwnProperty:a})=>(b,c)=>a.call(b,c))(Object.prototype),O=o("RegExp"),P=(a,b)=>{let c=Object.getOwnPropertyDescriptors(a),d={};H(c,(c,e)=>{let f;!1!==(f=b(c,e,a))&&(d[e]=f||c)}),Object.defineProperties(a,d)},Q=o("AsyncFunction"),R=(e="function"==typeof setImmediate,f=u(J.postMessage),e?setImmediate:f?((a,b)=>(J.addEventListener("message",({source:c,data:d})=>{c===J&&d===a&&b.length&&b.shift()()},!1),c=>{b.push(c),J.postMessage(a,"*")}))(`axios@${Math.random()}`,[]):a=>setTimeout(a)),S="undefined"!=typeof queueMicrotask?queueMicrotask.bind(J):"undefined"!=typeof process&&process.nextTick||R,T={isArray:q,isArrayBuffer:s,isBuffer:function(a){return null!==a&&!r(a)&&null!==a.constructor&&!r(a.constructor)&&u(a.constructor.isBuffer)&&a.constructor.isBuffer(a)},isFormData:a=>{let b;return a&&("function"==typeof FormData&&a instanceof FormData||u(a.append)&&("formdata"===(b=n(a))||"object"===b&&u(a.toString)&&"[object FormData]"===a.toString()))},isArrayBufferView:function(a){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(a):a&&a.buffer&&s(a.buffer)},isString:t,isNumber:v,isBoolean:a=>!0===a||!1===a,isObject:w,isPlainObject:x,isReadableStream:D,isRequest:E,isResponse:F,isHeaders:G,isUndefined:r,isDate:y,isFile:z,isBlob:A,isRegExp:O,isFunction:u,isStream:a=>w(a)&&u(a.pipe),isURLSearchParams:C,isTypedArray:L,isFileList:B,forEach:H,merge:function a(){let{caseless:b}=K(this)&&this||{},c={},d=(d,e)=>{let f=b&&I(c,e)||e;x(c[f])&&x(d)?c[f]=a(c[f],d):x(d)?c[f]=a({},d):q(d)?c[f]=d.slice():c[f]=d};for(let a=0,b=arguments.length;a<b;a++)arguments[a]&&H(arguments[a],d);return c},extend:(a,b,c,{allOwnKeys:d}={})=>(H(b,(b,d)=>{c&&u(b)?a[d]=i(b,c):a[d]=b},{allOwnKeys:d}),a),trim:a=>a.trim?a.trim():a.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:a=>(65279===a.charCodeAt(0)&&(a=a.slice(1)),a),inherits:(a,b,c,d)=>{a.prototype=Object.create(b.prototype,d),a.prototype.constructor=a,Object.defineProperty(a,"super",{value:b.prototype}),c&&Object.assign(a.prototype,c)},toFlatObject:(a,b,c,d)=>{let e,f,g,h={};if(b=b||{},null==a)return b;do{for(f=(e=Object.getOwnPropertyNames(a)).length;f-- >0;)g=e[f],(!d||d(g,a,b))&&!h[g]&&(b[g]=a[g],h[g]=!0);a=!1!==c&&k(a)}while(a&&(!c||c(a,b))&&a!==Object.prototype);return b},kindOf:n,kindOfTest:o,endsWith:(a,b,c)=>{a=String(a),(void 0===c||c>a.length)&&(c=a.length),c-=b.length;let d=a.indexOf(b,c);return -1!==d&&d===c},toArray:a=>{if(!a)return null;if(q(a))return a;let b=a.length;if(!v(b))return null;let c=Array(b);for(;b-- >0;)c[b]=a[b];return c},forEachEntry:(a,b)=>{let c,d=(a&&a[l]).call(a);for(;(c=d.next())&&!c.done;){let d=c.value;b.call(a,d[0],d[1])}},matchAll:(a,b)=>{let c,d=[];for(;null!==(c=a.exec(b));)d.push(c);return d},isHTMLForm:M,hasOwnProperty:N,hasOwnProp:N,reduceDescriptors:P,freezeMethods:a=>{P(a,(b,c)=>{if(u(a)&&-1!==["arguments","caller","callee"].indexOf(c))return!1;if(u(a[c])){if(b.enumerable=!1,"writable"in b){b.writable=!1;return}b.set||(b.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},toObjectSet:(a,b)=>{let c={};return(q(a)?a:String(a).split(b)).forEach(a=>{c[a]=!0}),c},toCamelCase:a=>a.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(a,b,c){return b.toUpperCase()+c}),noop:()=>{},toFiniteNumber:(a,b)=>null!=a&&Number.isFinite(a*=1)?a:b,findKey:I,global:J,isContextDefined:K,isSpecCompliantForm:function(a){return!!(a&&u(a.append)&&"FormData"===a[m]&&a[l])},toJSONObject:a=>{let b=Array(10),c=(a,d)=>{if(w(a)){if(b.indexOf(a)>=0)return;if(!("toJSON"in a)){b[d]=a;let e=q(a)?[]:{};return H(a,(a,b)=>{let f=c(a,d+1);r(f)||(e[b]=f)}),b[d]=void 0,e}}return a};return c(a,0)},isAsyncFn:Q,isThenable:a=>a&&(w(a)||u(a))&&u(a.then)&&u(a.catch),setImmediate:R,asap:S,isIterable:a=>null!=a&&u(a[l])};function U(a,b,c,d,e){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=a,this.name="AxiosError",b&&(this.code=b),c&&(this.config=c),d&&(this.request=d),e&&(this.response=e,this.status=e.status?e.status:null)}T.inherits(U,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:T.toJSONObject(this.config),code:this.code,status:this.status}}});let V=U.prototype,W={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(a=>{W[a]={value:a}}),Object.defineProperties(U,W),Object.defineProperty(V,"isAxiosError",{value:!0}),U.from=(a,b,c,d,e,f)=>{let g=Object.create(V);return T.toFlatObject(a,g,function(a){return a!==Error.prototype},a=>"isAxiosError"!==a),U.call(g,a.message,b,c,d,e),g.cause=a,g.name=a.name,f&&Object.assign(g,f),g};var X=c(35609);function Y(a){return T.isPlainObject(a)||T.isArray(a)}function Z(a){return T.endsWith(a,"[]")?a.slice(0,-2):a}function $(a,b,c){return a?a.concat(b).map(function(a,b){return a=Z(a),!c&&b?"["+a+"]":a}).join(c?".":""):b}let _=T.toFlatObject(T,{},null,function(a){return/^is[A-Z]/.test(a)}),aa=function(a,b,c){if(!T.isObject(a))throw TypeError("target must be an object");b=b||new(X||FormData);let d=(c=T.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(a,b){return!T.isUndefined(b[a])})).metaTokens,e=c.visitor||j,f=c.dots,g=c.indexes,h=(c.Blob||"undefined"!=typeof Blob&&Blob)&&T.isSpecCompliantForm(b);if(!T.isFunction(e))throw TypeError("visitor must be a function");function i(a){if(null===a)return"";if(T.isDate(a))return a.toISOString();if(T.isBoolean(a))return a.toString();if(!h&&T.isBlob(a))throw new U("Blob is not supported. Use a Buffer instead.");return T.isArrayBuffer(a)||T.isTypedArray(a)?h&&"function"==typeof Blob?new Blob([a]):Buffer.from(a):a}function j(a,c,e){let h=a;if(a&&!e&&"object"==typeof a)if(T.endsWith(c,"{}"))c=d?c:c.slice(0,-2),a=JSON.stringify(a);else{var j;if(T.isArray(a)&&(j=a,T.isArray(j)&&!j.some(Y))||(T.isFileList(a)||T.endsWith(c,"[]"))&&(h=T.toArray(a)))return c=Z(c),h.forEach(function(a,d){T.isUndefined(a)||null===a||b.append(!0===g?$([c],d,f):null===g?c:c+"[]",i(a))}),!1}return!!Y(a)||(b.append($(e,c,f),i(a)),!1)}let k=[],l=Object.assign(_,{defaultVisitor:j,convertValue:i,isVisitable:Y});if(!T.isObject(a))throw TypeError("data must be an object");return!function a(c,d){if(!T.isUndefined(c)){if(-1!==k.indexOf(c))throw Error("Circular reference detected in "+d.join("."));k.push(c),T.forEach(c,function(c,f){!0===(!(T.isUndefined(c)||null===c)&&e.call(b,c,T.isString(f)?f.trim():f,d,l))&&a(c,d?d.concat(f):[f])}),k.pop()}}(a),b};function ab(a){let b={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(a).replace(/[!'()~]|%20|%00/g,function(a){return b[a]})}function ac(a,b){this._pairs=[],a&&aa(a,this,b)}let ad=ac.prototype;function ae(a){return encodeURIComponent(a).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function af(a,b,c){let d;if(!b)return a;let e=c&&c.encode||ae;T.isFunction(c)&&(c={serialize:c});let f=c&&c.serialize;if(d=f?f(b,c):T.isURLSearchParams(b)?b.toString():new ac(b,c).toString(e)){let b=a.indexOf("#");-1!==b&&(a=a.slice(0,b)),a+=(-1===a.indexOf("?")?"?":"&")+d}return a}ad.append=function(a,b){this._pairs.push([a,b])},ad.toString=function(a){let b=a?function(b){return a.call(this,b,ab)}:ab;return this._pairs.map(function(a){return b(a[0])+"="+b(a[1])},"").join("&")};class ag{constructor(){this.handlers=[]}use(a,b,c){return this.handlers.push({fulfilled:a,rejected:b,synchronous:!!c&&c.synchronous,runWhen:c?c.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){T.forEach(this.handlers,function(b){null!==b&&a(b)})}}let ah={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var ai=c(55511);let aj=c(79551).URLSearchParams,ak="abcdefghijklmnopqrstuvwxyz",al="0123456789",am={DIGIT:al,ALPHA:ak,ALPHA_DIGIT:ak+ak.toUpperCase()+al},an={isNode:!0,classes:{URLSearchParams:aj,FormData:X,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:am,generateString:(a=16,b=am.ALPHA_DIGIT)=>{let c="",{length:d}=b,e=new Uint32Array(a);ai.randomFillSync(e);for(let f=0;f<a;f++)c+=b[e[f]%d];return c},protocols:["http","https","file","data"]},ao="undefined"!=typeof window&&"undefined"!=typeof document,ap="object"==typeof navigator&&navigator||void 0,aq=ao&&(!ap||0>["ReactNative","NativeScript","NS"].indexOf(ap.product)),ar="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,as=ao&&window.location.href||"http://localhost",at={...h,...an},au=function(a){if(T.isFormData(a)&&T.isFunction(a.entries)){let b={};return T.forEachEntry(a,(a,c)=>{!function a(b,c,d,e){let f=b[e++];if("__proto__"===f)return!0;let g=Number.isFinite(+f),h=e>=b.length;return(f=!f&&T.isArray(d)?d.length:f,h)?T.hasOwnProp(d,f)?d[f]=[d[f],c]:d[f]=c:(d[f]&&T.isObject(d[f])||(d[f]=[]),a(b,c,d[f],e)&&T.isArray(d[f])&&(d[f]=function(a){let b,c,d={},e=Object.keys(a),f=e.length;for(b=0;b<f;b++)d[c=e[b]]=a[c];return d}(d[f]))),!g}(T.matchAll(/\w+|\[(\w*)]/g,a).map(a=>"[]"===a[0]?"":a[1]||a[0]),c,b,0)}),b}return null},av={transitional:ah,adapter:["xhr","http","fetch"],transformRequest:[function(a,b){let c,d=b.getContentType()||"",e=d.indexOf("application/json")>-1,f=T.isObject(a);if(f&&T.isHTMLForm(a)&&(a=new FormData(a)),T.isFormData(a))return e?JSON.stringify(au(a)):a;if(T.isArrayBuffer(a)||T.isBuffer(a)||T.isStream(a)||T.isFile(a)||T.isBlob(a)||T.isReadableStream(a))return a;if(T.isArrayBufferView(a))return a.buffer;if(T.isURLSearchParams(a))return b.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();if(f){if(d.indexOf("application/x-www-form-urlencoded")>-1){var g,h;return(g=a,h=this.formSerializer,aa(g,new at.classes.URLSearchParams,Object.assign({visitor:function(a,b,c,d){return at.isNode&&T.isBuffer(a)?(this.append(b,a.toString("base64")),!1):d.defaultVisitor.apply(this,arguments)}},h))).toString()}if((c=T.isFileList(a))||d.indexOf("multipart/form-data")>-1){let b=this.env&&this.env.FormData;return aa(c?{"files[]":a}:a,b&&new b,this.formSerializer)}}if(f||e){b.setContentType("application/json",!1);var i=a;if(T.isString(i))try{return(0,JSON.parse)(i),T.trim(i)}catch(a){if("SyntaxError"!==a.name)throw a}return(0,JSON.stringify)(i)}return a}],transformResponse:[function(a){let b=this.transitional||av.transitional,c=b&&b.forcedJSONParsing,d="json"===this.responseType;if(T.isResponse(a)||T.isReadableStream(a))return a;if(a&&T.isString(a)&&(c&&!this.responseType||d)){let c=b&&b.silentJSONParsing;try{return JSON.parse(a)}catch(a){if(!c&&d){if("SyntaxError"===a.name)throw U.from(a,U.ERR_BAD_RESPONSE,this,null,this.response);throw a}}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:at.classes.FormData,Blob:at.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};T.forEach(["delete","get","head","post","put","patch"],a=>{av.headers[a]={}});let aw=T.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ax=Symbol("internals");function ay(a){return a&&String(a).trim().toLowerCase()}function az(a){return!1===a||null==a?a:T.isArray(a)?a.map(az):String(a)}function aA(a,b,c,d,e){if(T.isFunction(d))return d.call(this,b,c);if(e&&(b=c),T.isString(b)){if(T.isString(d))return -1!==b.indexOf(d);if(T.isRegExp(d))return d.test(b)}}class aB{constructor(a){a&&this.set(a)}set(a,b,c){let d=this;function e(a,b,c){let e=ay(b);if(!e)throw Error("header name must be a non-empty string");let f=T.findKey(d,e);f&&void 0!==d[f]&&!0!==c&&(void 0!==c||!1===d[f])||(d[f||b]=az(a))}let f=(a,b)=>T.forEach(a,(a,c)=>e(a,c,b));if(T.isPlainObject(a)||a instanceof this.constructor)f(a,b);else{let d;if(T.isString(a)&&(a=a.trim())&&(d=a,!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(d.trim())))f((a=>{let b,c,d,e={};return a&&a.split("\n").forEach(function(a){d=a.indexOf(":"),b=a.substring(0,d).trim().toLowerCase(),c=a.substring(d+1).trim(),!b||e[b]&&aw[b]||("set-cookie"===b?e[b]?e[b].push(c):e[b]=[c]:e[b]=e[b]?e[b]+", "+c:c)}),e})(a),b);else if(T.isObject(a)&&T.isIterable(a)){let c={},d,e;for(let b of a){if(!T.isArray(b))throw TypeError("Object iterator must return a key-value pair");c[e=b[0]]=(d=c[e])?T.isArray(d)?[...d,b[1]]:[d,b[1]]:b[1]}f(c,b)}else null!=a&&e(b,a,c)}return this}get(a,b){if(a=ay(a)){let c=T.findKey(this,a);if(c){let a=this[c];if(!b)return a;if(!0===b){let b,c=Object.create(null),d=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;b=d.exec(a);)c[b[1]]=b[2];return c}if(T.isFunction(b))return b.call(this,a,c);if(T.isRegExp(b))return b.exec(a);throw TypeError("parser must be boolean|regexp|function")}}}has(a,b){if(a=ay(a)){let c=T.findKey(this,a);return!!(c&&void 0!==this[c]&&(!b||aA(this,this[c],c,b)))}return!1}delete(a,b){let c=this,d=!1;function e(a){if(a=ay(a)){let e=T.findKey(c,a);e&&(!b||aA(c,c[e],e,b))&&(delete c[e],d=!0)}}return T.isArray(a)?a.forEach(e):e(a),d}clear(a){let b=Object.keys(this),c=b.length,d=!1;for(;c--;){let e=b[c];(!a||aA(this,this[e],e,a,!0))&&(delete this[e],d=!0)}return d}normalize(a){let b=this,c={};return T.forEach(this,(d,e)=>{let f=T.findKey(c,e);if(f){b[f]=az(d),delete b[e];return}let g=a?e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,b,c)=>b.toUpperCase()+c):String(e).trim();g!==e&&delete b[e],b[g]=az(d),c[g]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){let b=Object.create(null);return T.forEach(this,(c,d)=>{null!=c&&!1!==c&&(b[d]=a&&T.isArray(c)?c.join(", "):c)}),b}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,b])=>a+": "+b).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...b){let c=new this(a);return b.forEach(a=>c.set(a)),c}static accessor(a){let b=(this[ax]=this[ax]={accessors:{}}).accessors,c=this.prototype;function d(a){let d=ay(a);if(!b[d]){let e=T.toCamelCase(" "+a);["get","set","has"].forEach(b=>{Object.defineProperty(c,b+e,{value:function(c,d,e){return this[b].call(this,a,c,d,e)},configurable:!0})}),b[d]=!0}}return T.isArray(a)?a.forEach(d):d(a),this}}function aC(a,b){let c=this||av,d=b||c,e=aB.from(d.headers),f=d.data;return T.forEach(a,function(a){f=a.call(c,f,e.normalize(),b?b.status:void 0)}),e.normalize(),f}function aD(a){return!!(a&&a.__CANCEL__)}function aE(a,b,c){U.call(this,null==a?"canceled":a,U.ERR_CANCELED,b,c),this.name="CanceledError"}function aF(a,b,c){let d=c.config.validateStatus;!c.status||!d||d(c.status)?a(c):b(new U("Request failed with status code "+c.status,[U.ERR_BAD_REQUEST,U.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function aG(a,b,c){let d=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(b);return a&&(d||!1==c)?b?a.replace(/\/?\/$/,"")+"/"+b.replace(/^\/+/,""):a:b}aB.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),T.reduceDescriptors(aB.prototype,({value:a},b)=>{let c=b[0].toUpperCase()+b.slice(1);return{get:()=>a,set(a){this[c]=a}}}),T.freezeMethods(aB),T.inherits(aE,U,{__CANCEL__:!0});var aH=c(87736),aI=c(81630),aJ=c(55591),aK=c(28354),aL=c(23812),aM=c(74075);let aN="1.10.0";function aO(a){let b=/^([-+\w]{1,25})(:?\/\/|:)/.exec(a);return b&&b[1]||""}let aP=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var aQ=c(27910);let aR=Symbol("internals");class aS extends aQ.Transform{constructor(a){super({readableHighWaterMark:(a=T.toFlatObject(a,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(a,b)=>!T.isUndefined(b[a]))).chunkSize});let b=this[aR]={timeWindow:a.timeWindow,chunkSize:a.chunkSize,maxRate:a.maxRate,minChunkSize:a.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",a=>{"progress"!==a||b.isCaptured||(b.isCaptured=!0)})}_read(a){let b=this[aR];return b.onReadCallback&&b.onReadCallback(),super._read(a)}_transform(a,b,c){let d=this[aR],e=d.maxRate,f=this.readableHighWaterMark,g=d.timeWindow,h=e/(1e3/g),i=!1!==d.minChunkSize?Math.max(d.minChunkSize,.01*h):0,j=(a,b)=>{let c=Buffer.byteLength(a);d.bytesSeen+=c,d.bytes+=c,d.isCaptured&&this.emit("progress",d.bytesSeen),this.push(a)?process.nextTick(b):d.onReadCallback=()=>{d.onReadCallback=null,process.nextTick(b)}},k=(a,b)=>{let c,k=Buffer.byteLength(a),l=null,m=f,n=0;if(e){let a=Date.now();(!d.ts||(n=a-d.ts)>=g)&&(d.ts=a,c=h-d.bytes,d.bytes=c<0?-c:0,n=0),c=h-d.bytes}if(e){if(c<=0)return setTimeout(()=>{b(null,a)},g-n);c<m&&(m=c)}m&&k>m&&k-m>i&&(l=a.subarray(m),a=a.subarray(0,m)),j(a,l?()=>{process.nextTick(b,null,l)}:b)};k(a,function a(b,d){if(b)return c(b);d?k(d,a):c(null)})}}var aT=c(94735);let{asyncIterator:aU}=Symbol,aV=async function*(a){a.stream?yield*a.stream():a.arrayBuffer?yield await a.arrayBuffer():a[aU]?yield*a[aU]():yield a},aW=at.ALPHABET.ALPHA_DIGIT+"-_",aX="function"==typeof TextEncoder?new TextEncoder:new aK.TextEncoder,aY=aX.encode("\r\n");class aZ{constructor(a,b){let{escapeName:c}=this.constructor,d=T.isString(b),e=`Content-Disposition: form-data; name="${c(a)}"${!d&&b.name?`; filename="${c(b.name)}"`:""}\r
`;d?b=aX.encode(String(b).replace(/\r?\n|\r\n?/g,"\r\n")):e+=`Content-Type: ${b.type||"application/octet-stream"}\r
`,this.headers=aX.encode(e+"\r\n"),this.contentLength=d?b.byteLength:b.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=a,this.value=b}async *encode(){yield this.headers;let{value:a}=this;T.isTypedArray(a)?yield a:yield*aV(a),yield aY}static escapeName(a){return String(a).replace(/[\r\n"]/g,a=>({"\r":"%0D","\n":"%0A",'"':"%22"})[a])}}class a$ extends aQ.Transform{__transform(a,b,c){this.push(a),c()}_transform(a,b,c){if(0!==a.length&&(this._transform=this.__transform,120!==a[0])){let a=Buffer.alloc(2);a[0]=120,a[1]=156,this.push(a,b)}this.__transform(a,b,c)}}let a_=function(a,b){let c,d=Array(a=a||10),e=Array(a),f=0,g=0;return b=void 0!==b?b:1e3,function(h){let i=Date.now(),j=e[g];c||(c=i),d[f]=h,e[f]=i;let k=g,l=0;for(;k!==f;)l+=d[k++],k%=a;if((f=(f+1)%a)===g&&(g=(g+1)%a),i-c<b)return;let m=j&&i-j;return m?Math.round(1e3*l/m):void 0}},a0=function(a,b){let c,d,e=0,f=1e3/b,g=(b,f=Date.now())=>{e=f,c=null,d&&(clearTimeout(d),d=null),a.apply(null,b)};return[(...a)=>{let b=Date.now(),h=b-e;h>=f?g(a,b):(c=a,d||(d=setTimeout(()=>{d=null,g(c)},f-h)))},()=>c&&g(c)]},a1=(a,b,c=3)=>{let d=0,e=a_(50,250);return a0(c=>{let f=c.loaded,g=c.lengthComputable?c.total:void 0,h=f-d,i=e(h);d=f,a({loaded:f,total:g,progress:g?f/g:void 0,bytes:h,rate:i||void 0,estimated:i&&g&&f<=g?(g-f)/i:void 0,event:c,lengthComputable:null!=g,[b?"download":"upload"]:!0})},c)},a2=(a,b)=>{let c=null!=a;return[d=>b[0]({lengthComputable:c,total:a,loaded:d}),b[1]]},a3=a=>(...b)=>T.asap(()=>a(...b)),a4={flush:aM.constants.Z_SYNC_FLUSH,finishFlush:aM.constants.Z_SYNC_FLUSH},a5={flush:aM.constants.BROTLI_OPERATION_FLUSH,finishFlush:aM.constants.BROTLI_OPERATION_FLUSH},a6=T.isFunction(aM.createBrotliDecompress),{http:a7,https:a8}=aL,a9=/https:?/,ba=at.protocols.map(a=>a+":"),bb=(a,[b,c])=>(a.on("end",c).on("error",c),b);function bc(a,b){a.beforeRedirects.proxy&&a.beforeRedirects.proxy(a),a.beforeRedirects.config&&a.beforeRedirects.config(a,b)}let bd="undefined"!=typeof process&&"process"===T.kindOf(process),be=(a,b)=>(({address:a,family:b})=>{if(!T.isString(a))throw TypeError("address must be a string");return{address:a,family:b||(0>a.indexOf(".")?6:4)}})(T.isObject(a)?a:{address:a,family:b}),bf=bd&&function(a){let b;return b=async function(b,c,d){let e,f,g,h,i,j,k,{data:l,lookup:m,family:n}=a,{responseType:o,responseEncoding:p}=a,q=a.method.toUpperCase(),r=!1;if(m){let a,b,c=(a=m,b=a=>T.isArray(a)?a:[a],T.isAsyncFn(a)?function(...c){let d=c.pop();a.apply(this,c).then(a=>{try{b?d(null,...b(a)):d(null,a)}catch(a){d(a)}},d)}:a);m=(a,b,d)=>{c(a,b,(a,c,e)=>{if(a)return d(a);let f=T.isArray(c)?c.map(a=>be(a)):[be(c,e)];b.all?d(a,f):d(a,f[0].address,f[0].family)})}}let s=new aT.EventEmitter,t=()=>{a.cancelToken&&a.cancelToken.unsubscribe(u),a.signal&&a.signal.removeEventListener("abort",u),s.removeAllListeners()};function u(b){s.emit("abort",!b||b.type?new aE(null,a,i):b)}d((a,b)=>{h=!0,b&&(r=!0,t())}),s.once("abort",c),(a.cancelToken||a.signal)&&(a.cancelToken&&a.cancelToken.subscribe(u),a.signal&&(a.signal.aborted?u():a.signal.addEventListener("abort",u)));let v=new URL(aG(a.baseURL,a.url,a.allowAbsoluteUrls),at.hasBrowserEnv?at.origin:void 0),w=v.protocol||ba[0];if("data:"===w){let d;if("GET"!==q)return aF(b,c,{status:405,statusText:"method not allowed",headers:{},config:a});try{d=function(a,b,c){let d=c&&c.Blob||at.classes.Blob,e=aO(a);if(void 0===b&&d&&(b=!0),"data"===e){a=e.length?a.slice(e.length+1):a;let c=aP.exec(a);if(!c)throw new U("Invalid URL",U.ERR_INVALID_URL);let f=c[1],g=c[2],h=c[3],i=Buffer.from(decodeURIComponent(h),g?"base64":"utf8");if(b){if(!d)throw new U("Blob is not supported",U.ERR_NOT_SUPPORT);return new d([i],{type:f})}return i}throw new U("Unsupported protocol "+e,U.ERR_NOT_SUPPORT)}(a.url,"blob"===o,{Blob:a.env&&a.env.Blob})}catch(b){throw U.from(b,U.ERR_BAD_REQUEST,a)}return"text"===o?(d=d.toString(p),p&&"utf8"!==p||(d=T.stripBOM(d))):"stream"===o&&(d=aQ.Readable.from(d)),aF(b,c,{data:d,status:200,statusText:"OK",headers:new aB,config:a})}if(-1===ba.indexOf(w))return c(new U("Unsupported protocol "+w,U.ERR_BAD_REQUEST,a));let x=aB.from(a.headers).normalize();x.set("User-Agent","axios/"+aN,!1);let{onUploadProgress:y,onDownloadProgress:z}=a,A=a.maxRate;if(T.isSpecCompliantForm(l)){let a=x.getContentType(/boundary=([-_\w\d]{10,70})/i);l=((a,b,c)=>{let{tag:d="form-data-boundary",size:e=25,boundary:f=d+"-"+at.generateString(e,aW)}=c||{};if(!T.isFormData(a))throw TypeError("FormData instance required");if(f.length<1||f.length>70)throw Error("boundary must be 10-70 characters long");let g=aX.encode("--"+f+"\r\n"),h=aX.encode("--"+f+"--\r\n"),i=h.byteLength,j=Array.from(a.entries()).map(([a,b])=>{let c=new aZ(a,b);return i+=c.size,c});i+=g.byteLength*j.length;let k={"Content-Type":`multipart/form-data; boundary=${f}`};return Number.isFinite(i=T.toFiniteNumber(i))&&(k["Content-Length"]=i),b&&b(k),aQ.Readable.from(async function*(){for(let a of j)yield g,yield*a.encode();yield h}())})(l,a=>{x.set(a)},{tag:`axios-${aN}-boundary`,boundary:a&&a[1]||void 0})}else if(T.isFormData(l)&&T.isFunction(l.getHeaders)){if(x.set(l.getHeaders()),!x.hasContentLength())try{let a=await aK.promisify(l.getLength).call(l);Number.isFinite(a)&&a>=0&&x.setContentLength(a)}catch(a){}}else if(T.isBlob(l)||T.isFile(l))l.size&&x.setContentType(l.type||"application/octet-stream"),x.setContentLength(l.size||0),l=aQ.Readable.from(aV(l));else if(l&&!T.isStream(l)){if(Buffer.isBuffer(l));else if(T.isArrayBuffer(l))l=Buffer.from(new Uint8Array(l));else{if(!T.isString(l))return c(new U("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",U.ERR_BAD_REQUEST,a));l=Buffer.from(l,"utf-8")}if(x.setContentLength(l.length,!1),a.maxBodyLength>-1&&l.length>a.maxBodyLength)return c(new U("Request body larger than maxBodyLength limit",U.ERR_BAD_REQUEST,a))}let B=T.toFiniteNumber(x.getContentLength());T.isArray(A)?(e=A[0],f=A[1]):e=f=A,l&&(y||e)&&(T.isStream(l)||(l=aQ.Readable.from(l,{objectMode:!1})),l=aQ.pipeline([l,new aS({maxRate:T.toFiniteNumber(e)})],T.noop),y&&l.on("progress",bb(l,a2(B,a1(a3(y),!1,3))))),a.auth&&(g=(a.auth.username||"")+":"+(a.auth.password||"")),!g&&v.username&&(g=v.username+":"+v.password),g&&x.delete("authorization");try{j=af(v.pathname+v.search,a.params,a.paramsSerializer).replace(/^\?/,"")}catch(d){let b=Error(d.message);return b.config=a,b.url=a.url,b.exists=!0,c(b)}x.set("Accept-Encoding","gzip, compress, deflate"+(a6?", br":""),!1);let C={path:j,method:q,headers:x.toJSON(),agents:{http:a.httpAgent,https:a.httpsAgent},auth:g,protocol:w,family:n,beforeRedirect:bc,beforeRedirects:{}};T.isUndefined(m)||(C.lookup=m),a.socketPath?C.socketPath=a.socketPath:(C.hostname=v.hostname.startsWith("[")?v.hostname.slice(1,-1):v.hostname,C.port=v.port,function a(b,c,d){let e=c;if(!e&&!1!==e){let a=aH.getProxyForUrl(d);a&&(e=new URL(a))}if(e){if(e.username&&(e.auth=(e.username||"")+":"+(e.password||"")),e.auth){(e.auth.username||e.auth.password)&&(e.auth=(e.auth.username||"")+":"+(e.auth.password||""));let a=Buffer.from(e.auth,"utf8").toString("base64");b.headers["Proxy-Authorization"]="Basic "+a}b.headers.host=b.hostname+(b.port?":"+b.port:"");let a=e.hostname||e.host;b.hostname=a,b.host=a,b.port=e.port,b.path=d,e.protocol&&(b.protocol=e.protocol.includes(":")?e.protocol:`${e.protocol}:`)}b.beforeRedirects.proxy=function(b){a(b,c,b.href)}}(C,a.proxy,w+"//"+v.hostname+(v.port?":"+v.port:"")+C.path));let D=a9.test(C.protocol);if(C.agent=D?a.httpsAgent:a.httpAgent,a.transport?k=a.transport:0===a.maxRedirects?k=D?aJ:aI:(a.maxRedirects&&(C.maxRedirects=a.maxRedirects),a.beforeRedirect&&(C.beforeRedirects.config=a.beforeRedirect),k=D?a8:a7),a.maxBodyLength>-1?C.maxBodyLength=a.maxBodyLength:C.maxBodyLength=1/0,a.insecureHTTPParser&&(C.insecureHTTPParser=a.insecureHTTPParser),i=k.request(C,function(d){if(i.destroyed)return;let e=[d],g=+d.headers["content-length"];if(z||f){let a=new aS({maxRate:T.toFiniteNumber(f)});z&&a.on("progress",bb(a,a2(g,a1(a3(z),!0,3)))),e.push(a)}let h=d,j=d.req||i;if(!1!==a.decompress&&d.headers["content-encoding"])switch(("HEAD"===q||204===d.statusCode)&&delete d.headers["content-encoding"],(d.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":e.push(aM.createUnzip(a4)),delete d.headers["content-encoding"];break;case"deflate":e.push(new a$),e.push(aM.createUnzip(a4)),delete d.headers["content-encoding"];break;case"br":a6&&(e.push(aM.createBrotliDecompress(a5)),delete d.headers["content-encoding"])}h=e.length>1?aQ.pipeline(e,T.noop):e[0];let k=aQ.finished(h,()=>{k(),t()}),l={status:d.statusCode,statusText:d.statusMessage,headers:new aB(d.headers),config:a,request:j};if("stream"===o)l.data=h,aF(b,c,l);else{let d=[],e=0;h.on("data",function(b){d.push(b),e+=b.length,a.maxContentLength>-1&&e>a.maxContentLength&&(r=!0,h.destroy(),c(new U("maxContentLength size of "+a.maxContentLength+" exceeded",U.ERR_BAD_RESPONSE,a,j)))}),h.on("aborted",function(){if(r)return;let b=new U("stream has been aborted",U.ERR_BAD_RESPONSE,a,j);h.destroy(b),c(b)}),h.on("error",function(b){i.destroyed||c(U.from(b,null,a,j))}),h.on("end",function(){try{let a=1===d.length?d[0]:Buffer.concat(d);"arraybuffer"!==o&&(a=a.toString(p),p&&"utf8"!==p||(a=T.stripBOM(a))),l.data=a}catch(b){return c(U.from(b,null,a,l.request,l))}aF(b,c,l)})}s.once("abort",a=>{h.destroyed||(h.emit("error",a),h.destroy())})}),s.once("abort",a=>{c(a),i.destroy(a)}),i.on("error",function(b){c(U.from(b,null,a,i))}),i.on("socket",function(a){a.setKeepAlive(!0,6e4)}),a.timeout){let b=parseInt(a.timeout,10);if(Number.isNaN(b))return void c(new U("error trying to parse `config.timeout` to int",U.ERR_BAD_OPTION_VALUE,a,i));i.setTimeout(b,function(){if(h)return;let b=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded",d=a.transitional||ah;a.timeoutErrorMessage&&(b=a.timeoutErrorMessage),c(new U(b,d.clarifyTimeoutError?U.ETIMEDOUT:U.ECONNABORTED,a,i)),u()})}if(T.isStream(l)){let b=!1,c=!1;l.on("end",()=>{b=!0}),l.once("error",a=>{c=!0,i.destroy(a)}),l.on("close",()=>{b||c||u(new aE("Request stream has been aborted",a,i))}),l.pipe(i)}else i.end(l)},new Promise((a,c)=>{let d,e,f=(a,b)=>{!e&&(e=!0,d&&d(a,b))},g=a=>{f(a,!0),c(a)};b(b=>{f(b),a(b)},g,a=>d=a).catch(g)})},bg=at.hasStandardBrowserEnv?((a,b)=>c=>(c=new URL(c,at.origin),a.protocol===c.protocol&&a.host===c.host&&(b||a.port===c.port)))(new URL(at.origin),at.navigator&&/(msie|trident)/i.test(at.navigator.userAgent)):()=>!0,bh=at.hasStandardBrowserEnv?{write(a,b,c,d,e,f){let g=[a+"="+encodeURIComponent(b)];T.isNumber(c)&&g.push("expires="+new Date(c).toGMTString()),T.isString(d)&&g.push("path="+d),T.isString(e)&&g.push("domain="+e),!0===f&&g.push("secure"),document.cookie=g.join("; ")},read(a){let b=document.cookie.match(RegExp("(^|;\\s*)("+a+")=([^;]*)"));return b?decodeURIComponent(b[3]):null},remove(a){this.write(a,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},bi=a=>a instanceof aB?{...a}:a;function bj(a,b){b=b||{};let c={};function d(a,b,c,d){return T.isPlainObject(a)&&T.isPlainObject(b)?T.merge.call({caseless:d},a,b):T.isPlainObject(b)?T.merge({},b):T.isArray(b)?b.slice():b}function e(a,b,c,e){return T.isUndefined(b)?T.isUndefined(a)?void 0:d(void 0,a,c,e):d(a,b,c,e)}function f(a,b){if(!T.isUndefined(b))return d(void 0,b)}function g(a,b){return T.isUndefined(b)?T.isUndefined(a)?void 0:d(void 0,a):d(void 0,b)}function h(c,e,f){return f in b?d(c,e):f in a?d(void 0,c):void 0}let i={url:f,method:f,data:f,baseURL:g,transformRequest:g,transformResponse:g,paramsSerializer:g,timeout:g,timeoutMessage:g,withCredentials:g,withXSRFToken:g,adapter:g,responseType:g,xsrfCookieName:g,xsrfHeaderName:g,onUploadProgress:g,onDownloadProgress:g,decompress:g,maxContentLength:g,maxBodyLength:g,beforeRedirect:g,transport:g,httpAgent:g,httpsAgent:g,cancelToken:g,socketPath:g,responseEncoding:g,validateStatus:h,headers:(a,b,c)=>e(bi(a),bi(b),c,!0)};return T.forEach(Object.keys(Object.assign({},a,b)),function(d){let f=i[d]||e,g=f(a[d],b[d],d);T.isUndefined(g)&&f!==h||(c[d]=g)}),c}let bk=a=>{let b,c=bj({},a),{data:d,withXSRFToken:e,xsrfHeaderName:f,xsrfCookieName:g,headers:h,auth:i}=c;if(c.headers=h=aB.from(h),c.url=af(aG(c.baseURL,c.url,c.allowAbsoluteUrls),a.params,a.paramsSerializer),i&&h.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):""))),T.isFormData(d)){if(at.hasStandardBrowserEnv||at.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if(!1!==(b=h.getContentType())){let[a,...c]=b?b.split(";").map(a=>a.trim()).filter(Boolean):[];h.setContentType([a||"multipart/form-data",...c].join("; "))}}if(at.hasStandardBrowserEnv&&(e&&T.isFunction(e)&&(e=e(c)),e||!1!==e&&bg(c.url))){let a=f&&g&&bh.read(g);a&&h.set(f,a)}return c},bl="undefined"!=typeof XMLHttpRequest&&function(a){return new Promise(function(b,c){let d,e,f,g,h,i=bk(a),j=i.data,k=aB.from(i.headers).normalize(),{responseType:l,onUploadProgress:m,onDownloadProgress:n}=i;function o(){g&&g(),h&&h(),i.cancelToken&&i.cancelToken.unsubscribe(d),i.signal&&i.signal.removeEventListener("abort",d)}let p=new XMLHttpRequest;function q(){if(!p)return;let d=aB.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders());aF(function(a){b(a),o()},function(a){c(a),o()},{data:l&&"text"!==l&&"json"!==l?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:d,config:a,request:p}),p=null}p.open(i.method.toUpperCase(),i.url,!0),p.timeout=i.timeout,"onloadend"in p?p.onloadend=q:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(q)},p.onabort=function(){p&&(c(new U("Request aborted",U.ECONNABORTED,a,p)),p=null)},p.onerror=function(){c(new U("Network Error",U.ERR_NETWORK,a,p)),p=null},p.ontimeout=function(){let b=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded",d=i.transitional||ah;i.timeoutErrorMessage&&(b=i.timeoutErrorMessage),c(new U(b,d.clarifyTimeoutError?U.ETIMEDOUT:U.ECONNABORTED,a,p)),p=null},void 0===j&&k.setContentType(null),"setRequestHeader"in p&&T.forEach(k.toJSON(),function(a,b){p.setRequestHeader(b,a)}),T.isUndefined(i.withCredentials)||(p.withCredentials=!!i.withCredentials),l&&"json"!==l&&(p.responseType=i.responseType),n&&([f,h]=a1(n,!0),p.addEventListener("progress",f)),m&&p.upload&&([e,g]=a1(m),p.upload.addEventListener("progress",e),p.upload.addEventListener("loadend",g)),(i.cancelToken||i.signal)&&(d=b=>{p&&(c(!b||b.type?new aE(null,a,p):b),p.abort(),p=null)},i.cancelToken&&i.cancelToken.subscribe(d),i.signal&&(i.signal.aborted?d():i.signal.addEventListener("abort",d)));let r=aO(i.url);if(r&&-1===at.protocols.indexOf(r))return void c(new U("Unsupported protocol "+r+":",U.ERR_BAD_REQUEST,a));p.send(j||null)})},bm=function*(a,b){let c,d=a.byteLength;if(!b||d<b)return void(yield a);let e=0;for(;e<d;)c=e+b,yield a.slice(e,c),e=c},bn=async function*(a,b){for await(let c of bo(a))yield*bm(c,b)},bo=async function*(a){if(a[Symbol.asyncIterator])return void(yield*a);let b=a.getReader();try{for(;;){let{done:a,value:c}=await b.read();if(a)break;yield c}}finally{await b.cancel()}},bp=(a,b,c,d)=>{let e,f=bn(a,b),g=0,h=a=>{!e&&(e=!0,d&&d(a))};return new ReadableStream({async pull(a){try{let{done:b,value:d}=await f.next();if(b){h(),a.close();return}let e=d.byteLength;if(c){let a=g+=e;c(a)}a.enqueue(new Uint8Array(d))}catch(a){throw h(a),a}},cancel:a=>(h(a),f.return())},{highWaterMark:2})},bq="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,br=bq&&"function"==typeof ReadableStream,bs=bq&&("function"==typeof TextEncoder?(d=new TextEncoder,a=>d.encode(a)):async a=>new Uint8Array(await new Response(a).arrayBuffer())),bt=(a,...b)=>{try{return!!a(...b)}catch(a){return!1}},bu=br&&bt(()=>{let a=!1,b=new Request(at.origin,{body:new ReadableStream,method:"POST",get duplex(){return a=!0,"half"}}).headers.has("Content-Type");return a&&!b}),bv=br&&bt(()=>T.isReadableStream(new Response("").body)),bw={stream:bv&&(a=>a.body)};bq&&(g=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(a=>{bw[a]||(bw[a]=T.isFunction(g[a])?b=>b[a]():(b,c)=>{throw new U(`Response type '${a}' is not supported`,U.ERR_NOT_SUPPORT,c)})}));let bx=async a=>{if(null==a)return 0;if(T.isBlob(a))return a.size;if(T.isSpecCompliantForm(a)){let b=new Request(at.origin,{method:"POST",body:a});return(await b.arrayBuffer()).byteLength}return T.isArrayBufferView(a)||T.isArrayBuffer(a)?a.byteLength:(T.isURLSearchParams(a)&&(a+=""),T.isString(a))?(await bs(a)).byteLength:void 0},by=async(a,b)=>{let c=T.toFiniteNumber(a.getContentLength());return null==c?bx(b):c},bz={http:bf,xhr:bl,fetch:bq&&(async a=>{let b,c,{url:d,method:e,data:f,signal:g,cancelToken:h,timeout:i,onDownloadProgress:j,onUploadProgress:k,responseType:l,headers:m,withCredentials:n="same-origin",fetchOptions:o}=bk(a);l=l?(l+"").toLowerCase():"text";let p=((a,b)=>{let{length:c}=a=a?a.filter(Boolean):[];if(b||c){let c,d=new AbortController,e=function(a){if(!c){c=!0,g();let b=a instanceof Error?a:this.reason;d.abort(b instanceof U?b:new aE(b instanceof Error?b.message:b))}},f=b&&setTimeout(()=>{f=null,e(new U(`timeout ${b} of ms exceeded`,U.ETIMEDOUT))},b),g=()=>{a&&(f&&clearTimeout(f),f=null,a.forEach(a=>{a.unsubscribe?a.unsubscribe(e):a.removeEventListener("abort",e)}),a=null)};a.forEach(a=>a.addEventListener("abort",e));let{signal:h}=d;return h.unsubscribe=()=>T.asap(g),h}})([g,h&&h.toAbortSignal()],i),q=p&&p.unsubscribe&&(()=>{p.unsubscribe()});try{if(k&&bu&&"get"!==e&&"head"!==e&&0!==(c=await by(m,f))){let a,b=new Request(d,{method:"POST",body:f,duplex:"half"});if(T.isFormData(f)&&(a=b.headers.get("content-type"))&&m.setContentType(a),b.body){let[a,d]=a2(c,a1(a3(k)));f=bp(b.body,65536,a,d)}}T.isString(n)||(n=n?"include":"omit");let g="credentials"in Request.prototype;b=new Request(d,{...o,signal:p,method:e.toUpperCase(),headers:m.normalize().toJSON(),body:f,duplex:"half",credentials:g?n:void 0});let h=await fetch(b,o),i=bv&&("stream"===l||"response"===l);if(bv&&(j||i&&q)){let a={};["status","statusText","headers"].forEach(b=>{a[b]=h[b]});let b=T.toFiniteNumber(h.headers.get("content-length")),[c,d]=j&&a2(b,a1(a3(j),!0))||[];h=new Response(bp(h.body,65536,c,()=>{d&&d(),q&&q()}),a)}l=l||"text";let r=await bw[T.findKey(bw,l)||"text"](h,a);return!i&&q&&q(),await new Promise((c,d)=>{aF(c,d,{data:r,headers:aB.from(h.headers),status:h.status,statusText:h.statusText,config:a,request:b})})}catch(c){if(q&&q(),c&&"TypeError"===c.name&&/Load failed|fetch/i.test(c.message))throw Object.assign(new U("Network Error",U.ERR_NETWORK,a,b),{cause:c.cause||c});throw U.from(c,c&&c.code,a,b)}})};T.forEach(bz,(a,b)=>{if(a){try{Object.defineProperty(a,"name",{value:b})}catch(a){}Object.defineProperty(a,"adapterName",{value:b})}});let bA=a=>`- ${a}`,bB=a=>T.isFunction(a)||null===a||!1===a,bC={getAdapter:a=>{let b,c,{length:d}=a=T.isArray(a)?a:[a],e={};for(let f=0;f<d;f++){let d;if(c=b=a[f],!bB(b)&&void 0===(c=bz[(d=String(b)).toLowerCase()]))throw new U(`Unknown adapter '${d}'`);if(c)break;e[d||"#"+f]=c}if(!c){let a=Object.entries(e).map(([a,b])=>`adapter ${a} `+(!1===b?"is not supported by the environment":"is not available in the build"));throw new U("There is no suitable adapter to dispatch the request "+(d?a.length>1?"since :\n"+a.map(bA).join("\n"):" "+bA(a[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return c}};function bD(a){if(a.cancelToken&&a.cancelToken.throwIfRequested(),a.signal&&a.signal.aborted)throw new aE(null,a)}function bE(a){return bD(a),a.headers=aB.from(a.headers),a.data=aC.call(a,a.transformRequest),-1!==["post","put","patch"].indexOf(a.method)&&a.headers.setContentType("application/x-www-form-urlencoded",!1),bC.getAdapter(a.adapter||av.adapter)(a).then(function(b){return bD(a),b.data=aC.call(a,a.transformResponse,b),b.headers=aB.from(b.headers),b},function(b){return!aD(b)&&(bD(a),b&&b.response&&(b.response.data=aC.call(a,a.transformResponse,b.response),b.response.headers=aB.from(b.response.headers))),Promise.reject(b)})}let bF={};["object","boolean","number","function","string","symbol"].forEach((a,b)=>{bF[a]=function(c){return typeof c===a||"a"+(b<1?"n ":" ")+a}});let bG={};bF.transitional=function(a,b,c){function d(a,b){return"[Axios v"+aN+"] Transitional option '"+a+"'"+b+(c?". "+c:"")}return(c,e,f)=>{if(!1===a)throw new U(d(e," has been removed"+(b?" in "+b:"")),U.ERR_DEPRECATED);return b&&!bG[e]&&(bG[e]=!0,console.warn(d(e," has been deprecated since v"+b+" and will be removed in the near future"))),!a||a(c,e,f)}},bF.spelling=function(a){return(b,c)=>(console.warn(`${c} is likely a misspelling of ${a}`),!0)};let bH={assertOptions:function(a,b,c){if("object"!=typeof a)throw new U("options must be an object",U.ERR_BAD_OPTION_VALUE);let d=Object.keys(a),e=d.length;for(;e-- >0;){let f=d[e],g=b[f];if(g){let b=a[f],c=void 0===b||g(b,f,a);if(!0!==c)throw new U("option "+f+" must be "+c,U.ERR_BAD_OPTION_VALUE);continue}if(!0!==c)throw new U("Unknown option "+f,U.ERR_BAD_OPTION)}},validators:bF},bI=bH.validators;class bJ{constructor(a){this.defaults=a||{},this.interceptors={request:new ag,response:new ag}}async request(a,b){try{return await this._request(a,b)}catch(a){if(a instanceof Error){let b={};Error.captureStackTrace?Error.captureStackTrace(b):b=Error();let c=b.stack?b.stack.replace(/^.+\n/,""):"";try{a.stack?c&&!String(a.stack).endsWith(c.replace(/^.+\n.+\n/,""))&&(a.stack+="\n"+c):a.stack=c}catch(a){}}throw a}}_request(a,b){let c,d;"string"==typeof a?(b=b||{}).url=a:b=a||{};let{transitional:e,paramsSerializer:f,headers:g}=b=bj(this.defaults,b);void 0!==e&&bH.assertOptions(e,{silentJSONParsing:bI.transitional(bI.boolean),forcedJSONParsing:bI.transitional(bI.boolean),clarifyTimeoutError:bI.transitional(bI.boolean)},!1),null!=f&&(T.isFunction(f)?b.paramsSerializer={serialize:f}:bH.assertOptions(f,{encode:bI.function,serialize:bI.function},!0)),void 0!==b.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?b.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:b.allowAbsoluteUrls=!0),bH.assertOptions(b,{baseUrl:bI.spelling("baseURL"),withXsrfToken:bI.spelling("withXSRFToken")},!0),b.method=(b.method||this.defaults.method||"get").toLowerCase();let h=g&&T.merge(g.common,g[b.method]);g&&T.forEach(["delete","get","head","post","put","patch","common"],a=>{delete g[a]}),b.headers=aB.concat(h,g);let i=[],j=!0;this.interceptors.request.forEach(function(a){("function"!=typeof a.runWhen||!1!==a.runWhen(b))&&(j=j&&a.synchronous,i.unshift(a.fulfilled,a.rejected))});let k=[];this.interceptors.response.forEach(function(a){k.push(a.fulfilled,a.rejected)});let l=0;if(!j){let a=[bE.bind(this),void 0];for(a.unshift.apply(a,i),a.push.apply(a,k),d=a.length,c=Promise.resolve(b);l<d;)c=c.then(a[l++],a[l++]);return c}d=i.length;let m=b;for(l=0;l<d;){let a=i[l++],b=i[l++];try{m=a(m)}catch(a){b.call(this,a);break}}try{c=bE.call(this,m)}catch(a){return Promise.reject(a)}for(l=0,d=k.length;l<d;)c=c.then(k[l++],k[l++]);return c}getUri(a){return af(aG((a=bj(this.defaults,a)).baseURL,a.url,a.allowAbsoluteUrls),a.params,a.paramsSerializer)}}T.forEach(["delete","get","head","options"],function(a){bJ.prototype[a]=function(b,c){return this.request(bj(c||{},{method:a,url:b,data:(c||{}).data}))}}),T.forEach(["post","put","patch"],function(a){function b(b){return function(c,d,e){return this.request(bj(e||{},{method:a,headers:b?{"Content-Type":"multipart/form-data"}:{},url:c,data:d}))}}bJ.prototype[a]=b(),bJ.prototype[a+"Form"]=b(!0)});class bK{constructor(a){let b;if("function"!=typeof a)throw TypeError("executor must be a function.");this.promise=new Promise(function(a){b=a});let c=this;this.promise.then(a=>{if(!c._listeners)return;let b=c._listeners.length;for(;b-- >0;)c._listeners[b](a);c._listeners=null}),this.promise.then=a=>{let b,d=new Promise(a=>{c.subscribe(a),b=a}).then(a);return d.cancel=function(){c.unsubscribe(b)},d},a(function(a,d,e){c.reason||(c.reason=new aE(a,d,e),b(c.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason)return void a(this.reason);this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;let b=this._listeners.indexOf(a);-1!==b&&this._listeners.splice(b,1)}toAbortSignal(){let a=new AbortController,b=b=>{a.abort(b)};return this.subscribe(b),a.signal.unsubscribe=()=>this.unsubscribe(b),a.signal}static source(){let a;return{token:new bK(function(b){a=b}),cancel:a}}}let bL={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(bL).forEach(([a,b])=>{bL[b]=a});let bM=function a(b){let c=new bJ(b),d=i(bJ.prototype.request,c);return T.extend(d,bJ.prototype,c,{allOwnKeys:!0}),T.extend(d,c,null,{allOwnKeys:!0}),d.create=function(c){return a(bj(b,c))},d}(av);bM.Axios=bJ,bM.CanceledError=aE,bM.CancelToken=bK,bM.isCancel=aD,bM.VERSION=aN,bM.toFormData=aa,bM.AxiosError=U,bM.Cancel=bM.CanceledError,bM.all=function(a){return Promise.all(a)},bM.spread=function(a){return function(b){return a.apply(null,b)}},bM.isAxiosError=function(a){return T.isObject(a)&&!0===a.isAxiosError},bM.mergeConfig=bj,bM.AxiosHeaders=aB,bM.formToJSON=a=>au(T.isHTMLForm(a)?new FormData(a):a),bM.getAdapter=bC.getAdapter,bM.HttpStatusCode=bL,bM.default=bM;let bN=bM},14758:a=>{"use strict";a.exports=Math.abs},15217:a=>{function b(a,b,c,d){return Math.round(a/c)+" "+d+(b>=1.5*c?"s":"")}a.exports=function(a,c){c=c||{};var d,e,f,g,h=typeof a;if("string"===h&&a.length>0){var i=a;if(!((i=String(i)).length>100)){var j=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(i);if(j){var k=parseFloat(j[1]);switch((j[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*k;case"weeks":case"week":case"w":return 6048e5*k;case"days":case"day":case"d":return 864e5*k;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*k;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*k;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*k;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return k;default:break}}}return}if("number"===h&&isFinite(a)){return c.long?(e=Math.abs(d=a))>=864e5?b(d,e,864e5,"day"):e>=36e5?b(d,e,36e5,"hour"):e>=6e4?b(d,e,6e4,"minute"):e>=1e3?b(d,e,1e3,"second"):d+" ms":(g=Math.abs(f=a))>=864e5?Math.round(f/864e5)+"d":g>=36e5?Math.round(f/36e5)+"h":g>=6e4?Math.round(f/6e4)+"m":g>=1e3?Math.round(f/1e3)+"s":f+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(a))}},18391:(a,b,c)=>{"use strict";var d=c(4245);if(d)try{d([],"length")}catch(a){d=null}a.exports=d},19777:(a,b,c)=>{b.formatArgs=function(b){if(b[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+b[0]+(this.useColors?"%c ":" ")+"+"+a.exports.humanize(this.diff),!this.useColors)return;let c="color: "+this.color;b.splice(1,0,c,"color: inherit");let d=0,e=0;b[0].replace(/%[a-zA-Z%]/g,a=>{"%%"!==a&&(d++,"%c"===a&&(e=d))}),b.splice(e,0,c)},b.save=function(a){try{a?b.storage.setItem("debug",a):b.storage.removeItem("debug")}catch(a){}},b.load=function(){let a;try{a=b.storage.getItem("debug")}catch(a){}return!a&&"undefined"!=typeof process&&"env"in process&&(a=process.env.DEBUG),a},b.useColors=function(){let a;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(a=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(a[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},b.storage=function(){try{return localStorage}catch(a){}}(),b.destroy=(()=>{let a=!1;return()=>{a||(a=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),b.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],b.log=console.debug||console.log||(()=>{}),a.exports=c(22208)(b);let{formatters:d}=a.exports;d.j=function(a){try{return JSON.stringify(a)}catch(a){return"[UnexpectedJSONParseError]: "+a.message}}},22208:(a,b,c)=>{a.exports=function(a){function b(a){let c,e,f,g=null;function h(...a){if(!h.enabled)return;let d=Number(new Date);h.diff=d-(c||d),h.prev=c,h.curr=d,c=d,a[0]=b.coerce(a[0]),"string"!=typeof a[0]&&a.unshift("%O");let e=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,(c,d)=>{if("%%"===c)return"%";e++;let f=b.formatters[d];if("function"==typeof f){let b=a[e];c=f.call(h,b),a.splice(e,1),e--}return c}),b.formatArgs.call(h,a),(h.log||b.log).apply(h,a)}return h.namespace=a,h.useColors=b.useColors(),h.color=b.selectColor(a),h.extend=d,h.destroy=b.destroy,Object.defineProperty(h,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==g?g:(e!==b.namespaces&&(e=b.namespaces,f=b.enabled(a)),f),set:a=>{g=a}}),"function"==typeof b.init&&b.init(h),h}function d(a,c){let d=b(this.namespace+(void 0===c?":":c)+a);return d.log=this.log,d}function e(a){return a.toString().substring(2,a.toString().length-2).replace(/\.\*\?$/,"*")}return b.debug=b,b.default=b,b.coerce=function(a){return a instanceof Error?a.stack||a.message:a},b.disable=function(){let a=[...b.names.map(e),...b.skips.map(e).map(a=>"-"+a)].join(",");return b.enable(""),a},b.enable=function(a){let c;b.save(a),b.namespaces=a,b.names=[],b.skips=[];let d=("string"==typeof a?a:"").split(/[\s,]+/),e=d.length;for(c=0;c<e;c++)d[c]&&("-"===(a=d[c].replace(/\*/g,".*?"))[0]?b.skips.push(RegExp("^"+a.slice(1)+"$")):b.names.push(RegExp("^"+a+"$")))},b.enabled=function(a){let c,d;if("*"===a[a.length-1])return!0;for(c=0,d=b.skips.length;c<d;c++)if(b.skips[c].test(a))return!1;for(c=0,d=b.names.length;c<d;c++)if(b.names[c].test(a))return!0;return!1},b.humanize=c(15217),b.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(a).forEach(c=>{b[c]=a[c]}),b.names=[],b.skips=[],b.formatters={},b.selectColor=function(a){let c=0;for(let b=0;b<a.length;b++)c=(c<<5)-c+a.charCodeAt(b)|0;return b.colors[Math.abs(c)%b.colors.length]},b.enable(b.load()),b}},22862:a=>{"use strict";a.exports=function(a,b){return Object.keys(b).forEach(function(c){a[c]=a[c]||b[c]}),a}},23812:(a,b,c)=>{var d=c(79551),e=d.URL,f=c(81630),g=c(55591),h=c(27910).Writable,i=c(12412),j=c(56807);!function(){var a="undefined"!=typeof process,b="undefined"!=typeof window&&"undefined"!=typeof document,c=E(Error.captureStackTrace);a||!b&&c||console.warn("The follow-redirects package should be excluded from browser builds.")}();var k=!1;try{i(new e(""))}catch(a){k="ERR_INVALID_URL"===a.code}var l=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],m=["abort","aborted","connect","error","socket","timeout"],n=Object.create(null);m.forEach(function(a){n[a]=function(b,c,d){this._redirectable.emit(a,b,c,d)}});var o=B("ERR_INVALID_URL","Invalid URL",TypeError),p=B("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),q=B("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",p),r=B("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),s=B("ERR_STREAM_WRITE_AFTER_END","write after end"),t=h.prototype.destroy||w;function u(a,b){h.call(this),this._sanitizeOptions(a),this._options=a,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],b&&this.on("response",b);var c=this;this._onNativeResponse=function(a){try{c._processResponse(a)}catch(a){c.emit("error",a instanceof p?a:new p({cause:a}))}},this._performRequest()}function v(a){var b={maxRedirects:21,maxBodyLength:0xa00000},c={};return Object.keys(a).forEach(function(d){var f=d+":",g=c[f]=a[d],h=b[d]=Object.create(g);Object.defineProperties(h,{request:{value:function(a,d,g){var h;return(h=a,e&&h instanceof e)?a=z(a):D(a)?a=z(x(a)):(g=d,d=y(a),a={protocol:f}),E(d)&&(g=d,d=null),(d=Object.assign({maxRedirects:b.maxRedirects,maxBodyLength:b.maxBodyLength},a,d)).nativeProtocols=c,D(d.host)||D(d.hostname)||(d.hostname="::1"),i.equal(d.protocol,f,"protocol mismatch"),j("options",d),new u(d,g)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(a,b,c){var d=h.request(a,b,c);return d.end(),d},configurable:!0,enumerable:!0,writable:!0}})}),b}function w(){}function x(a){var b;if(k)b=new e(a);else if(!D((b=y(d.parse(a))).protocol))throw new o({input:a});return b}function y(a){if(/^\[/.test(a.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(a.hostname)||/^\[/.test(a.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(a.host))throw new o({input:a.href||a});return a}function z(a,b){var c=b||{};for(var d of l)c[d]=a[d];return c.hostname.startsWith("[")&&(c.hostname=c.hostname.slice(1,-1)),""!==c.port&&(c.port=Number(c.port)),c.path=c.search?c.pathname+c.search:c.pathname,c}function A(a,b){var c;for(var d in b)a.test(d)&&(c=b[d],delete b[d]);return null==c?void 0:String(c).trim()}function B(a,b,c){function d(c){E(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,c||{}),this.code=a,this.message=this.cause?b+": "+this.cause.message:b}return d.prototype=new(c||Error),Object.defineProperties(d.prototype,{constructor:{value:d,enumerable:!1},name:{value:"Error ["+a+"]",enumerable:!1}}),d}function C(a,b){for(var c of m)a.removeListener(c,n[c]);a.on("error",w),a.destroy(b)}function D(a){return"string"==typeof a||a instanceof String}function E(a){return"function"==typeof a}u.prototype=Object.create(h.prototype),u.prototype.abort=function(){C(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},u.prototype.destroy=function(a){return C(this._currentRequest,a),t.call(this,a),this},u.prototype.write=function(a,b,c){var d;if(this._ending)throw new s;if(!D(a)&&!("object"==typeof(d=a)&&"length"in d))throw TypeError("data should be a string, Buffer or Uint8Array");if(E(b)&&(c=b,b=null),0===a.length){c&&c();return}this._requestBodyLength+a.length<=this._options.maxBodyLength?(this._requestBodyLength+=a.length,this._requestBodyBuffers.push({data:a,encoding:b}),this._currentRequest.write(a,b,c)):(this.emit("error",new r),this.abort())},u.prototype.end=function(a,b,c){if(E(a)?(c=a,a=b=null):E(b)&&(c=b,b=null),a){var d=this,e=this._currentRequest;this.write(a,b,function(){d._ended=!0,e.end(null,null,c)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,c)},u.prototype.setHeader=function(a,b){this._options.headers[a]=b,this._currentRequest.setHeader(a,b)},u.prototype.removeHeader=function(a){delete this._options.headers[a],this._currentRequest.removeHeader(a)},u.prototype.setTimeout=function(a,b){var c=this;function d(b){b.setTimeout(a),b.removeListener("timeout",b.destroy),b.addListener("timeout",b.destroy)}function e(b){c._timeout&&clearTimeout(c._timeout),c._timeout=setTimeout(function(){c.emit("timeout"),f()},a),d(b)}function f(){c._timeout&&(clearTimeout(c._timeout),c._timeout=null),c.removeListener("abort",f),c.removeListener("error",f),c.removeListener("response",f),c.removeListener("close",f),b&&c.removeListener("timeout",b),c.socket||c._currentRequest.removeListener("socket",e)}return b&&this.on("timeout",b),this.socket?e(this.socket):this._currentRequest.once("socket",e),this.on("socket",d),this.on("abort",f),this.on("error",f),this.on("response",f),this.on("close",f),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(a){u.prototype[a]=function(b,c){return this._currentRequest[a](b,c)}}),["aborted","connection","socket"].forEach(function(a){Object.defineProperty(u.prototype,a,{get:function(){return this._currentRequest[a]}})}),u.prototype._sanitizeOptions=function(a){if(a.headers||(a.headers={}),a.host&&(a.hostname||(a.hostname=a.host),delete a.host),!a.pathname&&a.path){var b=a.path.indexOf("?");b<0?a.pathname=a.path:(a.pathname=a.path.substring(0,b),a.search=a.path.substring(b))}},u.prototype._performRequest=function(){var a=this._options.protocol,b=this._options.nativeProtocols[a];if(!b)throw TypeError("Unsupported protocol "+a);if(this._options.agents){var c=a.slice(0,-1);this._options.agent=this._options.agents[c]}var e=this._currentRequest=b.request(this._options,this._onNativeResponse);for(var f of(e._redirectable=this,m))e.on(f,n[f]);if(this._currentUrl=/^\//.test(this._options.path)?d.format(this._options):this._options.path,this._isRedirect){var g=0,h=this,i=this._requestBodyBuffers;!function a(b){if(e===h._currentRequest)if(b)h.emit("error",b);else if(g<i.length){var c=i[g++];e.finished||e.write(c.data,c.encoding,a)}else h._ended&&e.end()}()}},u.prototype._processResponse=function(a){var b,c,f,g,h,l,m=a.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:a.headers,statusCode:m});var n=a.headers.location;if(!n||!1===this._options.followRedirects||m<300||m>=400){a.responseUrl=this._currentUrl,a.redirects=this._redirects,this.emit("response",a),this._requestBodyBuffers=[];return}if(C(this._currentRequest),a.destroy(),++this._redirectCount>this._options.maxRedirects)throw new q;var o=this._options.beforeRedirect;o&&(l=Object.assign({Host:a.req.getHeader("host")},this._options.headers));var p=this._options.method;(301!==m&&302!==m||"POST"!==this._options.method)&&(303!==m||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],A(/^content-/i,this._options.headers));var r=A(/^host$/i,this._options.headers),s=x(this._currentUrl),t=r||s.host,u=/^\w+:/.test(n)?this._currentUrl:d.format(Object.assign(s,{host:t})),v=(b=n,c=u,k?new e(b,c):x(d.resolve(c,b)));if(j("redirecting to",v.href),this._isRedirect=!0,z(v,this._options),(v.protocol===s.protocol||"https:"===v.protocol)&&(v.host===t||(f=v.host,g=t,i(D(f)&&D(g)),(h=f.length-g.length-1)>0&&"."===f[h]&&f.endsWith(g)))||A(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),E(o)){var w={headers:a.headers,statusCode:m},y={url:u,method:p,headers:l};o(this._options,w,y),this._sanitizeOptions(this._options)}this._performRequest()},a.exports=v({http:f,https:g}),a.exports.wrap=v},24485:(a,b,c)=>{"use strict";var d=c(52469)("%Object.defineProperty%",!0),e=c(91740)(),f=c(27154),g=c(8767),h=e?Symbol.toStringTag:null;a.exports=function(a,b){var c=arguments.length>2&&!!arguments[2]&&arguments[2].force,e=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==c&&"boolean"!=typeof c||void 0!==e&&"boolean"!=typeof e)throw new g("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");h&&(c||!f(a,h))&&(d?d(a,h,{configurable:!e,enumerable:!1,value:b,writable:!1}):a[h]=b)}},26132:a=>{a.exports=function(a,b){var c=!Array.isArray(a),d={index:0,keyedList:c||b?Object.keys(a):null,jobs:{},results:c?{}:[],size:c?Object.keys(a).length:a.length};return b&&d.keyedList.sort(c?b:function(c,d){return b(a[c],a[d])}),d}},27154:(a,b,c)=>{"use strict";var d=Function.prototype.call,e=Object.prototype.hasOwnProperty;a.exports=c(87123).call(d,e)},28326:a=>{"use strict";a.exports=Function.prototype.apply},32361:(a,b,c)=>{"use strict";var d=c(1042),e=c(33873).extname,f=/^\s*([^;\s]*)(?:;|\s|$)/,g=/^text\//i;function h(a){if(!a||"string"!=typeof a)return!1;var b=f.exec(a),c=b&&d[b[1].toLowerCase()];return c&&c.charset?c.charset:!!(b&&g.test(b[1]))&&"UTF-8"}b.charset=h,b.charsets={lookup:h},b.contentType=function(a){if(!a||"string"!=typeof a)return!1;var c=-1===a.indexOf("/")?b.lookup(a):a;if(!c)return!1;if(-1===c.indexOf("charset")){var d=b.charset(c);d&&(c+="; charset="+d.toLowerCase())}return c},b.extension=function(a){if(!a||"string"!=typeof a)return!1;var c=f.exec(a),d=c&&b.extensions[c[1].toLowerCase()];return!!d&&!!d.length&&d[0]},b.extensions=Object.create(null),b.lookup=function(a){if(!a||"string"!=typeof a)return!1;var c=e("x."+a).toLowerCase().substr(1);return!!c&&(b.types[c]||!1)},b.types=Object.create(null),function(a,b){var c=["nginx","apache",void 0,"iana"];Object.keys(d).forEach(function(e){var f=d[e],g=f.extensions;if(g&&g.length){a[e]=g;for(var h=0;h<g.length;h++){var i=g[h];if(b[i]){var j=c.indexOf(d[b[i]].source),k=c.indexOf(f.source);if("application/octet-stream"!==b[i]&&(j>k||j===k&&"application/"===b[i].substr(0,12)))continue}b[i]=e}}})}(b.extensions,b.types)},32872:(a,b,c)=>{"use strict";var d=c(87123),e=c(28326),f=c(46212);a.exports=c(59995)||d.call(f,e)},35609:(a,b,c)=>{"use strict";var d=c(10153),e=c(28354),f=c(33873),g=c(81630),h=c(55591),i=c(79551).parse,j=c(29021),k=c(27910).Stream,l=c(32361),m=c(66713),n=c(24485),o=c(27154),p=c(22862);function q(a){if(!(this instanceof q))return new q(a);for(var b in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],d.call(this),a=a||{})this[b]=a[b]}e.inherits(q,d),q.LINE_BREAK="\r\n",q.DEFAULT_CONTENT_TYPE="application/octet-stream",q.prototype.append=function(a,b,c){"string"==typeof(c=c||{})&&(c={filename:c});var e=d.prototype.append.bind(this);if(("number"==typeof b||null==b)&&(b=String(b)),Array.isArray(b))return void this._error(Error("Arrays are not supported."));var f=this._multiPartHeader(a,b,c),g=this._multiPartFooter();e(f),e(b),e(g),this._trackLength(f,b,c)},q.prototype._trackLength=function(a,b,c){var d=0;null!=c.knownLength?d+=Number(c.knownLength):Buffer.isBuffer(b)?d=b.length:"string"==typeof b&&(d=Buffer.byteLength(b)),this._valueLength+=d,this._overheadLength+=Buffer.byteLength(a)+q.LINE_BREAK.length,b&&(b.path||b.readable&&o(b,"httpVersion")||b instanceof k)&&(c.knownLength||this._valuesToMeasure.push(b))},q.prototype._lengthRetriever=function(a,b){o(a,"fd")?void 0!=a.end&&a.end!=1/0&&void 0!=a.start?b(null,a.end+1-(a.start?a.start:0)):j.stat(a.path,function(c,d){if(c)return void b(c);b(null,d.size-(a.start?a.start:0))}):o(a,"httpVersion")?b(null,Number(a.headers["content-length"])):o(a,"httpModule")?(a.on("response",function(c){a.pause(),b(null,Number(c.headers["content-length"]))}),a.resume()):b("Unknown stream")},q.prototype._multiPartHeader=function(a,b,c){if("string"==typeof c.header)return c.header;var d,e=this._getContentDisposition(b,c),f=this._getContentType(b,c),g="",h={"Content-Disposition":["form-data",'name="'+a+'"'].concat(e||[]),"Content-Type":[].concat(f||[])};for(var i in"object"==typeof c.header&&p(h,c.header),h)if(o(h,i)){if(null==(d=h[i]))continue;Array.isArray(d)||(d=[d]),d.length&&(g+=i+": "+d.join("; ")+q.LINE_BREAK)}return"--"+this.getBoundary()+q.LINE_BREAK+g+q.LINE_BREAK},q.prototype._getContentDisposition=function(a,b){var c;if("string"==typeof b.filepath?c=f.normalize(b.filepath).replace(/\\/g,"/"):b.filename||a&&(a.name||a.path)?c=f.basename(b.filename||a&&(a.name||a.path)):a&&a.readable&&o(a,"httpVersion")&&(c=f.basename(a.client._httpMessage.path||"")),c)return'filename="'+c+'"'},q.prototype._getContentType=function(a,b){var c=b.contentType;return!c&&a&&a.name&&(c=l.lookup(a.name)),!c&&a&&a.path&&(c=l.lookup(a.path)),!c&&a&&a.readable&&o(a,"httpVersion")&&(c=a.headers["content-type"]),!c&&(b.filepath||b.filename)&&(c=l.lookup(b.filepath||b.filename)),!c&&a&&"object"==typeof a&&(c=q.DEFAULT_CONTENT_TYPE),c},q.prototype._multiPartFooter=function(){return(function(a){var b=q.LINE_BREAK;0===this._streams.length&&(b+=this._lastBoundary()),a(b)}).bind(this)},q.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+q.LINE_BREAK},q.prototype.getHeaders=function(a){var b,c={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(b in a)o(a,b)&&(c[b.toLowerCase()]=a[b]);return c},q.prototype.setBoundary=function(a){if("string"!=typeof a)throw TypeError("FormData boundary must be a string");this._boundary=a},q.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},q.prototype.getBuffer=function(){for(var a=new Buffer.alloc(0),b=this.getBoundary(),c=0,d=this._streams.length;c<d;c++)"function"!=typeof this._streams[c]&&(a=Buffer.isBuffer(this._streams[c])?Buffer.concat([a,this._streams[c]]):Buffer.concat([a,Buffer.from(this._streams[c])]),("string"!=typeof this._streams[c]||this._streams[c].substring(2,b.length+2)!==b)&&(a=Buffer.concat([a,Buffer.from(q.LINE_BREAK)])));return Buffer.concat([a,Buffer.from(this._lastBoundary())])},q.prototype._generateBoundary=function(){for(var a="--------------------------",b=0;b<24;b++)a+=Math.floor(10*Math.random()).toString(16);this._boundary=a},q.prototype.getLengthSync=function(){var a=this._overheadLength+this._valueLength;return this._streams.length&&(a+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),a},q.prototype.hasKnownLength=function(){var a=!0;return this._valuesToMeasure.length&&(a=!1),a},q.prototype.getLength=function(a){var b=this._overheadLength+this._valueLength;if(this._streams.length&&(b+=this._lastBoundary().length),!this._valuesToMeasure.length)return void process.nextTick(a.bind(this,null,b));m.parallel(this._valuesToMeasure,this._lengthRetriever,function(c,d){if(c)return void a(c);d.forEach(function(a){b+=a}),a(null,b)})},q.prototype.submit=function(a,b){var c,d,e={method:"post"};return"string"==typeof a?d=p({port:(a=i(a)).port,path:a.pathname,host:a.hostname,protocol:a.protocol},e):(d=p(a,e)).port||(d.port="https:"===d.protocol?443:80),d.headers=this.getHeaders(a.headers),c="https:"===d.protocol?h.request(d):g.request(d),this.getLength((function(a,d){if(a&&"Unknown stream"!==a)return void this._error(a);if(d&&c.setHeader("Content-Length",d),this.pipe(c),b){var e,f=function(a,d){return c.removeListener("error",f),c.removeListener("response",e),b.call(this,a,d)};e=f.bind(this,null),c.on("error",f),c.on("response",e)}}).bind(this)),c},q.prototype._error=function(a){this.error||(this.error=a,this.pause(),this.emit("error",a))},q.prototype.toString=function(){return"[object FormData]"},n(q,"FormData"),a.exports=q},41320:a=>{"use strict";a.exports=Math.floor},41541:a=>{a.exports=function(a){var b="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;b?b(a):setTimeout(a,0)}},42536:(a,b,c)=>{"use strict";a.exports=c(76196).getPrototypeOf||null},44924:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},46196:(a,b,c)=>{var d=c(77447),e=c(26132),f=c(9148);function g(a,b){return a<b?-1:+(a>b)}a.exports=function(a,b,c,g){var h=e(a,c);return d(a,b,h,function c(e,f){return e?void g(e,f):(h.index++,h.index<(h.keyedList||a).length)?void d(a,b,h,c):void g(null,h.results)}),f.bind(h,g)},a.exports.ascending=g,a.exports.descending=function(a,b){return -1*g(a,b)}},46212:a=>{"use strict";a.exports=Function.prototype.call},46449:(a,b,c)=>{var d=c(41541);a.exports=function(a){var b=!1;return d(function(){b=!0}),function(c,e){b?a(c,e):d(function(){a(c,e)})}}},47576:a=>{"use strict";a.exports=Math.pow},48343:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},49230:a=>{"use strict";a.exports=ReferenceError},49810:a=>{"use strict";a.exports=Math.round},50361:(a,b,c)=>{var d=c(46196);a.exports=function(a,b,c){return d(a,b,null,c)}},51181:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},52469:(a,b,c)=>{"use strict";var d,e=c(76196),f=c(77923),g=c(85333),h=c(62214),i=c(49230),j=c(80620),k=c(8767),l=c(73209),m=c(14758),n=c(41320),o=c(67060),p=c(94142),q=c(47576),r=c(49810),s=c(1189),t=Function,u=function(a){try{return t('"use strict"; return ('+a+").constructor;")()}catch(a){}},v=c(18391),w=c(94859),x=function(){throw new k},y=v?function(){try{return arguments.callee,x}catch(a){try{return v(arguments,"callee").get}catch(a){return x}}}():x,z=c(83651)(),A=c(80484),B=c(42536),C=c(7864),D=c(28326),E=c(46212),F={},G="undefined"!=typeof Uint8Array&&A?A(Uint8Array):d,H={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?d:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?d:ArrayBuffer,"%ArrayIteratorPrototype%":z&&A?A([][Symbol.iterator]()):d,"%AsyncFromSyncIteratorPrototype%":d,"%AsyncFunction%":F,"%AsyncGenerator%":F,"%AsyncGeneratorFunction%":F,"%AsyncIteratorPrototype%":F,"%Atomics%":"undefined"==typeof Atomics?d:Atomics,"%BigInt%":"undefined"==typeof BigInt?d:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?d:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?d:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?d:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":f,"%eval%":eval,"%EvalError%":g,"%Float16Array%":"undefined"==typeof Float16Array?d:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?d:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?d:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?d:FinalizationRegistry,"%Function%":t,"%GeneratorFunction%":F,"%Int8Array%":"undefined"==typeof Int8Array?d:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?d:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?d:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":z&&A?A(A([][Symbol.iterator]())):d,"%JSON%":"object"==typeof JSON?JSON:d,"%Map%":"undefined"==typeof Map?d:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&z&&A?A(new Map()[Symbol.iterator]()):d,"%Math%":Math,"%Number%":Number,"%Object%":e,"%Object.getOwnPropertyDescriptor%":v,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?d:Promise,"%Proxy%":"undefined"==typeof Proxy?d:Proxy,"%RangeError%":h,"%ReferenceError%":i,"%Reflect%":"undefined"==typeof Reflect?d:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?d:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&z&&A?A(new Set()[Symbol.iterator]()):d,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?d:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":z&&A?A(""[Symbol.iterator]()):d,"%Symbol%":z?Symbol:d,"%SyntaxError%":j,"%ThrowTypeError%":y,"%TypedArray%":G,"%TypeError%":k,"%Uint8Array%":"undefined"==typeof Uint8Array?d:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?d:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?d:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?d:Uint32Array,"%URIError%":l,"%WeakMap%":"undefined"==typeof WeakMap?d:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?d:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?d:WeakSet,"%Function.prototype.call%":E,"%Function.prototype.apply%":D,"%Object.defineProperty%":w,"%Object.getPrototypeOf%":B,"%Math.abs%":m,"%Math.floor%":n,"%Math.max%":o,"%Math.min%":p,"%Math.pow%":q,"%Math.round%":r,"%Math.sign%":s,"%Reflect.getPrototypeOf%":C};if(A)try{null.error}catch(a){var I=A(A(a));H["%Error.prototype%"]=I}var J=function a(b){var c;if("%AsyncFunction%"===b)c=u("async function () {}");else if("%GeneratorFunction%"===b)c=u("function* () {}");else if("%AsyncGeneratorFunction%"===b)c=u("async function* () {}");else if("%AsyncGenerator%"===b){var d=a("%AsyncGeneratorFunction%");d&&(c=d.prototype)}else if("%AsyncIteratorPrototype%"===b){var e=a("%AsyncGenerator%");e&&A&&(c=A(e.prototype))}return H[b]=c,c},K={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},L=c(87123),M=c(27154),N=L.call(E,Array.prototype.concat),O=L.call(D,Array.prototype.splice),P=L.call(E,String.prototype.replace),Q=L.call(E,String.prototype.slice),R=L.call(E,RegExp.prototype.exec),S=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,T=/\\(\\)?/g,U=function(a){var b=Q(a,0,1),c=Q(a,-1);if("%"===b&&"%"!==c)throw new j("invalid intrinsic syntax, expected closing `%`");if("%"===c&&"%"!==b)throw new j("invalid intrinsic syntax, expected opening `%`");var d=[];return P(a,S,function(a,b,c,e){d[d.length]=c?P(e,T,"$1"):b||a}),d},V=function(a,b){var c,d=a;if(M(K,d)&&(d="%"+(c=K[d])[0]+"%"),M(H,d)){var e=H[d];if(e===F&&(e=J(d)),void 0===e&&!b)throw new k("intrinsic "+a+" exists, but is not available. Please file an issue!");return{alias:c,name:d,value:e}}throw new j("intrinsic "+a+" does not exist!")};a.exports=function(a,b){if("string"!=typeof a||0===a.length)throw new k("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof b)throw new k('"allowMissing" argument must be a boolean');if(null===R(/^%?[^%]*%?$/,a))throw new j("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var c=U(a),d=c.length>0?c[0]:"",e=V("%"+d+"%",b),f=e.name,g=e.value,h=!1,i=e.alias;i&&(d=i[0],O(c,N([0,1],i)));for(var l=1,m=!0;l<c.length;l+=1){var n=c[l],o=Q(n,0,1),p=Q(n,-1);if(('"'===o||"'"===o||"`"===o||'"'===p||"'"===p||"`"===p)&&o!==p)throw new j("property names with quotes must have matching quotes");if("constructor"!==n&&m||(h=!0),d+="."+n,M(H,f="%"+d+"%"))g=H[f];else if(null!=g){if(!(n in g)){if(!b)throw new k("base intrinsic for "+a+" exists, but the property is not available.");return}if(v&&l+1>=c.length){var q=v(g,n);g=(m=!!q)&&"get"in q&&!("originalValue"in q.get)?q.get:g[n]}else m=M(g,n),g=g[n];m&&!h&&(H[f]=g)}}return g}},56100:a=>{"use strict";a.exports=(a,b=process.argv)=>{let c=a.startsWith("-")?"":1===a.length?"-":"--",d=b.indexOf(c+a),e=b.indexOf("--");return -1!==d&&(-1===e||d<e)}},56807:(a,b,c)=>{var d;a.exports=function(){if(!d){try{d=c(9841)("follow-redirects")}catch(a){}"function"!=typeof d&&(d=function(){})}d.apply(null,arguments)}},57455:a=>{a.exports=function(a){Object.keys(a.jobs).forEach(b.bind(a)),a.jobs={}};function b(a){"function"==typeof this.jobs[a]&&this.jobs[a]()}},59995:a=>{"use strict";a.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},62214:a=>{"use strict";a.exports=RangeError},64723:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},65169:(a,b,c)=>{let{createProxy:d}=c(39893);a.exports=d("/home/<USER>/shangrila/node_modules/next/dist/client/app-dir/link.js")},66111:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},66713:(a,b,c)=>{a.exports={parallel:c(98898),serial:c(50361),serialOrdered:c(46196)}},67060:a=>{"use strict";a.exports=Math.max},73007:a=>{"use strict";a.exports=Number.isNaN||function(a){return a!=a}},73097:(a,b,c)=>{let d=c(83997),e=c(28354);b.init=function(a){a.inspectOpts={};let c=Object.keys(b.inspectOpts);for(let d=0;d<c.length;d++)a.inspectOpts[c[d]]=b.inspectOpts[c[d]]},b.log=function(...a){return process.stderr.write(e.formatWithOptions(b.inspectOpts,...a)+"\n")},b.formatArgs=function(c){let{namespace:d,useColors:e}=this;if(e){let b=this.color,e="\x1b[3"+(b<8?b:"8;5;"+b),f=`  ${e};1m${d} \u001B[0m`;c[0]=f+c[0].split("\n").join("\n"+f),c.push(e+"m+"+a.exports.humanize(this.diff)+"\x1b[0m")}else c[0]=(b.inspectOpts.hideDate?"":new Date().toISOString()+" ")+d+" "+c[0]},b.save=function(a){a?process.env.DEBUG=a:delete process.env.DEBUG},b.load=function(){return process.env.DEBUG},b.useColors=function(){return"colors"in b.inspectOpts?!!b.inspectOpts.colors:d.isatty(process.stderr.fd)},b.destroy=e.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),b.colors=[6,2,3,4,5,1];try{let a=c(94883);a&&(a.stderr||a).level>=2&&(b.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(a){}b.inspectOpts=Object.keys(process.env).filter(a=>/^debug_/i.test(a)).reduce((a,b)=>{let c=b.substring(6).toLowerCase().replace(/_([a-z])/g,(a,b)=>b.toUpperCase()),d=process.env[b];return d=!!/^(yes|on|true|enabled)$/i.test(d)||!/^(no|off|false|disabled)$/i.test(d)&&("null"===d?null:Number(d)),a[c]=d,a},{}),a.exports=c(22208)(b);let{formatters:f}=a.exports;f.o=function(a){return this.inspectOpts.colors=this.useColors,e.inspect(a,this.inspectOpts).split("\n").map(a=>a.trim()).join(" ")},f.O=function(a){return this.inspectOpts.colors=this.useColors,e.inspect(a,this.inspectOpts)}},73209:a=>{"use strict";a.exports=URIError},73466:(a,b,c)=>{"use strict";var d=c(87123),e=c(8767),f=c(46212),g=c(32872);a.exports=function(a){if(a.length<1||"function"!=typeof a[0])throw new e("a function is required");return g(d,f,a)}},74264:(a,b,c)=>{"use strict";var d,e=c(73466),f=c(18391);try{d=[].__proto__===Array.prototype}catch(a){if(!a||"object"!=typeof a||!("code"in a)||"ERR_PROTO_ACCESS"!==a.code)throw a}var g=!!d&&f&&f(Object.prototype,"__proto__"),h=Object,i=h.getPrototypeOf;a.exports=g&&"function"==typeof g.get?e([g.get]):"function"==typeof i&&function(a){return i(null==a?a:h(a))}},76196:a=>{"use strict";a.exports=Object},77447:(a,b,c)=>{var d=c(46449),e=c(57455);a.exports=function(a,b,c,f){var g,h,i,j,k=c.keyedList?c.keyedList[c.index]:c.index;c.jobs[k]=(g=b,h=k,i=a[k],j=function(a,b){k in c.jobs&&(delete c.jobs[k],a?e(c):c.results[k]=b,f(a,c.results))},2==g.length?g(i,d(j)):g(i,h,d(j)))}},77923:a=>{"use strict";a.exports=Error},79148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},79669:a=>{"use strict";a.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var a={},b=Symbol("test"),c=Object(b);if("string"==typeof b||"[object Symbol]"!==Object.prototype.toString.call(b)||"[object Symbol]"!==Object.prototype.toString.call(c))return!1;for(var d in a[b]=42,a)return!1;if("function"==typeof Object.keys&&0!==Object.keys(a).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(a).length)return!1;var e=Object.getOwnPropertySymbols(a);if(1!==e.length||e[0]!==b||!Object.prototype.propertyIsEnumerable.call(a,b))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var f=Object.getOwnPropertyDescriptor(a,b);if(42!==f.value||!0!==f.enumerable)return!1}return!0}},80484:(a,b,c)=>{"use strict";var d=c(7864),e=c(42536),f=c(74264);a.exports=d?function(a){return d(a)}:e?function(a){if(!a||"object"!=typeof a&&"function"!=typeof a)throw TypeError("getProto: not an object");return e(a)}:f?function(a){return f(a)}:null},80620:a=>{"use strict";a.exports=SyntaxError},81231:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(4290).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},83651:(a,b,c)=>{"use strict";var d="undefined"!=typeof Symbol&&Symbol,e=c(79669);a.exports=function(){return"function"==typeof d&&"function"==typeof Symbol&&"symbol"==typeof d("foo")&&"symbol"==typeof Symbol("bar")&&e()}},84933:a=>{"use strict";a.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},85333:a=>{"use strict";a.exports=EvalError},87123:(a,b,c)=>{"use strict";var d=c(90305);a.exports=Function.prototype.bind||d},87736:(a,b,c)=>{"use strict";var d=c(79551).parse,e={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},f=String.prototype.endsWith||function(a){return a.length<=this.length&&-1!==this.indexOf(a,this.length-a.length)};function g(a){return process.env[a.toLowerCase()]||process.env[a.toUpperCase()]||""}b.getProxyForUrl=function(a){var b,c,h,i="string"==typeof a?d(a):a||{},j=i.protocol,k=i.host,l=i.port;if("string"!=typeof k||!k||"string"!=typeof j)return"";if(j=j.split(":",1)[0],b=k=k.replace(/:\d*$/,""),c=l=parseInt(l)||e[j]||0,!(!(h=(g("npm_config_no_proxy")||g("no_proxy")).toLowerCase())||"*"!==h&&h.split(/[,\s]/).every(function(a){if(!a)return!0;var d=a.match(/^(.+):(\d+)$/),e=d?d[1]:a,g=d?parseInt(d[2]):0;return!!g&&g!==c||(/^[.*]/.test(e)?("*"===e.charAt(0)&&(e=e.slice(1)),!f.call(b,e)):b!==e)})))return"";var m=g("npm_config_"+j+"_proxy")||g(j+"_proxy")||g("npm_config_proxy")||g("all_proxy");return m&&-1===m.indexOf("://")&&(m=j+"://"+m),m}},90305:a=>{"use strict";var b=Object.prototype.toString,c=Math.max,d=function(a,b){for(var c=[],d=0;d<a.length;d+=1)c[d]=a[d];for(var e=0;e<b.length;e+=1)c[e+a.length]=b[e];return c},e=function(a,b){for(var c=[],d=b||0,e=0;d<a.length;d+=1,e+=1)c[e]=a[d];return c},f=function(a,b){for(var c="",d=0;d<a.length;d+=1)c+=a[d],d+1<a.length&&(c+=b);return c};a.exports=function(a){var g,h=this;if("function"!=typeof h||"[object Function]"!==b.apply(h))throw TypeError("Function.prototype.bind called on incompatible "+h);for(var i=e(arguments,1),j=c(0,h.length-i.length),k=[],l=0;l<j;l++)k[l]="$"+l;if(g=Function("binder","return function ("+f(k,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof g){var b=h.apply(this,d(i,arguments));return Object(b)===b?b:this}return h.apply(a,d(i,arguments))}),h.prototype){var m=function(){};m.prototype=h.prototype,g.prototype=new m,m.prototype=null}return g}},91740:(a,b,c)=>{"use strict";var d=c(79669);a.exports=function(){return d()&&!!Symbol.toStringTag}},94142:a=>{"use strict";a.exports=Math.min},94859:a=>{"use strict";var b=Object.defineProperty||!1;if(b)try{b({},"a",{value:1})}catch(a){b=!1}a.exports=b},94883:(a,b,c)=>{"use strict";let d,e=c(21820),f=c(83997),g=c(56100),{env:h}=process;function i(a){return 0!==a&&{level:a,hasBasic:!0,has256:a>=2,has16m:a>=3}}function j(a,b){if(0===d)return 0;if(g("color=16m")||g("color=full")||g("color=truecolor"))return 3;if(g("color=256"))return 2;if(a&&!b&&void 0===d)return 0;let c=d||0;if("dumb"===h.TERM)return c;if("win32"===process.platform){let a=e.release().split(".");return Number(a[0])>=10&&Number(a[2])>=10586?Number(a[2])>=14931?3:2:1}if("CI"in h)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(a=>a in h)||"codeship"===h.CI_NAME?1:c;if("TEAMCITY_VERSION"in h)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(h.TEAMCITY_VERSION);if("truecolor"===h.COLORTERM)return 3;if("TERM_PROGRAM"in h){let a=parseInt((h.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(h.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(h.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(h.TERM)||"COLORTERM"in h?1:c}g("no-color")||g("no-colors")||g("color=false")||g("color=never")?d=0:(g("color")||g("colors")||g("color=true")||g("color=always"))&&(d=1),"FORCE_COLOR"in h&&(d="true"===h.FORCE_COLOR?1:"false"===h.FORCE_COLOR?0:0===h.FORCE_COLOR.length?1:Math.min(parseInt(h.FORCE_COLOR,10),3)),a.exports={supportsColor:function(a){return i(j(a,a&&a.isTTY))},stdout:i(j(!0,f.isatty(1))),stderr:i(j(!0,f.isatty(2)))}},98898:(a,b,c)=>{var d=c(77447),e=c(26132),f=c(9148);a.exports=function(a,b,c){for(var g=e(a);g.index<(g.keyedList||a).length;)d(a,b,g,function(a,b){return a?void c(a,b):0===Object.keys(g.jobs).length?void c(null,g.results):void 0}),g.index++;return f.bind(g,c)}}};