exports.id=925,exports.ids=[925],exports.modules={17124:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,81170,23)),Promise.resolve().then(c.t.bind(c,23597,23)),Promise.resolve().then(c.t.bind(c,36893,23)),Promise.resolve().then(c.t.bind(c,89748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,69576,23)),Promise.resolve().then(c.t.bind(c,73041,23)),Promise.resolve().then(c.t.bind(c,51384,23))},20839:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>P,metadata:()=>N});var d=c(75338),e=c(1595),f=c.n(e),g=c(44207),h=c.n(g),i=c(83168),j=c.n(i);c(61135);var k=c(74515);let l=0,m=new Map,n=a=>{if(m.has(a))return;let b=setTimeout(()=>{m.delete(a),q({type:"REMOVE_TOAST",toastId:a})},1e6);m.set(a,b)},o=[],p={toasts:[]};function q(a){p=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?n(c):a.toasts.forEach(a=>{n(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(p,a),o.forEach(a=>{a(p)})}function r({...a}){let b=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),c=()=>q({type:"DISMISS_TOAST",toastId:b});return q({type:"ADD_TOAST",toast:{...a,id:b,open:!0,onOpenChange:a=>{a||c()}}}),{id:b,dismiss:c,update:a=>q({type:"UPDATE_TOAST",toast:{...a,id:b}})}}var s=c(56414),t=c(86281),u=c(97571),v=c(70673);let w=s.Provider,x=k.forwardRef(({className:a,...b},c)=>(0,d.jsx)(s.Viewport,{ref:c,className:(0,v.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...b}));x.displayName=s.Viewport.displayName;let y=(0,t.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),z=k.forwardRef(({className:a,variant:b,...c},e)=>(0,d.jsx)(s.Root,{ref:e,className:(0,v.cn)(y({variant:b}),a),...c}));z.displayName=s.Root.displayName,k.forwardRef(({className:a,...b},c)=>(0,d.jsx)(s.Action,{ref:c,className:(0,v.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...b})).displayName=s.Action.displayName;let A=k.forwardRef(({className:a,...b},c)=>(0,d.jsx)(s.Close,{ref:c,className:(0,v.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...b,children:(0,d.jsx)(u.A,{className:"h-4 w-4"})}));A.displayName=s.Close.displayName;let B=k.forwardRef(({className:a,...b},c)=>(0,d.jsx)(s.Title,{ref:c,className:(0,v.cn)("text-sm font-semibold",a),...b}));B.displayName=s.Title.displayName;let C=k.forwardRef(({className:a,...b},c)=>(0,d.jsx)(s.Description,{ref:c,className:(0,v.cn)("text-sm opacity-90",a),...b}));function D(){let{toasts:a}=function(){let[a,b]=k.useState(p);return k.useEffect(()=>(o.push(b),()=>{let a=o.indexOf(b);a>-1&&o.splice(a,1)}),[a]),{...a,toast:r,dismiss:a=>q({type:"DISMISS_TOAST",toastId:a})}}();return(0,d.jsxs)(w,{children:[a.map(function({id:a,title:b,description:c,action:e,...f}){return(0,d.jsxs)(z,{...f,children:[(0,d.jsxs)("div",{className:"grid gap-1",children:[b&&(0,d.jsx)(B,{children:b}),c&&(0,d.jsx)(C,{children:c})]}),e,(0,d.jsx)(A,{})]},a)}),(0,d.jsx)(x,{})]})}C.displayName=s.Description.displayName;var E=c(81049),F=c(16076);let G=({...a})=>{let{theme:b="system"}=(0,E.useTheme)();return(0,d.jsx)(F.Toaster,{theme:b,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...a})};var H=c(69894);let I=H.Provider;H.Root,H.Trigger,k.forwardRef(({className:a,sideOffset:b=4,...c},e)=>(0,d.jsx)(H.Content,{ref:e,sideOffset:b,className:(0,v.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c})).displayName=H.Content.displayName;var J=c(68894),K=c(50468),L=c(97811),M=c.n(L);let N={metadataBase:new URL("https://shangriladistillery.com"),title:{default:"Shangrila Distillery | Premium Craft Spirits from Nepal",template:"%s | Shangrila Distillery"},description:"Shangrila Distillery produces world-class craft spirits in Nepal. Discover our premium gins, vodkas, and whiskeys, made with Himalayan purity.",keywords:["Shangrila Distillery","Nepal Whiskey","Nepal Vodka","Premium Spirits","Craft Distillery","Himalayan Spirits","Nepal Gin"],authors:[{name:"Shangrila Distillery"}],creator:"Shangrila Distillery",publisher:"Shangrila Distillery",formatDetection:{email:!1,address:!1,telephone:!1},openGraph:{type:"website",locale:"en_US",url:"https://shangriladistillery.com",title:"Shangrila Distillery | Premium Spirits from Nepal",description:"Discover world-class gin, vodka, and whiskey from Shangrila Distillery.",siteName:"Shangrila Distillery",images:[{url:"/lovable-uploads/favicon-shangrila.png",width:1200,height:630,alt:"Shangrila Distillery Logo"}]},twitter:{card:"summary_large_image",title:"Shangrila Distillery | Nepal's Craft Distillery",description:"Crafting premium spirits in the heart of the Himalayas.",images:["/lovable-uploads/favicon-shangrila.png"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}},O=new J.E;function P({children:a}){return(0,d.jsxs)("html",{lang:"en",className:`${f().variable} ${h().variable} ${j().variable}`,children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"canonical",href:"https://shangriladistillery.com"}),(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Distillery",name:"Shangrila Distillery",url:"https://shangriladistillery.com",logo:"https://shangriladistillery.com/lovable-uploads/favicon-shangrila.png",description:"Shangrila Distillery is a premium spirits producer in Nepal, offering gin, vodka, and whiskey.",address:{"@type":"PostalAddress",streetAddress:"Brahmanagar, Rapti Nagarpalika-9",addressLocality:"Chitwan",addressCountry:"NP"},contactPoint:{"@type":"ContactPoint",telephone:"+977-1-4528118",contactType:"customer service",email:"<EMAIL>"},sameAs:["https://www.linkedin.com/company/shangrila-distillery/","https://www.facebook.com/profile.php?id=61577339984580","https://www.instagram.com/shangriladistillery"]})}})]}),(0,d.jsx)("body",{children:(0,d.jsx)(K.QueryClientProvider,{client:O,children:(0,d.jsxs)(I,{children:[(0,d.jsx)(D,{}),(0,d.jsx)(G,{}),(0,d.jsx)(M(),{children:a})]})})})]})}},34613:(a,b,c)=>{Promise.resolve().then(c.bind(c,98510)),Promise.resolve().then(c.bind(c,95384)),Promise.resolve().then(c.bind(c,76596)),Promise.resolve().then(c.bind(c,59217)),Promise.resolve().then(c.bind(c,96610)),Promise.resolve().then(c.bind(c,90295)),Promise.resolve().then(c.bind(c,86111)),Promise.resolve().then(c.bind(c,49787)),Promise.resolve().then(c.bind(c,45267)),Promise.resolve().then(c.bind(c,99043)),Promise.resolve().then(c.bind(c,98206)),Promise.resolve().then(c.bind(c,93415)),Promise.resolve().then(c.bind(c,64371)),Promise.resolve().then(c.bind(c,61855)),Promise.resolve().then(c.bind(c,41555)),Promise.resolve().then(c.bind(c,45523)),Promise.resolve().then(c.bind(c,42830))},44341:(a,b,c)=>{Promise.resolve().then(c.bind(c,56414)),Promise.resolve().then(c.bind(c,69894)),Promise.resolve().then(c.bind(c,99258)),Promise.resolve().then(c.bind(c,11787)),Promise.resolve().then(c.bind(c,50468)),Promise.resolve().then(c.bind(c,29033)),Promise.resolve().then(c.bind(c,13085)),Promise.resolve().then(c.bind(c,67209)),Promise.resolve().then(c.bind(c,97510)),Promise.resolve().then(c.bind(c,95261)),Promise.resolve().then(c.bind(c,73617)),Promise.resolve().then(c.bind(c,40929)),Promise.resolve().then(c.bind(c,14321)),Promise.resolve().then(c.bind(c,87589)),Promise.resolve().then(c.bind(c,18893)),Promise.resolve().then(c.bind(c,81049)),Promise.resolve().then(c.bind(c,16076))},61135:()=>{},70276:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,54160,23)),Promise.resolve().then(c.t.bind(c,31603,23)),Promise.resolve().then(c.t.bind(c,68495,23)),Promise.resolve().then(c.t.bind(c,75170,23)),Promise.resolve().then(c.t.bind(c,77526,23)),Promise.resolve().then(c.t.bind(c,78922,23)),Promise.resolve().then(c.t.bind(c,29234,23)),Promise.resolve().then(c.t.bind(c,12263,23)),Promise.resolve().then(c.bind(c,82146))},70673:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(81171),e=c(11167);function f(...a){return(0,e.QP)((0,d.$)(a))}},97811:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m You\'re importing a component that needs `useState`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.\n  \x1b[31m|\x1b[0m\n  \x1b[31m|\x1b[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client\n  \x1b[31m|\x1b[0m\n\n   ,-[\x1b[36;1;4m/home/<USER>/shangrila/src/components/WebsiteWrapper.tsx\x1b[0m:2:1]\n \x1b[2m1\x1b[0m | \n \x1b[2m2\x1b[0m | import { useState, useEffect } from "react";\n   : \x1b[35;1m         ^^^^^^^^\x1b[0m\n \x1b[2m3\x1b[0m | import AgeVerification from "./AgeVerification";\n \x1b[2m4\x1b[0m | \n \x1b[2m5\x1b[0m | interface WebsiteWrapperProps {\n   `----\n  \x1b[31mx\x1b[0m You\'re importing a component that needs `useEffect`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.\n  \x1b[31m|\x1b[0m\n  \x1b[31m|\x1b[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client\n  \x1b[31m|\x1b[0m\n\n   ,-[\x1b[36;1;4m/home/<USER>/shangrila/src/components/WebsiteWrapper.tsx\x1b[0m:2:1]\n \x1b[2m1\x1b[0m | \n \x1b[2m2\x1b[0m | import { useState, useEffect } from "react";\n   : \x1b[35;1m                   ^^^^^^^^^\x1b[0m\n \x1b[2m3\x1b[0m | import AgeVerification from "./AgeVerification";\n \x1b[2m4\x1b[0m | \n \x1b[2m5\x1b[0m | interface WebsiteWrapperProps {\n   `----\n')}};