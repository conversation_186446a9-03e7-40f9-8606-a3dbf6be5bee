// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/about" | "/blog" | "/blog/[slug]" | "/contact" | "/exports" | "/facility" | "/leadership/prajanna-raj-adhikari" | "/our-leadership" | "/products" | "/sales"
type PageRoutes = "/About" | "/Blog" | "/Careers" | "/Contact" | "/Distillery" | "/Events" | "/Exports" | "/Facility" | "/Index" | "/Leadership" | "/NotFound" | "/PrajannaProfile" | "/PrivacyPolicy" | "/Products" | "/ResponsibleDrinking" | "/Sales" | "/TermsOfService" | "/blog/BlogPost"
type LayoutRoutes = "/"
type RedirectRoutes = "/global-partners"
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes


interface ParamMap {
  "/": {}
  "/about": {}
  "/About": {}
  "/blog": {}
  "/Blog": {}
  "/blog/[slug]": { "slug": string; }
  "/blog/BlogPost": {}
  "/Careers": {}
  "/contact": {}
  "/Contact": {}
  "/Distillery": {}
  "/Events": {}
  "/exports": {}
  "/Exports": {}
  "/facility": {}
  "/Facility": {}
  "/global-partners": {}
  "/Index": {}
  "/Leadership": {}
  "/leadership/prajanna-raj-adhikari": {}
  "/NotFound": {}
  "/our-leadership": {}
  "/PrajannaProfile": {}
  "/PrivacyPolicy": {}
  "/products": {}
  "/Products": {}
  "/ResponsibleDrinking": {}
  "/sales": {}
  "/Sales": {}
  "/TermsOfService": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }
}
