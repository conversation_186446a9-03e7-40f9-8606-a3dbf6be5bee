// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type PagesPageConfig = {
  default: React.ComponentType<any> | ((props: any) => React.ReactNode | Promise<React.ReactNode> | never | void)
  getStaticProps?: (context: any) => Promise<any> | any
  getStaticPaths?: (context: any) => Promise<any> | any
  getServerSideProps?: (context: any) => Promise<any> | any
  getInitialProps?: (context: any) => Promise<any> | any
  /**
   * Segment configuration for legacy Pages Router pages.
   * Validated at build-time by parsePagesSegmentConfig.
   */
  config?: {
    amp?: boolean | 'hybrid' | string // necessary for JS
    maxDuration?: number
    runtime?: 'edge' | 'experimental-edge' | 'nodejs' | string // necessary unless config is exported as const
    regions?: string[]
  }
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../src/app/about/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/about">> = Specific
  const handler = {} as typeof import("../../src/app/about/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/blog/[slug]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/blog/[slug]">> = Specific
  const handler = {} as typeof import("../../src/app/blog/[slug]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/blog/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/blog">> = Specific
  const handler = {} as typeof import("../../src/app/blog/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/contact/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/contact">> = Specific
  const handler = {} as typeof import("../../src/app/contact/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/exports/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/exports">> = Specific
  const handler = {} as typeof import("../../src/app/exports/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/facility/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/facility">> = Specific
  const handler = {} as typeof import("../../src/app/facility/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/leadership/prajanna-raj-adhikari/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/leadership/prajanna-raj-adhikari">> = Specific
  const handler = {} as typeof import("../../src/app/leadership/prajanna-raj-adhikari/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/our-leadership/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/our-leadership">> = Specific
  const handler = {} as typeof import("../../src/app/our-leadership/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/products/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/products">> = Specific
  const handler = {} as typeof import("../../src/app/products/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/sales/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/sales">> = Specific
  const handler = {} as typeof import("../../src/app/sales/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}



// Validate ../../src/pages/About.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/About.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Blog.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Blog.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Careers.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Careers.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Contact.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Contact.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Distillery.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Distillery.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Events.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Events.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Exports.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Exports.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Facility.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Facility.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Index.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Index.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Leadership.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Leadership.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/NotFound.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/NotFound.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/PrajannaProfile.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/PrajannaProfile.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/PrivacyPolicy.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/PrivacyPolicy.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Products.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Products.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/ResponsibleDrinking.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/ResponsibleDrinking.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/Sales.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/Sales.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/TermsOfService.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/TermsOfService.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/pages/blog/BlogPost.tsx
{
  type __IsExpected<Specific extends PagesPageConfig> = Specific
  const handler = {} as typeof import("../../src/pages/blog/BlogPost.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}



// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
