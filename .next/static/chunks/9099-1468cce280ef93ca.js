"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9099],{294:(e,t,n)=>{n.d(t,{UC:()=>nf,YJ:()=>nh,In:()=>nc,q7:()=>nm,VF:()=>ng,p4:()=>ny,JU:()=>nv,ZL:()=>nd,bL:()=>na,wn:()=>nb,PP:()=>nw,wv:()=>nx,l9:()=>nu,WT:()=>ns,LM:()=>np});var r,o,l,i,a=n(2115),u=n.t(a,2),s=n(7650);function c(e,[t,n]){return Math.min(n,Math.max(t,e))}function d(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function f(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function p(e,t){var n=f(e,t,"get");return n.get?n.get.call(e):n.value}function h(e,t,n){var r=f(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var v=n(5155);function m(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),l=n.length;n=[...n,r];let i=t=>{let{scope:n,children:r,...i}=t,u=n?.[e]?.[l]||o,s=a.useMemo(()=>i,Object.values(i));return(0,v.jsx)(u.Provider,{value:s,children:r})};return i.displayName=t+"Provider",[i,function(n,i){let u=i?.[e]?.[l]||o,s=a.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function y(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function g(...e){return t=>{let n=!1,r=e.map(e=>{let r=y(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():y(e[t],null)}}}}function w(...e){return a.useCallback(g(...e),e)}function b(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:n,...r}=e;if(a.isValidElement(n)){var o;let e,l,i=(o=n,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==a.Fragment&&(u.ref=t?g(t,i):i),a.cloneElement(n,u)}return a.Children.count(n)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=a.forwardRef((e,n)=>{let{children:r,...o}=e,l=a.Children.toArray(r),i=l.find(E);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,v.jsx)(t,{...o,ref:n,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,v.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var x=Symbol("radix.slottable");function E(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===x}var S=new WeakMap;function C(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=R(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function R(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap,class e extends Map{set(e,t){return S.get(this)&&(this.has(e)?p(this,o)[p(this,o).indexOf(e)]=e:p(this,o).push(e)),super.set(e,t),this}insert(e,t,n){let r,l=this.has(t),i=p(this,o).length,a=R(e),u=a>=0?a:i+a,s=u<0||u>=i?-1:u;if(s===this.size||l&&s===this.size-1||-1===s)return this.set(t,n),this;let c=this.size+ +!l;a<0&&u++;let d=[...p(this,o)],f=!1;for(let e=u;e<c;e++)if(u===e){let o=d[e];d[e]===t&&(o=d[e+1]),l&&this.delete(t),r=this.get(o),this.set(t,n)}else{f||d[e-1]!==t||(f=!0);let n=d[f?e:e-1],o=r;r=this.get(n),this.delete(n),this.set(n,o)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=p(this,o).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=p(this,o).indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=p(this,o).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=p(this,o).indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return h(this,o,[]),super.clear()}delete(e){let t=super.delete(e);return t&&p(this,o).splice(p(this,o).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=C(p(this,o),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=C(p(this,o),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return p(this,o).indexOf(e)}keyAt(e){return C(p(this,o),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,l=0,i=null!=o?o:this.at(0);for(let e of this)i=0===l&&1===t.length?e:Reflect.apply(r,this,[i,e,l,this]),l++;return i}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,l=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);l=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[l,n,e,this])}return l}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,o,{writable:!0,value:void 0}),h(this,o,[...super.keys()]),S.set(this,!0)}};var A=a.createContext(void 0),j=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=b(`Primitive.${t}`),r=a.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,v.jsx)(o?n:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function P(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var k="dismissableLayer.update",N=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),T=a.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:s,onInteractOutside:c,onDismiss:f,...p}=e,h=a.useContext(N),[m,y]=a.useState(null),g=null!=(r=null==m?void 0:m.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,b]=a.useState({}),x=w(t,e=>y(e)),E=Array.from(h.layers),[S]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),C=E.indexOf(S),R=m?E.indexOf(m):-1,A=h.layersWithOutsidePointerEventsDisabled.size>0,T=R>=C,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=P(e),o=a.useRef(!1),l=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){L("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));T&&!n&&(null==u||u(e),null==c||c(e),e.defaultPrevented||null==f||f())},g),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=P(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&L("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==s||s(e),null==c||c(e),e.defaultPrevented||null==f||f())},g);return!function(e,t=globalThis?.document){let n=function(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{R===h.layers.size-1&&(null==i||i(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},g),a.useEffect(()=>{if(m)return o&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(l=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(m)),h.layers.add(m),M(),()=>{o&&1===h.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=l)}},[m,g,o,h]),a.useEffect(()=>()=>{m&&(h.layers.delete(m),h.layersWithOutsidePointerEventsDisabled.delete(m),M())},[m,h]),a.useEffect(()=>{let e=()=>b({});return document.addEventListener(k,e),()=>document.removeEventListener(k,e)},[]),(0,v.jsx)(j.div,{...p,ref:x,style:{pointerEvents:A?T?"auto":"none":void 0,...e.style},onFocusCapture:d(e.onFocusCapture,I.onFocusCapture),onBlurCapture:d(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:d(e.onPointerDownCapture,O.onPointerDownCapture)})});function M(){let e=new CustomEvent(k);document.dispatchEvent(e)}function L(e,t,n,r){let{discrete:o}=r,l=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&l.addEventListener(e,t,{once:!0}),o)l&&s.flushSync(()=>l.dispatchEvent(i));else l.dispatchEvent(i)}T.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(N),r=a.useRef(null),o=w(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(j.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var O=0;function I(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}function D(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function W(...e){return t=>{let n=!1,r=e.map(e=>{let r=D(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():D(e[t],null)}}}}var _=Symbol("radix.slottable");function B(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===_}var F=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:n,...r}=e;if(a.isValidElement(n)){var o;let e,l,i=(o=n,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==a.Fragment&&(u.ref=t?W(t,i):i),a.cloneElement(n,u)}return a.Children.count(n)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=a.forwardRef((e,n)=>{let{children:r,...o}=e,l=a.Children.toArray(r),i=l.find(B);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,v.jsx)(t,{...o,ref:n,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,v.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),r=a.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,v.jsx)(o?n:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function z(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var H="focusScope.autoFocusOnMount",V="focusScope.autoFocusOnUnmount",K={bubbles:!1,cancelable:!0},$=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...i}=e,[u,s]=a.useState(null),c=z(o),d=z(l),f=a.useRef(null),p=function(...e){return a.useCallback(W(...e),e)}(t,e=>s(e)),h=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(h.paused||!u)return;let t=e.target;u.contains(t)?f.current=t:X(f.current,{select:!0})},t=function(e){if(h.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||X(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&X(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,h.paused]),a.useEffect(()=>{if(u){q.add(h);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(H,K);u.addEventListener(H,c),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(X(r,{select:t}),document.activeElement!==n)return}(U(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&X(u))}return()=>{u.removeEventListener(H,c),setTimeout(()=>{let t=new CustomEvent(V,K);u.addEventListener(V,d),u.dispatchEvent(t),t.defaultPrevented||X(null!=e?e:document.body,{select:!0}),u.removeEventListener(V,d),q.remove(h)},0)}}},[u,c,d,h]);let m=a.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,l]=function(e){let t=U(e);return[Y(t,e),Y(t.reverse(),e)]}(t);r&&l?e.shiftKey||o!==l?e.shiftKey&&o===r&&(e.preventDefault(),n&&X(l,{select:!0})):(e.preventDefault(),n&&X(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,v.jsx)(F.div,{tabIndex:-1,...i,ref:p,onKeyDown:m})});function U(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Y(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function X(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}$.displayName="FocusScope";var q=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=Z(e,t)).unshift(t)},remove(t){var n;null==(n=(e=Z(e,t))[0])||n.resume()}}}();function Z(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var G=globalThis?.document?a.useLayoutEffect:()=>{},J=u[" useId ".trim().toString()]||(()=>void 0),Q=0;function ee(e){let[t,n]=a.useState(J());return G(()=>{e||n(e=>e??String(Q++))},[e]),e||(t?`radix-${t}`:"")}var et=n(8334),en=n(8146),er=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,v.jsx)(j.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});er.displayName="Arrow";var eo="Popper",[el,ei]=m(eo),[ea,eu]=el(eo),es=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,v.jsx)(ea,{scope:t,anchor:r,onAnchorChange:o,children:n})};es.displayName=eo;var ec="PopperAnchor",ed=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=eu(ec,n),i=a.useRef(null),u=w(t,i);return a.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||i.current)}),r?null:(0,v.jsx)(j.div,{...o,ref:u})});ed.displayName=ec;var ef="PopperContent",[ep,eh]=el(ef),ev=a.forwardRef((e,t)=>{var n,r,o,l,i,u,s,c;let{__scopePopper:d,side:f="bottom",sideOffset:p=0,align:h="center",alignOffset:m=0,arrowPadding:y=0,avoidCollisions:g=!0,collisionBoundary:b=[],collisionPadding:x=0,sticky:E="partial",hideWhenDetached:S=!1,updatePositionStrategy:C="optimized",onPlaced:R,...A}=e,k=eu(ef,d),[N,T]=a.useState(null),M=w(t,e=>T(e)),[L,O]=a.useState(null),I=function(e){let[t,n]=a.useState(void 0);return G(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(L),D=null!=(s=null==I?void 0:I.width)?s:0,W=null!=(c=null==I?void 0:I.height)?c:0,_="number"==typeof x?x:{top:0,right:0,bottom:0,left:0,...x},B=Array.isArray(b)?b:[b],F=B.length>0,z={padding:_,boundary:B.filter(ew),altBoundary:F},{refs:H,floatingStyles:V,placement:K,isPositioned:$,middlewareData:U}=(0,et.we)({strategy:"fixed",placement:f+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,en.ll)(...t,{animationFrame:"always"===C})},elements:{reference:k.anchor},middleware:[(0,et.cY)({mainAxis:p+W,alignmentAxis:m}),g&&(0,et.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===E?(0,et.ER)():void 0,...z}),g&&(0,et.UU)({...z}),(0,et.Ej)({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),L&&(0,et.UE)({element:L,padding:y}),eb({arrowWidth:D,arrowHeight:W}),S&&(0,et.jD)({strategy:"referenceHidden",...z})]}),[Y,X]=ex(K),q=P(R);G(()=>{$&&(null==q||q())},[$,q]);let Z=null==(n=U.arrow)?void 0:n.x,J=null==(r=U.arrow)?void 0:r.y,Q=(null==(o=U.arrow)?void 0:o.centerOffset)!==0,[ee,er]=a.useState();return G(()=>{N&&er(window.getComputedStyle(N).zIndex)},[N]),(0,v.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:$?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[null==(l=U.transformOrigin)?void 0:l.x,null==(i=U.transformOrigin)?void 0:i.y].join(" "),...(null==(u=U.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(ep,{scope:d,placedSide:Y,onArrowChange:O,arrowX:Z,arrowY:J,shouldHideArrow:Q,children:(0,v.jsx)(j.div,{"data-side":Y,"data-align":X,...A,ref:M,style:{...A.style,animation:$?void 0:"none"}})})})});ev.displayName=ef;var em="PopperArrow",ey={top:"bottom",right:"left",bottom:"top",left:"right"},eg=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eh(em,n),l=ey[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(er,{...r,ref:t,style:{...r.style,display:"block"}})})});function ew(e){return null!==e}eg.displayName=em;var eb=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=ex(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(l=null==(r=s.arrow)?void 0:r.x)?l:0)+d/2,y=(null!=(i=null==(o=s.arrow)?void 0:o.y)?i:0)+f/2,g="",w="";return"bottom"===p?(g=c?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(g=c?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),w=c?v:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),w=c?v:"".concat(y,"px")),{data:{x:g,y:w}}}});function ex(e){let[t,n="center"]=e.split("-");return[t,n]}function eE(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var eS=Symbol("radix.slottable");function eC(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===eS}var eR=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:n,...r}=e;if(a.isValidElement(n)){var o;let e,l,i=(o=n,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==a.Fragment&&(u.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=eE(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():eE(e[t],null)}}}}(t,i):i),a.cloneElement(n,u)}return a.Children.count(n)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=a.forwardRef((e,n)=>{let{children:r,...o}=e,l=a.Children.toArray(r),i=l.find(eC);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,v.jsx)(t,{...o,ref:n,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,v.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),r=a.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,v.jsx)(o?n:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),eA=globalThis?.document?a.useLayoutEffect:()=>{},ej=a.forwardRef((e,t)=>{var n,r;let{container:o,...l}=e,[i,u]=a.useState(!1);eA(()=>u(!0),[]);let c=o||i&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?s.createPortal((0,v.jsx)(eR.div,{...l,ref:t}),c):null});ej.displayName="Portal";var eP=u[" useInsertionEffect ".trim().toString()]||G;function ek({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,i]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),l=a.useRef(t);return eP(()=>{l.current=t},[t]),a.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,a.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&i.current?.(n)}else l(t)},[u,e,l,i])]}Symbol("RADIX:SYNC_STATE");var eN=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});a.forwardRef((e,t)=>(0,v.jsx)(j.span,{...e,ref:t,style:{...eN,...e.style}})).displayName="VisuallyHidden";var eT=new WeakMap,eM=new WeakMap,eL={},eO=0,eI=function(e){return e&&(e.host||eI(e.parentNode))},eD=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eI(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eL[n]||(eL[n]=new WeakMap);var l=eL[n],i=[],a=new Set,u=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var c=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(eT.get(e)||0)+1,s=(l.get(e)||0)+1;eT.set(e,u),l.set(e,s),i.push(e),1===u&&o&&eM.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),eO++,function(){i.forEach(function(e){var t=eT.get(e)-1,o=l.get(e)-1;eT.set(e,t),l.set(e,o),t||(eM.has(e)||e.removeAttribute(r),eM.delete(e)),o||e.removeAttribute(n)}),--eO||(eT=new WeakMap,eT=new WeakMap,eM=new WeakMap,eL={})}},eW=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),eD(r,o,n,"aria-hidden")):function(){return null}},e_=function(){return(e_=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function eB(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;var eF="right-scroll-bar-position",ez="width-before-scroll-bar";function eH(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var eV="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,eK=new WeakMap;function e$(e){return e}var eU=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=e$),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var l=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return o.options=e_({async:!0,ssr:!1},e),o}(),eY=function(){},eX=a.forwardRef(function(e,t){var n,r,o,l,i=a.useRef(null),u=a.useState({onScrollCapture:eY,onWheelCapture:eY,onTouchMoveCapture:eY}),s=u[0],c=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,v=e.enabled,m=e.shards,y=e.sideCar,g=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,S=e.gapMode,C=eB(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[i,t],r=function(e){return n.forEach(function(t){return eH(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,eV(function(){var e=eK.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||eH(e,null)}),r.forEach(function(e){t.has(e)||eH(e,o)})}eK.set(l,n)},[n]),l),A=e_(e_({},C),s);return a.createElement(a.Fragment,null,v&&a.createElement(y,{sideCar:eU,removeScrollBar:h,shards:m,noRelative:g,noIsolation:w,inert:b,setCallbacks:c,allowPinchZoom:!!x,lockRef:i,gapMode:S}),d?a.cloneElement(a.Children.only(f),e_(e_({},A),{ref:R})):a.createElement(void 0===E?"div":E,e_({},A,{className:p,ref:R}),f))});eX.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},eX.classNames={fullWidth:ez,zeroRight:eF};var eq=function(e){var t=e.sideCar,n=eB(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,e_({},n))};eq.isSideCarExport=!0;var eZ=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},eG=function(){var e=eZ();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},eJ=function(){var e=eG();return function(t){return e(t.styles,t.dynamic),null}},eQ={left:0,top:0,right:0,gap:0},e0=function(e){return parseInt(e||"",10)||0},e1=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[e0(n),e0(r),e0(o)]},e2=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return eQ;var t=e1(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},e5=eJ(),e6="data-scroll-locked",e3=function(e,t,n,r){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(e6,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(eF," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(ez," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(eF," .").concat(eF," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(ez," .").concat(ez," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(e6,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},e9=function(){var e=parseInt(document.body.getAttribute(e6)||"0",10);return isFinite(e)?e:0},e4=function(){a.useEffect(function(){return document.body.setAttribute(e6,(e9()+1).toString()),function(){var e=e9()-1;e<=0?document.body.removeAttribute(e6):document.body.setAttribute(e6,e.toString())}},[])},e8=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;e4();var l=a.useMemo(function(){return e2(o)},[o]);return a.createElement(e5,{styles:e3(l,!t,o,n?"":"!important")})},e7=!1;if("undefined"!=typeof window)try{var te=Object.defineProperty({},"passive",{get:function(){return e7=!0,!0}});window.addEventListener("test",te,te),window.removeEventListener("test",te,te)}catch(e){e7=!1}var tt=!!e7&&{passive:!1},tn=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},tr=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),to(e,r)){var o=tl(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},to=function(e,t){return"v"===e?tn(t,"overflowY"):tn(t,"overflowX")},tl=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ti=function(e,t,n,r,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=tl(e,u),v=h[0],m=h[1]-h[2]-i*v;(v||m)&&to(e,u)&&(f+=m,p+=v);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},ta=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},tu=function(e){return[e.deltaX,e.deltaY]},ts=function(e){return e&&"current"in e?e.current:e},tc=0,td=[];let tf=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(tc++)[0],l=a.useState(eJ)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ts),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=ta(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],s="deltaY"in e?e.deltaY:a[1]-l[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=tr(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=tr(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return ti(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(td.length&&td[td.length-1]===l){var n="deltaY"in e?tu(e):ta(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(ts).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),d=a.useCallback(function(e){n.current=ta(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,tu(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,ta(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return td.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,tt),document.addEventListener("touchmove",s,tt),document.addEventListener("touchstart",d,tt),function(){td=td.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,tt),document.removeEventListener("touchmove",s,tt),document.removeEventListener("touchstart",d,tt)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(e8,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},eU.useMedium(r),eq);var tp=a.forwardRef(function(e,t){return a.createElement(eX,e_({},e,{ref:t,sideCar:tf}))});tp.classNames=eX.classNames;var th=[" ","Enter","ArrowUp","ArrowDown"],tv=[" ","Enter"],tm="Select",[ty,tg,tw]=function(e){let t=e+"CollectionProvider",[n,r]=m(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:n}=e,r=a.useRef(null),l=a.useRef(new Map).current;return(0,v.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};i.displayName=t;let u=e+"CollectionSlot",s=b(u),c=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=w(t,l(u,n).collectionRef);return(0,v.jsx)(s,{ref:o,children:r})});c.displayName=u;let d=e+"CollectionItemSlot",f="data-radix-collection-item",p=b(d),h=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,i=a.useRef(null),u=w(t,i),s=l(d,n);return a.useEffect(()=>(s.itemMap.set(i,{ref:i,...o}),()=>void s.itemMap.delete(i))),(0,v.jsx)(p,{...{[f]:""},ref:u,children:r})});return h.displayName=d,[{Provider:i,Slot:c,ItemSlot:h},function(t){let n=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(f,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(tm),[tb,tx]=m(tm,[tw,ei]),tE=ei(),[tS,tC]=tb(tm),[tR,tA]=tb(tm),tj=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:l,value:i,defaultValue:u,onValueChange:s,dir:c,name:d,autoComplete:f,disabled:p,required:h,form:m}=e,y=tE(t),[g,w]=a.useState(null),[b,x]=a.useState(null),[E,S]=a.useState(!1),C=function(e){let t=a.useContext(A);return e||t||"ltr"}(c),[R,j]=ek({prop:r,defaultProp:null!=o&&o,onChange:l,caller:tm}),[P,k]=ek({prop:i,defaultProp:u,onChange:s,caller:tm}),N=a.useRef(null),T=!g||m||!!g.closest("form"),[M,L]=a.useState(new Set),O=Array.from(M).map(e=>e.props.value).join(";");return(0,v.jsx)(es,{...y,children:(0,v.jsxs)(tS,{required:h,scope:t,trigger:g,onTriggerChange:w,valueNode:b,onValueNodeChange:x,valueNodeHasChildren:E,onValueNodeHasChildrenChange:S,contentId:ee(),value:P,onValueChange:k,open:R,onOpenChange:j,dir:C,triggerPointerDownPosRef:N,disabled:p,children:[(0,v.jsx)(ty.Provider,{scope:t,children:(0,v.jsx)(tR,{scope:e.__scopeSelect,onNativeOptionAdd:a.useCallback(e=>{L(t=>new Set(t).add(e))},[]),onNativeOptionRemove:a.useCallback(e=>{L(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),T?(0,v.jsxs)(nr,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:P,onChange:e=>k(e.target.value),disabled:p,form:m,children:[void 0===P?(0,v.jsx)("option",{value:""}):null,Array.from(M)]},O):null]})})};tj.displayName=tm;var tP="SelectTrigger",tk=a.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,l=tE(n),i=tC(tP,n),u=i.disabled||r,s=w(t,i.onTriggerChange),c=tg(n),f=a.useRef("touch"),[p,h,m]=nl(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===i.value),r=ni(t,e,n);void 0!==r&&i.onValueChange(r.value)}),y=e=>{u||(i.onOpenChange(!0),m()),e&&(i.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,v.jsx)(ed,{asChild:!0,...l,children:(0,v.jsx)(j.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":no(i.value)?"":void 0,...o,ref:s,onClick:d(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:d(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:d(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&th.includes(e.key)&&(y(),e.preventDefault())})})})});tk.displayName=tP;var tN="SelectValue",tT=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=tC(tN,n),{onValueNodeHasChildrenChange:s}=u,c=void 0!==l,d=w(t,u.onValueNodeChange);return G(()=>{s(c)},[s,c]),(0,v.jsx)(j.span,{...a,ref:d,style:{pointerEvents:"none"},children:no(u.value)?(0,v.jsx)(v.Fragment,{children:i}):l})});tT.displayName=tN;var tM=a.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,v.jsx)(j.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tM.displayName="SelectIcon";var tL=e=>(0,v.jsx)(ej,{asChild:!0,...e});tL.displayName="SelectPortal";var tO="SelectContent",tI=a.forwardRef((e,t)=>{let n=tC(tO,e.__scopeSelect),[r,o]=a.useState();return(G(()=>{o(new DocumentFragment)},[]),n.open)?(0,v.jsx)(tB,{...e,ref:t}):r?s.createPortal((0,v.jsx)(tD,{scope:e.__scopeSelect,children:(0,v.jsx)(ty.Slot,{scope:e.__scopeSelect,children:(0,v.jsx)("div",{children:e.children})})}),r):null});tI.displayName=tO;var[tD,tW]=tb(tO),t_=b("SelectContent.RemoveScroll"),tB=a.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:l,onPointerDownOutside:i,side:u,sideOffset:s,align:c,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:g,avoidCollisions:b,...x}=e,E=tC(tO,n),[S,C]=a.useState(null),[R,A]=a.useState(null),j=w(t,e=>C(e)),[P,k]=a.useState(null),[N,M]=a.useState(null),L=tg(n),[D,W]=a.useState(!1),_=a.useRef(!1);a.useEffect(()=>{if(S)return eW(S)},[S]),a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:I()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:I()),O++,()=>{1===O&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),O--}},[]);let B=a.useCallback(e=>{let[t,...n]=L().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[L,R]),F=a.useCallback(()=>B([P,S]),[B,P,S]);a.useEffect(()=>{D&&F()},[D,F]);let{onOpenChange:z,triggerPointerDownPosRef:H}=E;a.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=H.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=H.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||z(!1),document.removeEventListener("pointermove",t),H.current=null};return null!==H.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,z,H]),a.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[V,K]=nl(e=>{let t=L().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ni(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),U=a.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==E.value&&E.value===t||r)&&(k(e),r&&(_.current=!0))},[E.value]),Y=a.useCallback(()=>null==S?void 0:S.focus(),[S]),X=a.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==E.value&&E.value===t||r)&&M(e)},[E.value]),q="popper"===r?tz:tF,Z=q===tz?{side:u,sideOffset:s,align:c,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:g,avoidCollisions:b}:{};return(0,v.jsx)(tD,{scope:n,content:S,viewport:R,onViewportChange:A,itemRefCallback:U,selectedItem:P,onItemLeave:Y,itemTextRefCallback:X,focusSelectedItem:F,selectedItemText:N,position:r,isPositioned:D,searchRef:V,children:(0,v.jsx)(tp,{as:t_,allowPinchZoom:!0,children:(0,v.jsx)($,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:d(o,e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,v.jsx)(T,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,v.jsx)(q,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...x,...Z,onPlaced:()=>W(!0),ref:j,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:d(x.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=L().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});tB.displayName="SelectContentImpl";var tF=a.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,l=tC(tO,n),i=tW(tO,n),[u,s]=a.useState(null),[d,f]=a.useState(null),p=w(t,e=>f(e)),h=tg(n),m=a.useRef(!1),y=a.useRef(!0),{viewport:g,selectedItem:b,selectedItemText:x,focusSelectedItem:E}=i,S=a.useCallback(()=>{if(l.trigger&&l.valueNode&&u&&d&&g&&b&&x){let e=l.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),o=x.getBoundingClientRect();if("rtl"!==l.dir){let r=o.left-t.left,l=n.left-r,i=e.left-l,a=e.width+i,s=Math.max(a,t.width),d=c(l,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.left=d+"px"}else{let r=t.right-o.right,l=window.innerWidth-n.right-r,i=window.innerWidth-e.right-l,a=e.width+i,s=Math.max(a,t.width),d=c(l,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.right=d+"px"}let i=h(),a=window.innerHeight-20,s=g.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),v=parseInt(f.paddingTop,10),y=parseInt(f.borderBottomWidth,10),w=p+v+s+parseInt(f.paddingBottom,10)+y,E=Math.min(5*b.offsetHeight,w),S=window.getComputedStyle(g),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,j=b.offsetHeight/2,P=p+v+(b.offsetTop+j);if(P<=A){let e=i.length>0&&b===i[i.length-1].ref.current;u.style.bottom="0px";let t=Math.max(a-A,j+(e?R:0)+(d.clientHeight-g.offsetTop-g.offsetHeight)+y);u.style.height=P+t+"px"}else{let e=i.length>0&&b===i[0].ref.current;u.style.top="0px";let t=Math.max(A,p+g.offsetTop+(e?C:0)+j);u.style.height=t+(w-P)+"px",g.scrollTop=P-A+g.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=E+"px",u.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>m.current=!0)}},[h,l.trigger,l.valueNode,u,d,g,b,x,l.dir,r]);G(()=>S(),[S]);let[C,R]=a.useState();G(()=>{d&&R(window.getComputedStyle(d).zIndex)},[d]);let A=a.useCallback(e=>{e&&!0===y.current&&(S(),null==E||E(),y.current=!1)},[S,E]);return(0,v.jsx)(tH,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:A,children:(0,v.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,v.jsx)(j.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});tF.displayName="SelectItemAlignedPosition";var tz=a.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=tE(n);return(0,v.jsx)(ev,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tz.displayName="SelectPopperPosition";var[tH,tV]=tb(tO,{}),tK="SelectViewport",t$=a.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,l=tW(tK,n),i=tV(tK,n),u=w(t,l.onViewportChange),s=a.useRef(0);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,v.jsx)(ty.Slot,{scope:n,children:(0,v.jsx)(j.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:d(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=i;if((null==r?void 0:r.current)&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});t$.displayName=tK;var tU="SelectGroup",[tY,tX]=tb(tU),tq=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ee();return(0,v.jsx)(tY,{scope:n,id:o,children:(0,v.jsx)(j.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tq.displayName=tU;var tZ="SelectLabel",tG=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tX(tZ,n);return(0,v.jsx)(j.div,{id:o.id,...r,ref:t})});tG.displayName=tZ;var tJ="SelectItem",[tQ,t0]=tb(tJ),t1=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:l,...i}=e,u=tC(tJ,n),s=tW(tJ,n),c=u.value===r,[f,p]=a.useState(null!=l?l:""),[h,m]=a.useState(!1),y=w(t,e=>{var t;return null==(t=s.itemRefCallback)?void 0:t.call(s,e,r,o)}),g=ee(),b=a.useRef("touch"),x=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,v.jsx)(tQ,{scope:n,value:r,disabled:o,textId:g,isSelected:c,onItemTextChange:a.useCallback(e=>{p(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,v.jsx)(ty.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,v.jsx)(j.div,{role:"option","aria-labelledby":g,"data-highlighted":h?"":void 0,"aria-selected":c&&h,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:y,onFocus:d(i.onFocus,()=>m(!0)),onBlur:d(i.onBlur,()=>m(!1)),onClick:d(i.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:d(i.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:d(i.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:d(i.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=s.onItemLeave)||t.call(s)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:d(i.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=s.onItemLeave)||t.call(s)}}),onKeyDown:d(i.onKeyDown,e=>{var t;((null==(t=s.searchRef)?void 0:t.current)===""||" "!==e.key)&&(tv.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});t1.displayName=tJ;var t2="SelectItemText",t5=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...l}=e,i=tC(t2,n),u=tW(t2,n),c=t0(t2,n),d=tA(t2,n),[f,p]=a.useState(null),h=w(t,e=>p(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,y=a.useMemo(()=>(0,v.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:g,onNativeOptionRemove:b}=d;return G(()=>(g(y),()=>b(y)),[g,b,y]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(j.span,{id:c.textId,...l,ref:h}),c.isSelected&&i.valueNode&&!i.valueNodeHasChildren?s.createPortal(l.children,i.valueNode):null]})});t5.displayName=t2;var t6="SelectItemIndicator",t3=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t0(t6,n).isSelected?(0,v.jsx)(j.span,{"aria-hidden":!0,...r,ref:t}):null});t3.displayName=t6;var t9="SelectScrollUpButton",t4=a.forwardRef((e,t)=>{let n=tW(t9,e.__scopeSelect),r=tV(t9,e.__scopeSelect),[o,l]=a.useState(!1),i=w(t,r.onScrollButtonChange);return G(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(ne,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t4.displayName=t9;var t8="SelectScrollDownButton",t7=a.forwardRef((e,t)=>{let n=tW(t8,e.__scopeSelect),r=tV(t8,e.__scopeSelect),[o,l]=a.useState(!1),i=w(t,r.onScrollButtonChange);return G(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(ne,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t7.displayName=t8;var ne=a.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,l=tW("SelectScrollButton",n),i=a.useRef(null),u=tg(n),s=a.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return a.useEffect(()=>()=>s(),[s]),G(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,v.jsx)(j.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:d(o.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(r,50))}),onPointerMove:d(o.onPointerMove,()=>{var e;null==(e=l.onItemLeave)||e.call(l),null===i.current&&(i.current=window.setInterval(r,50))}),onPointerLeave:d(o.onPointerLeave,()=>{s()})})}),nt=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,v.jsx)(j.div,{"aria-hidden":!0,...r,ref:t})});nt.displayName="SelectSeparator";var nn="SelectArrow";a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tE(n),l=tC(nn,n),i=tW(nn,n);return l.open&&"popper"===i.position?(0,v.jsx)(eg,{...o,...r,ref:t}):null}).displayName=nn;var nr=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,l=a.useRef(null),i=w(t,l),u=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return a.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,v.jsx)(j.select,{...o,style:{...eN,...o.style},ref:i,defaultValue:r})});function no(e){return""===e||void 0===e}function nl(e){let t=P(e),n=a.useRef(""),r=a.useRef(0),o=a.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=a.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return a.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,l]}function ni(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return u!==n?u:void 0}nr.displayName="SelectBubbleInput";var na=tj,nu=tk,ns=tT,nc=tM,nd=tL,nf=tI,np=t$,nh=tq,nv=tG,nm=t1,ny=t5,ng=t3,nw=t4,nb=t7,nx=nt},2108:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(1847).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},4033:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(1847).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},5917:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(1847).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])}}]);