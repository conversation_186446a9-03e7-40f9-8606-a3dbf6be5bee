"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4952],{4952:(e,t,r)=>{r.d(t,{N_:()=>w});var n,o,i,s,a=r(4232),c=r(8477),l=r(8307),u=r(8116);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}let h=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(e){}(n||(n=r.t(a,2))).startTransition,(o||(o=r.t(c,2))).flushSync,(n||(n=r.t(a,2))).useId;let d="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,p=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,w=a.forwardRef(function(e,t){let r,{onClick:n,relative:o,reloadDocument:i,replace:s,state:c,target:w,to:b,preventScrollReset:v,viewTransition:y}=e,S=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,h),{basename:m}=a.useContext(l.jb),g=!1;if("string"==typeof b&&p.test(b)&&(r=b,d))try{let e=new URL(window.location.href),t=new URL(b.startsWith("//")?e.protocol+b:b),r=(0,u.pb)(t.pathname,m);t.origin===e.origin&&null!=r?b=r+t.search+t.hash:g=!0}catch(e){}let R=(0,l.$P)(b,{relative:o}),U=function(e,t){let{target:r,replace:n,state:o,preventScrollReset:i,relative:s,viewTransition:c}=void 0===t?{}:t,f=(0,l.Zp)(),h=(0,l.zy)(),d=(0,l.x$)(e,{relative:s});return a.useCallback(t=>{0!==t.button||r&&"_self"!==r||t.metaKey||t.altKey||t.ctrlKey||t.shiftKey||(t.preventDefault(),f(e,{replace:void 0!==n?n:(0,u.AO)(h)===(0,u.AO)(d),state:o,preventScrollReset:i,relative:s,viewTransition:c}))},[h,f,d,n,o,r,e,i,s,c])}(b,{replace:s,state:c,target:w,preventScrollReset:v,relative:o,viewTransition:y});return a.createElement("a",f({},S,{href:r||R,onClick:g||i?n:function(e){n&&n(e),e.defaultPrevented||U(e)},ref:t,target:w}))});!function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"}(i||(i={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(s||(s={}))}}]);