(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7892],{1804:(e,s,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/Contact",function(){return a(8706)}])},4099:(e,s,a)=>{"use strict";a.d(s,{J:()=>o});var r=a(7876),t=a(4232),l=a(9687),i=a(7137),n=a(6680);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(l.b,{ref:s,className:(0,n.cn)(d(),a),...t})});o.displayName=l.b.displayName},5989:(e,s,a)=>{"use strict";a.d(s,{T:()=>i});var r=a(7876),t=a(4232),l=a(6680);let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...t})});i.displayName="Textarea"},6283:(e,s,a)=>{"use strict";a.d(s,{$:()=>o});var r=a(7876),t=a(4232),l=a(1138),i=a(7137),n=a(6680);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=t.forwardRef((e,s)=>{let{className:a,variant:t,size:i,asChild:o=!1,...m}=e,c=o?l.DX:"button";return(0,r.jsx)(c,{className:(0,n.cn)(d({variant:t,size:i,className:a})),ref:s,...m})});o.displayName="Button"},6625:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var r=a(7876),t=a(4232),l=a(6680);let i=t.forwardRef((e,s)=>{let{className:a,type:t,...i}=e;return(0,r.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...i})});i.displayName="Input"},6680:(e,s,a)=>{"use strict";a.d(s,{cn:()=>l});var r=a(9241),t=a(9573);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,r.$)(s))}},8706:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>_});var r=a(7876),t=a(3744),l=a(7009),i=a(2958),n=a(9890),d=a(7295),o=a(6283),m=a(6625),c=a(4099),x=a(5989),u=a(4232),p=a(1215),h=a(5316),f=a(8129),b=a(458),g=a(6680);let y=p.bL;p.YJ;let N=p.WT,j=u.forwardRef((e,s)=>{let{className:a,children:t,...l}=e;return(0,r.jsxs)(p.l9,{ref:s,className:(0,g.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[t,(0,r.jsx)(p.In,{asChild:!0,children:(0,r.jsx)(h.A,{className:"h-4 w-4 opacity-50"})})]})});j.displayName=p.l9.displayName;let v=u.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(p.PP,{ref:s,className:(0,g.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})});v.displayName=p.PP.displayName;let w=u.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(p.wn,{ref:s,className:(0,g.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})});w.displayName=p.wn.displayName;let q=u.forwardRef((e,s)=>{let{className:a,children:t,position:l="popper",...i}=e;return(0,r.jsx)(p.ZL,{children:(0,r.jsxs)(p.UC,{ref:s,className:(0,g.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:l,...i,children:[(0,r.jsx)(v,{}),(0,r.jsx)(p.LM,{className:(0,g.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(w,{})]})})});q.displayName=p.UC.displayName,u.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(p.JU,{ref:s,className:(0,g.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...t})}).displayName=p.JU.displayName;let T=u.forwardRef((e,s)=>{let{className:a,children:t,...l}=e;return(0,r.jsxs)(p.q7,{ref:s,className:(0,g.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(p.VF,{children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})}),(0,r.jsx)(p.p4,{children:t})]})});T.displayName=p.q7.displayName,u.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(p.wv,{ref:s,className:(0,g.cn)("-mx-1 my-1 h-px bg-muted",a),...t})}).displayName=p.wv.displayName;let C=()=>(0,r.jsxs)("div",{className:"distillery-card p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-4",children:"Find Us"}),(0,r.jsx)("div",{className:"w-full h-64 rounded-lg overflow-hidden border border-amber-500/30",children:(0,r.jsx)("iframe",{title:"Shangrila Distillery Location",src:"https://www.google.com/maps?q=27.7172,85.3240&z=15&output=embed",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade"})})]});var P=a(623),k=a(1040),F=a(9688),A=a(5364);let _=()=>{let[e,s]=(0,u.useState)({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""}),[a,p]=(0,u.useState)(!1),[h,f]=(0,u.useState)({}),b=a=>{let{name:r,value:t}=a.target;s({...e,[r]:t}),h[r]&&f({...h,[r]:""})},g=async a=>{if(a.preventDefault(),(()=>{let s={};return e.firstName.trim()||(s.firstName="First name is required"),e.lastName.trim()||(s.lastName="Last name is required"),e.email?/\S+@\S+\.\S+/.test(e.email)||(s.email="Email is invalid"):s.email="Email is required",e.phone?/^[0-9\s\-+()]*$/.test(e.phone)||(s.phone="Please enter a valid phone number"):s.phone="Phone number is required",e.inquiryType||(s.inquiryType="Please select an inquiry type"),e.message.trim()||(s.message="Message is required"),f(s),0===Object.keys(s).length})()){p(!0);try{let a=await k.A.post("".concat(A.env.NEXT_PUBLIC_API_URL||"https://mail.shangriladistillery.com","/api/contact"),e,{headers:{"Content-Type":"application/json"}});if(200===a.status)F.oR.success("Your message has been sent successfully!"),s({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""});else throw Error("Failed to send message")}catch(e){var r,t;console.error("Error sending message:",e),F.oR.error((null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"Failed to send message. Please try again later.")}finally{p(!1)}}};return(0,r.jsxs)("div",{className:"min-h-screen distillery-gradient",children:[(0,r.jsx)(t.A,{}),(0,r.jsxs)("div",{className:"pt-20",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-playfair font-bold text-amber-100 mb-6",children:"Visit Us"}),(0,r.jsx)("p",{className:"text-xl distillery-text max-w-3xl mx-auto",children:"Get in touch with our team for inquiries, partnerships, or to learn more about our premium spirits."})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{className:"distillery-card p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)(l.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Email"}),(0,r.jsx)("p",{className:"distillery-text",children:"<EMAIL>"})]})]})}),(0,r.jsx)("div",{className:"distillery-card p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)(i.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Phone"}),(0,r.jsx)("p",{className:"distillery-text",children:"+977-1-4528118"}),(0,r.jsx)("p",{className:"distillery-text",children:"WhatsApp: +977 1-4528118"})]})]})}),(0,r.jsxs)("div",{className:"distillery-card p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)(n.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Head Office Address"}),(0,r.jsxs)("p",{className:"distillery-text",children:["Pipalbot dillibazar-29",(0,r.jsx)("br",{}),"kathmandu nepal"]})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)(n.A,{className:"h-6 w-6 text-amber-400 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-playfair font-semibold text-amber-100 mb-1",children:"Factory Location"}),(0,r.jsxs)("p",{className:"distillery-text",children:["Brahmanagar, Rapti Nagarpalika-9",(0,r.jsx)("br",{}),"Chitwan, Nepal"]})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-amber-600 to-amber-500 rounded-lg p-6 text-stone-900",children:[(0,r.jsx)(d.A,{className:"h-8 w-8 text-stone-900 mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-playfair font-semibold mb-2",children:"Business Hours"}),(0,r.jsxs)("p",{className:"font-crimson",children:["Sunday - Friday: 9:00 AM - 6:00 PM",(0,r.jsx)("br",{}),"Saturday: Closed",(0,r.jsx)("br",{})]})]}),(0,r.jsx)(C,{})]}),(0,r.jsxs)("div",{className:"distillery-card p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-playfair font-bold text-amber-100 mb-6",children:"Send us a Message"}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:g,children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"firstName",className:"text-amber-200",children:"First Name"}),(0,r.jsx)(m.p,{id:"firstName",name:"firstName",type:"text",value:e.firstName,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(h.firstName?"border-red-500":""),onChange:b}),h.firstName&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:h.firstName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"lastName",className:"text-amber-200",children:"Last Name"}),(0,r.jsx)(m.p,{id:"lastName",name:"lastName",type:"text",value:e.lastName,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(h.lastName?"border-red-500":""),onChange:b}),h.lastName&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:h.lastName})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,r.jsx)(m.p,{id:"email",name:"email",type:"email",value:e.email,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(h.email?"border-red-500":""),onChange:b}),h.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:h.email})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"phone",className:"text-amber-200",children:"Phone Number"}),(0,r.jsx)(m.p,{id:"phone",name:"phone",type:"tel",value:e.phone,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(h.phone?"border-red-500":""),onChange:b,placeholder:"+****************"}),h.phone&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:h.phone})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"inquiryType",className:"text-amber-200",children:"Inquiry Type"}),(0,r.jsxs)(y,{value:e.inquiryType,onValueChange:a=>{s({...e,inquiryType:a}),h.inquiryType&&f({...h,inquiryType:""})},children:[(0,r.jsx)(j,{className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(h.inquiryType?"border-red-500":""),children:(0,r.jsx)(N,{placeholder:"Select inquiry type"})}),(0,r.jsxs)(q,{className:"bg-stone-800 border-amber-500/30",children:[(0,r.jsx)(T,{value:"general",children:"General Inquiry"}),(0,r.jsx)(T,{value:"wholesale",children:"Wholesale Inquiry"}),(0,r.jsx)(T,{value:"partnership",children:"Partnership"}),(0,r.jsx)(T,{value:"press",children:"Press/Media"}),(0,r.jsx)(T,{value:"other",children:"Other"})]})]}),h.inquiryType&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:h.inquiryType})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"subject",className:"text-amber-200",children:"Subject (Optional)"}),(0,r.jsx)(m.p,{id:"subject",name:"subject",type:"text",value:e.subject,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400",onChange:b})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"message",className:"text-amber-200",children:"Message"}),(0,r.jsx)(x.T,{id:"message",name:"message",rows:5,value:e.message,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(h.message?"border-red-500":""),onChange:b}),h.message&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:h.message})]})]}),(0,r.jsx)(o.$,{type:"submit",disabled:a,className:"w-full bg-amber-600 hover:bg-amber-700 text-white py-6 text-lg font-medium transition-colors duration-200 ".concat(a?"opacity-70 cursor-not-allowed":""),children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):"Send Message"})]})]})]})]}),(0,r.jsx)(P.A,{})]})]})}}},e=>{e.O(0,[6743,2172,4339,8890,5328,636,6593,8792],()=>e(e.s=1804)),_N_E=e.O()}]);