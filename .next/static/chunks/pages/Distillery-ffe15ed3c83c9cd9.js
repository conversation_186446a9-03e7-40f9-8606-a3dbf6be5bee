(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4489],{5375:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9065).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6894:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/Distillery",function(){return r(8938)}])},8938:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(7876),a=r(4952),i=r(5375);let l=e=>{let{title:t,description:r}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50",children:[(0,s.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-amber-200 z-50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,s.jsx)(a.N_,{to:"/",className:"flex items-center space-x-2",children:(0,s.jsx)("img",{src:"/lovable-uploads/cfc50220-4059-4960-a0ea-0b328b8473e8.png",alt:"Shangrila Distillery",className:"h-16 w-auto"})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)(a.N_,{to:"/",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Home"}),(0,s.jsx)(a.N_,{to:"/about",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"About"}),(0,s.jsx)(a.N_,{to:"/products",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Products"}),(0,s.jsx)(a.N_,{to:"/distillery",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Distillery"}),(0,s.jsx)(a.N_,{to:"/tours",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Tours"}),(0,s.jsx)(a.N_,{to:"/events",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Events"}),(0,s.jsx)(a.N_,{to:"/contact",className:"text-amber-800 hover:text-amber-600 font-medium transition-colors",children:"Contact"})]})]})})}),(0,s.jsx)("div",{className:"pt-20 flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center max-w-2xl mx-auto px-4",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("img",{src:"/lovable-uploads/cfc50220-4059-4960-a0ea-0b328b8473e8.png",alt:"Shangrila Distillery",className:"h-32 w-auto mx-auto mb-6 opacity-80"})}),(0,s.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-amber-900 mb-4",children:t}),(0,s.jsx)("h2",{className:"text-2xl md:text-3xl font-light text-amber-700 mb-6",children:"Coming Soon"}),(0,s.jsx)("p",{className:"text-lg text-amber-600 mb-8",children:r||"We're crafting something special for you. Stay tuned for updates on this exciting new addition to our distillery experience."}),(0,s.jsx)("div",{className:"bg-white/70 backdrop-blur-sm rounded-lg p-6 mb-8",children:(0,s.jsx)("p",{className:"text-amber-800 font-medium",children:"In the meantime, explore our current offerings and learn about our craft distilling process in the Himalayas."})}),(0,s.jsxs)(a.N_,{to:"/",className:"inline-flex items-center space-x-2 bg-amber-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-800 transition-colors shadow-lg",children:[(0,s.jsx)(i.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Back to Home"})]})]})})]})},o=()=>(0,s.jsx)(l,{title:"The Distillery",description:"Take a virtual tour of our facilities, learn about our distilling process, and see the equipment and techniques that make our spirits extraordinary."})},9065:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(4232);let a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:n,className:c="",children:m,iconNode:d,...x}=e;return(0,s.createElement)("svg",{ref:t,...i,width:l,height:l,stroke:r,strokeWidth:n?24*Number(o)/Number(l):o,className:a("lucide",c),...x},[...d.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),o=(e,t)=>{let r=(0,s.forwardRef)((r,i)=>{let{className:o,...n}=r;return(0,s.createElement)(l,{ref:i,iconNode:t,className:a("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),o),...n})});return r.displayName="".concat(e),r}}},e=>{e.O(0,[3796,8307,4952,636,6593,8792],()=>e(e.s=6894)),_N_E=e.O()}]);