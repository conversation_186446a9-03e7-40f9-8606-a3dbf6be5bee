"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8334],{8146:(t,e,n)=>{n.d(e,{UE:()=>tA,ll:()=>tv,rD:()=>tC,UU:()=>tE,jD:()=>tT,ER:()=>tS,cY:()=>tb,BN:()=>tR,Ej:()=>tL});let r=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,f=Math.floor,a=t=>({x:t,y:t}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function p(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}let g=new Set(["top","bottom"]);function w(t){return g.has(d(t))?"y":"x"}function y(t){return t.replace(/start|end/g,t=>c[t])}let x=["left","right"],v=["right","left"],b=["top","bottom"],R=["bottom","top"];function E(t){return t.replace(/left|right|bottom|top/g,t=>u[t])}function L(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function T(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function A(t,e,n){let r,{reference:i,floating:o}=t,l=w(e),f=p(w(e)),a=m(f),u=d(e),c="y"===l,s=i.x+i.width/2-o.width/2,g=i.y+i.height/2-o.height/2,y=i[a]/2-o[a]/2;switch(u){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:g};break;case"left":r={x:i.x-o.width,y:g};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[f]-=y*(n&&c?-1:1);break;case"end":r[f]+=y*(n&&c?-1:1)}return r}let S=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,f=o.filter(Boolean),a=await (null==l.isRTL?void 0:l.isRTL(e)),u=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:s}=A(u,r,a),d=r,h={},p=0;for(let n=0;n<f.length;n++){let{name:o,fn:m}=f[n],{x:g,y:w,data:y,reset:x}=await m({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:h,rects:u,platform:l,elements:{reference:t,floating:e}});c=null!=g?g:c,s=null!=w?w:s,h={...h,[o]:{...h[o],...y}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(u=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),{x:c,y:s}=A(u,d,a)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:h}};async function C(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:f,strategy:a}=t,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=s(e,t),m=L(p),g=f[h?"floating"===d?"reference":"floating":d],w=T(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(f.floating)),boundary:u,rootBoundary:c,strategy:a})),y="floating"===d?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(f.floating)),v=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=T(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:y,offsetParent:x,strategy:a}):y);return{top:(w.top-b.top+m.top)/v.y,bottom:(b.bottom-w.bottom+m.bottom)/v.y,left:(w.left-b.left+m.left)/v.x,right:(b.right-w.right+m.right)/v.x}}function D(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function O(t){return r.some(e=>t[e]>=0)}let k=new Set(["left","top"]);async function P(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=d(n),f=h(n),a="y"===w(n),u=k.has(l)?-1:1,c=o&&a?-1:1,p=s(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return f&&"number"==typeof y&&(g="end"===f?-1*y:y),a?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function F(){return"undefined"!=typeof window}function H(t){return j(t)?(t.nodeName||"").toLowerCase():"#document"}function M(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function W(t){var e;return null==(e=(j(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function j(t){return!!F()&&(t instanceof Node||t instanceof M(t).Node)}function B(t){return!!F()&&(t instanceof Element||t instanceof M(t).Element)}function N(t){return!!F()&&(t instanceof HTMLElement||t instanceof M(t).HTMLElement)}function U(t){return!!F()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof M(t).ShadowRoot)}let V=new Set(["inline","contents"]);function _(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=Z(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!V.has(i)}let z=new Set(["table","td","th"]),Y=[":popover-open",":modal"];function I(t){return Y.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let $=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function G(t){let e=J(),n=B(t)?Z(t):t;return $.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||q.some(t=>(n.willChange||"").includes(t))||X.some(t=>(n.contain||"").includes(t))}function J(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let K=new Set(["html","body","#document"]);function Q(t){return K.has(H(t))}function Z(t){return M(t).getComputedStyle(t)}function tt(t){return B(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function te(t){if("html"===H(t))return t;let e=t.assignedSlot||t.parentNode||U(t)&&t.host||W(t);return U(e)?e.host:e}function tn(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=te(e);return Q(n)?e.ownerDocument?e.ownerDocument.body:e.body:N(n)&&_(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=M(i);if(o){let t=tr(l);return e.concat(l,l.visualViewport||[],_(i)?i:[],t&&n?tn(t):[])}return e.concat(i,tn(i,[],n))}function tr(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function ti(t){let e=Z(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=N(t),o=i?t.offsetWidth:n,f=i?t.offsetHeight:r,a=l(n)!==o||l(r)!==f;return a&&(n=o,r=f),{width:n,height:r,$:a}}function to(t){return B(t)?t:t.contextElement}function tl(t){let e=to(t);if(!N(e))return a(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=ti(e),f=(o?l(n.width):n.width)/r,u=(o?l(n.height):n.height)/i;return f&&Number.isFinite(f)||(f=1),u&&Number.isFinite(u)||(u=1),{x:f,y:u}}let tf=a(0);function ta(t){let e=M(t);return J()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tf}function tu(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=to(t),f=a(1);e&&(r?B(r)&&(f=tl(r)):f=tl(t));let u=(void 0===(i=n)&&(i=!1),r&&(!i||r===M(l))&&i)?ta(l):a(0),c=(o.left+u.x)/f.x,s=(o.top+u.y)/f.y,d=o.width/f.x,h=o.height/f.y;if(l){let t=M(l),e=r&&B(r)?M(r):r,n=t,i=tr(n);for(;i&&r&&e!==n;){let t=tl(i),e=i.getBoundingClientRect(),r=Z(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,s*=t.y,d*=t.x,h*=t.y,c+=o,s+=l,i=tr(n=M(i))}}return T({width:d,height:h,x:c,y:s})}function tc(t,e){let n=tt(t).scrollLeft;return e?e.left+n:tu(W(t)).left+n}function ts(t,e){let n=t.getBoundingClientRect();return{x:n.left+e.scrollLeft-tc(t,n),y:n.top+e.scrollTop}}let td=new Set(["absolute","fixed"]);function th(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=M(t),r=W(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,f=0,a=0;if(i){o=i.width,l=i.height;let t=J();(!t||t&&"fixed"===e)&&(f=i.offsetLeft,a=i.offsetTop)}let u=tc(r);if(u<=0){let t=r.ownerDocument,e=t.body,n=getComputedStyle(e),i="CSS1Compat"===t.compatMode&&parseFloat(n.marginLeft)+parseFloat(n.marginRight)||0,l=Math.abs(r.clientWidth-e.clientWidth-i);l<=25&&(o-=l)}else u<=25&&(o+=u);return{width:o,height:l,x:f,y:a}}(t,n);else if("document"===e)r=function(t){let e=W(t),n=tt(t),r=t.ownerDocument.body,i=o(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),l=o(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),f=-n.scrollLeft+tc(t),a=-n.scrollTop;return"rtl"===Z(r).direction&&(f+=o(e.clientWidth,r.clientWidth)-i),{width:i,height:l,x:f,y:a}}(W(t));else if(B(e))r=function(t,e){let n=tu(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=N(t)?tl(t):a(1),l=t.clientWidth*o.x,f=t.clientHeight*o.y;return{width:l,height:f,x:i*o.x,y:r*o.y}}(e,n);else{let n=ta(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return T(r)}function tp(t){return"static"===Z(t).position}function tm(t,e){if(!N(t)||"fixed"===Z(t).position)return null;if(e)return e(t);let n=t.offsetParent;return W(t)===n&&(n=n.ownerDocument.body),n}function tg(t,e){var n;let r=M(t);if(I(t))return r;if(!N(t)){let e=te(t);for(;e&&!Q(e);){if(B(e)&&!tp(e))return e;e=te(e)}return r}let i=tm(t,e);for(;i&&(n=i,z.has(H(n)))&&tp(i);)i=tm(i,e);return i&&Q(i)&&tp(i)&&!G(i)?r:i||function(t){let e=te(t);for(;N(e)&&!Q(e);){if(G(e))return e;if(I(e))break;e=te(e)}return null}(t)||r}let tw=async function(t){let e=this.getOffsetParent||tg,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=N(e),i=W(e),o="fixed"===n,l=tu(t,!0,o,e),f={scrollLeft:0,scrollTop:0},u=a(0);if(r||!r&&!o)if(("body"!==H(e)||_(i))&&(f=tt(e)),r){let t=tu(e,!0,o,e);u.x=t.x+e.clientLeft,u.y=t.y+e.clientTop}else i&&(u.x=tc(i));o&&!r&&i&&(u.x=tc(i));let c=!i||r||o?a(0):ts(i,f);return{x:l.left+f.scrollLeft-u.x-c.x,y:l.top+f.scrollTop-u.y-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ty={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=W(r),f=!!e&&I(e.floating);if(r===l||f&&o)return n;let u={scrollLeft:0,scrollTop:0},c=a(1),s=a(0),d=N(r);if((d||!d&&!o)&&(("body"!==H(r)||_(l))&&(u=tt(r)),N(r))){let t=tu(r);c=tl(r),s.x=t.x+r.clientLeft,s.y=t.y+r.clientTop}let h=!l||d||o?a(0):ts(l,u);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+h.x,y:n.y*c.y-u.scrollTop*c.y+s.y+h.y}},getDocumentElement:W,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:l}=t,f=[..."clippingAncestors"===n?I(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=tn(t,[],!1).filter(t=>B(t)&&"body"!==H(t)),i=null,o="fixed"===Z(t).position,l=o?te(t):t;for(;B(l)&&!Q(l);){let e=Z(l),n=G(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&td.has(i.position)||_(l)&&!n&&function t(e,n){let r=te(e);return!(r===n||!B(r)||Q(r))&&("fixed"===Z(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=te(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],a=f[0],u=f.reduce((t,n)=>{let r=th(e,n,l);return t.top=o(r.top,t.top),t.right=i(r.right,t.right),t.bottom=i(r.bottom,t.bottom),t.left=o(r.left,t.left),t},th(e,a,l));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:tg,getElementRects:tw,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=ti(t);return{width:e,height:n}},getScale:tl,isElement:B,isRTL:function(t){return"rtl"===Z(t).direction}};function tx(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function tv(t,e,n,r){let l;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,h=to(t),p=a||u?[...h?tn(h):[],...tn(e)]:[];p.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),u&&t.addEventListener("resize",n)});let m=h&&s?function(t,e){let n,r=null,l=W(t);function a(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function u(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),a();let d=t.getBoundingClientRect(),{left:h,top:p,width:m,height:g}=d;if(c||e(),!m||!g)return;let w=f(p),y=f(l.clientWidth-(h+m)),x={rootMargin:-w+"px "+-y+"px "+-f(l.clientHeight-(p+g))+"px "+-f(h)+"px",threshold:o(0,i(1,s))||1},v=!0;function b(e){let r=e[0].intersectionRatio;if(r!==s){if(!v)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||tx(d,t.getBoundingClientRect())||u(),v=!1}try{r=new IntersectionObserver(b,{...x,root:l.ownerDocument})}catch(t){r=new IntersectionObserver(b,x)}r.observe(t)}(!0),a}(h,n):null,g=-1,w=null;c&&(w=new ResizeObserver(t=>{let[r]=t;r&&r.target===h&&w&&(w.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=w)||t.observe(e)})),n()}),h&&!d&&w.observe(h),w.observe(e));let y=d?tu(t):null;return d&&function e(){let r=tu(t);y&&!tx(y,r)&&n(),y=r,l=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach(t=>{a&&t.removeEventListener("scroll",n),u&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=w)||t.disconnect(),w=null,d&&cancelAnimationFrame(l)}}let tb=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:f}=e,a=await P(e,t);return l===(null==(n=f.offset)?void 0:n.placement)&&null!=(r=f.arrow)&&r.alignmentOffset?{}:{x:i+a.x,y:o+a.y,data:{...a,placement:l}}}}},tR=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:l}=e,{mainAxis:f=!0,crossAxis:a=!1,limiter:u={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=s(t,e),h={x:n,y:r},m=await C(e,c),g=w(d(l)),y=p(g),x=h[y],v=h[g];if(f){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=x+m[t],r=x-m[e];x=o(n,i(x,r))}if(a){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=v+m[t],r=v-m[e];v=o(n,i(v,r))}let b=u.fn({...e,[y]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:f,[g]:a}}}}}},tE=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:f,middlewareData:a,rects:u,initialPlacement:c,platform:g,elements:L}=e,{mainAxis:T=!0,crossAxis:A=!0,fallbackPlacements:S,fallbackStrategy:D="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:k=!0,...P}=s(t,e);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};let F=d(f),H=w(c),M=d(c)===c,W=await (null==g.isRTL?void 0:g.isRTL(L.floating)),j=S||(M||!k?[E(c)]:function(t){let e=E(t);return[y(t),e,y(e)]}(c)),B="none"!==O;!S&&B&&j.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){switch(t){case"top":case"bottom":if(n)return e?v:x;return e?x:v;case"left":case"right":return e?b:R;default:return[]}}(d(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(y)))),o}(c,k,O,W));let N=[c,...j],U=await C(e,P),V=[],_=(null==(r=a.flip)?void 0:r.overflows)||[];if(T&&V.push(U[F]),A){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=p(w(t)),o=m(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=E(l)),[l,E(l)]}(f,u,W);V.push(U[t[0]],U[t[1]])}if(_=[..._,{placement:f,overflows:V}],!V.every(t=>t<=0)){let t=((null==(i=a.flip)?void 0:i.index)||0)+1,e=N[t];if(e&&("alignment"!==A||H===w(e)||_.every(t=>w(t.placement)!==H||t.overflows[0]>0)))return{data:{index:t,overflows:_},reset:{placement:e}};let n=null==(o=_.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(D){case"bestFit":{let t=null==(l=_.filter(t=>{if(B){let e=w(t.placement);return e===H||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=c}if(f!==n)return{reset:{placement:n}}}return{}}}},tL=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let l,f,{placement:a,rects:u,platform:c,elements:p}=e,{apply:m=()=>{},...g}=s(t,e),y=await C(e,g),x=d(a),v=h(a),b="y"===w(a),{width:R,height:E}=u.floating;"top"===x||"bottom"===x?(l=x,f=v===(await (null==c.isRTL?void 0:c.isRTL(p.floating))?"start":"end")?"left":"right"):(f=x,l="end"===v?"top":"bottom");let L=E-y.top-y.bottom,T=R-y.left-y.right,A=i(E-y[l],L),S=i(R-y[f],T),D=!e.middlewareData.shift,O=A,k=S;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=T),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(O=L),D&&!v){let t=o(y.left,0),e=o(y.right,0),n=o(y.top,0),r=o(y.bottom,0);b?k=R-2*(0!==t||0!==e?t+e:o(y.left,y.right)):O=E-2*(0!==n||0!==r?n+r:o(y.top,y.bottom))}await m({...e,availableWidth:k,availableHeight:O});let P=await c.getDimensions(p.floating);return R!==P.width||E!==P.height?{reset:{rects:!0}}:{}}}},tT=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=s(t,e);switch(r){case"referenceHidden":{let t=D(await C(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:O(t)}}}case"escaped":{let t=D(await C(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:O(t)}}}default:return{}}}}},tA=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:l,rects:f,platform:a,elements:u,middlewareData:c}=e,{element:d,padding:g=0}=s(t,e)||{};if(null==d)return{};let y=L(g),x={x:n,y:r},v=p(w(l)),b=m(v),R=await a.getDimensions(d),E="y"===v,T=E?"clientHeight":"clientWidth",A=f.reference[b]+f.reference[v]-x[v]-f.floating[b],S=x[v]-f.reference[v],C=await (null==a.getOffsetParent?void 0:a.getOffsetParent(d)),D=C?C[T]:0;D&&await (null==a.isElement?void 0:a.isElement(C))||(D=u.floating[T]||f.floating[b]);let O=D/2-R[b]/2-1,k=i(y[E?"top":"left"],O),P=i(y[E?"bottom":"right"],O),F=D-R[b]-P,H=D/2-R[b]/2+(A/2-S/2),M=o(k,i(H,F)),W=!c.arrow&&null!=h(l)&&H!==M&&f.reference[b]/2-(H<k?k:P)-R[b]/2<0,j=W?H<k?H-k:H-F:0;return{[v]:x[v]+j,data:{[v]:M,centerOffset:H-M-j,...W&&{alignmentOffset:j}},reset:W}}}),tS=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:f=0,mainAxis:a=!0,crossAxis:u=!0}=s(t,e),c={x:n,y:r},h=w(i),m=p(h),g=c[m],y=c[h],x=s(f,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(a){let t="y"===m?"height":"width",e=o.reference[m]-o.floating[t]+v.mainAxis,n=o.reference[m]+o.reference[t]-v.mainAxis;g<e?g=e:g>n&&(g=n)}if(u){var b,R;let t="y"===m?"width":"height",e=k.has(d(i)),n=o.reference[h]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[h])||0)+(e?0:v.crossAxis),r=o.reference[h]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[h])||0)-(e?v.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:g,[h]:y}}}},tC=(t,e,n)=>{let r=new Map,i={platform:ty,...n},o={...i.platform,_c:r};return S(t,e,{...i,platform:o})}},8334:(t,e,n)=>{n.d(e,{BN:()=>h,ER:()=>p,Ej:()=>g,UE:()=>y,UU:()=>m,cY:()=>d,jD:()=>w,we:()=>s});var r=n(8146),i=n(2115),o=n(7650),l="undefined"!=typeof document?i.useLayoutEffect:function(){};function f(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!f(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!f(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function a(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function u(t,e){let n=a(t);return Math.round(e*n)/n}function c(t){let e=i.useRef(t);return l(()=>{e.current=t}),e}function s(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:s=[],platform:d,elements:{reference:h,floating:p}={},transform:m=!0,whileElementsMounted:g,open:w}=t,[y,x]=i.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[v,b]=i.useState(s);f(v,s)||b(s);let[R,E]=i.useState(null),[L,T]=i.useState(null),A=i.useCallback(t=>{t!==O.current&&(O.current=t,E(t))},[]),S=i.useCallback(t=>{t!==k.current&&(k.current=t,T(t))},[]),C=h||R,D=p||L,O=i.useRef(null),k=i.useRef(null),P=i.useRef(y),F=null!=g,H=c(g),M=c(d),W=c(w),j=i.useCallback(()=>{if(!O.current||!k.current)return;let t={placement:e,strategy:n,middleware:v};M.current&&(t.platform=M.current),(0,r.rD)(O.current,k.current,t).then(t=>{let e={...t,isPositioned:!1!==W.current};B.current&&!f(P.current,e)&&(P.current=e,o.flushSync(()=>{x(e)}))})},[v,e,n,M,W]);l(()=>{!1===w&&P.current.isPositioned&&(P.current.isPositioned=!1,x(t=>({...t,isPositioned:!1})))},[w]);let B=i.useRef(!1);l(()=>(B.current=!0,()=>{B.current=!1}),[]),l(()=>{if(C&&(O.current=C),D&&(k.current=D),C&&D){if(H.current)return H.current(C,D,j);j()}},[C,D,j,H,F]);let N=i.useMemo(()=>({reference:O,floating:k,setReference:A,setFloating:S}),[A,S]),U=i.useMemo(()=>({reference:C,floating:D}),[C,D]),V=i.useMemo(()=>{let t={position:n,left:0,top:0};if(!U.floating)return t;let e=u(U.floating,y.x),r=u(U.floating,y.y);return m?{...t,transform:"translate("+e+"px, "+r+"px)",...a(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,m,U.floating,y.x,y.y]);return i.useMemo(()=>({...y,update:j,refs:N,elements:U,floatingStyles:V}),[y,j,N,U,V])}let d=(t,e)=>({...(0,r.cY)(t),options:[t,e]}),h=(t,e)=>({...(0,r.BN)(t),options:[t,e]}),p=(t,e)=>({...(0,r.ER)(t),options:[t,e]}),m=(t,e)=>({...(0,r.UU)(t),options:[t,e]}),g=(t,e)=>({...(0,r.Ej)(t),options:[t,e]}),w=(t,e)=>({...(0,r.jD)(t),options:[t,e]}),y=(t,e)=>({...(t=>({name:"arrow",options:t,fn(e){let{element:n,padding:i}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:i}).fn(e):{}:n?(0,r.UE)({element:n,padding:i}).fn(e):{}}}))(t),options:[t,e]})}}]);