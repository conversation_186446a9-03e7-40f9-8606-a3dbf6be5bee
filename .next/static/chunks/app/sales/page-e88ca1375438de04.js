(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9117],{1120:(e,s,a)=>{"use strict";a.d(s,{T:()=>i});var t=a(5155),r=a(2115),l=a(4269);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...r})});i.displayName="Textarea"},3998:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155),r=a(2115),l=a(2467),i=a(3101),n=a(4269);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:a,variant:r,size:i,asChild:d=!1,...m}=e,c=d?l.DX:"button";return(0,t.jsx)(c,{className:(0,n.cn)(o({variant:r,size:i,className:a})),ref:s,...m})});d.displayName="Button"},4269:(e,s,a)=>{"use strict";a.d(s,{cn:()=>l});var t=a(2821),r=a(5889);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}},5142:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var t=a(5155),r=a(2115),l=a(4269);let i=r.forwardRef((e,s)=>{let{className:a,type:r,...i}=e;return(0,t.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...i})});i.displayName="Input"},6061:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,2619,23)),Promise.resolve().then(a.bind(a,9379)),Promise.resolve().then(a.bind(a,8065))},6444:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var t=a(5155),r=a(2115),l=a(489),i=a(3101),n=a(4269);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.b,{ref:s,className:(0,n.cn)(o(),a),...r})});d.displayName=l.b.displayName},8065:(e,s,a)=>{"use strict";a.d(s,{default:()=>u});var t=a(5155),r=a(2115),l=a(3998),i=a(5142),n=a(6444),o=a(1120),d=a(5125),m=a(8720),c=a(5704);let u=()=>{let[e,s]=(0,r.useState)({fullName:"",businessName:"",email:"",phone:"",businessLocation:"",message:""}),[a,u]=(0,r.useState)({}),[b,h]=(0,r.useState)(!1),p=e=>{let{name:t,value:r}=e.target;s(e=>({...e,[t]:r})),a[t]&&u(e=>({...e,[t]:""}))},x=async a=>{if(a.preventDefault(),(()=>{let s={};return e.fullName.trim()||(s.fullName="Full name is required"),e.businessName.trim()||(s.businessName="Business name is required"),e.email?/\S+@\S+\.\S+/.test(e.email)||(s.email="Email is invalid"):s.email="Email is required",e.phone?/^[0-9\s\-+()]*$/.test(e.phone)||(s.phone="Please enter a valid phone number"):s.phone="Phone number is required",e.businessLocation.trim()||(s.businessLocation="Business location is required"),e.message.trim()||(s.message="Please tell us about your business"),u(s),0===Object.keys(s).length})()){h(!0);try{let a=await d.A.post("".concat(c.env.NEXT_PUBLIC_API_URL||"https://mail.shangriladistillery.com","/api/local"),e,{headers:{"Content-Type":"application/json"}});if(200===a.status)m.toast.success("Your application has been submitted successfully!"),s({fullName:"",businessName:"",email:"",phone:"",businessLocation:"",message:""});else throw Error("Failed to submit application")}catch(e){console.error("Error submitting application:",e),m.toast.error("Failed to submit application. Please try again later.")}finally{h(!1)}}};return(0,t.jsxs)("div",{className:"distillery-card p-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"Join Our Network"}),(0,t.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("form",{className:"space-y-6",onSubmit:x,children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"fullName",className:"text-amber-200",children:"Full Name"}),(0,t.jsx)(i.p,{id:"fullName",name:"fullName",type:"text",value:e.fullName,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(a.fullName?"border-red-500":""),onChange:p}),a.fullName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-400",children:a.fullName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"businessName",className:"text-amber-200",children:"Business Name"}),(0,t.jsx)(i.p,{id:"businessName",name:"businessName",type:"text",value:e.businessName,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(a.businessName?"border-red-500":""),onChange:p}),a.businessName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-400",children:a.businessName})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,t.jsx)(i.p,{id:"email",name:"email",type:"email",value:e.email,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(a.email?"border-red-500":""),onChange:p}),a.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-400",children:a.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"phone",className:"text-amber-200",children:"Phone"}),(0,t.jsx)(i.p,{id:"phone",name:"phone",type:"tel",value:e.phone,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(a.phone?"border-red-500":""),onChange:p}),a.phone&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-400",children:a.phone})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"businessLocation",className:"text-amber-200",children:"Business Location"}),(0,t.jsx)(i.p,{id:"businessLocation",name:"businessLocation",type:"text",value:e.businessLocation,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(a.businessLocation?"border-red-500":""),onChange:p}),a.businessLocation&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-400",children:a.businessLocation})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.J,{htmlFor:"message",className:"text-amber-200",children:"Tell us about your business"}),(0,t.jsx)(o.T,{id:"message",name:"message",rows:4,value:e.message,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(a.message?"border-red-500":""),onChange:p}),a.message&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-400",children:a.message})]}),(0,t.jsx)(l.$,{type:"submit",disabled:b,className:"w-full premium-button py-3 font-crimson font-semibold text-lg ".concat(b?"opacity-70 cursor-not-allowed":""),children:b?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Submitting..."]}):"Apply to Become a Dealer"})]})})]})}},9379:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var t=a(5155),r=a(2619),l=a.n(r),i=a(63),n=a(2115),o=a(5229),d=a(9540);let m=()=>{let[e,s]=(0,n.useState)(!1),a=(0,i.usePathname)(),r=[{path:"/",label:"Home"},{path:"/about",label:"Our Story"},{path:"/products",label:"Heritage Collection"},{path:"/facility",label:"Master Distillery"},{path:"/sales",label:"Sales"},{path:"/exports",label:"Exports"},{path:"/events",label:"Events & Notices"},{path:"/blog",label:"Blog"},{path:"/careers",label:"Careers"},{path:"/our-leadership",label:"Our Leadership"},{path:"/contact",label:"Visit Us"}];return(0,t.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-stone-900/95 backdrop-blur-md border-b border-amber-400/30 z-50 shadow-lg shadow-amber-500/10",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,t.jsx)(l(),{href:"/",className:"flex items-center space-x-2 group",children:(0,t.jsx)("div",{className:"relative flex items-center justify-center h-16 w-16 bg-stone-900 overflow-hidden rounded-full",children:(0,t.jsx)("img",{src:"/lovable-uploads/favicon-shangrila.png",alt:"Shangrila Distillery",className:"h-16 w-16 object-cover filter brightness-125 contrast-125 drop-shadow-lg transition-all duration-500 rounded-full"})})}),(0,t.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:r.map(e=>(0,t.jsxs)(l(),{href:e.path,className:"font-crimson font-medium transition-all duration-300 relative group ".concat(a===e.path?"text-amber-300":"text-amber-100/80 hover:text-amber-300"),children:[e.label,(0,t.jsx)("span",{className:"absolute -bottom-1 left-0 h-0.5 bg-amber-400 transition-all duration-300 ".concat(a===e.path?"w-full":"w-0 group-hover:w-full")})]},e.path))}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("button",{onClick:()=>s(!e),className:"text-amber-100 hover:text-amber-300 transition-colors p-2",children:e?(0,t.jsx)(o.A,{className:"h-6 w-6"}):(0,t.jsx)(d.A,{className:"h-6 w-6"})})})]}),e&&(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-stone-900/98 backdrop-blur-md border-t border-amber-400/30 rounded-b-lg",children:r.map(e=>(0,t.jsx)(l(),{href:e.path,onClick:()=>s(!1),className:"block px-4 py-3 font-crimson font-medium transition-all duration-300 rounded-md ".concat(a===e.path?"text-amber-300 bg-amber-400/10 border-l-4 border-amber-400":"text-amber-100/80 hover:text-amber-300 hover:bg-amber-400/5"),children:e.label},e.path))})})]})})}}},e=>{e.O(0,[4249,5707,7736,5125,8441,1255,7358],()=>e(e.s=6061)),_N_E=e.O()}]);