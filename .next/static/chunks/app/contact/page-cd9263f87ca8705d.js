(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{1120:(e,a,s)=>{"use strict";s.d(a,{T:()=>i});var r=s(5155),t=s(2115),l=s(4269);let i=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...t})});i.displayName="Textarea"},3518:(e,a,s)=>{Promise.resolve().then(s.t.bind(s,2619,23)),Promise.resolve().then(s.bind(s,5834)),Promise.resolve().then(s.bind(s,9379))},3998:(e,a,s)=>{"use strict";s.d(a,{$:()=>d});var r=s(5155),t=s(2115),l=s(2467),i=s(3101),n=s(4269);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef((e,a)=>{let{className:s,variant:t,size:i,asChild:d=!1,...c}=e,m=d?l.DX:"button";return(0,r.jsx)(m,{className:(0,n.cn)(o({variant:t,size:i,className:s})),ref:a,...c})});d.displayName="Button"},4269:(e,a,s)=>{"use strict";s.d(a,{cn:()=>l});var r=s(2821),t=s(5889);function l(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,t.QP)((0,r.$)(a))}},5142:(e,a,s)=>{"use strict";s.d(a,{p:()=>i});var r=s(5155),t=s(2115),l=s(4269);let i=t.forwardRef((e,a)=>{let{className:s,type:t,...i}=e;return(0,r.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:a,...i})});i.displayName="Input"},5834:(e,a,s)=>{"use strict";s.d(a,{default:()=>q});var r=s(5155),t=s(2115),l=s(3998),i=s(5142),n=s(6444),o=s(1120),d=s(294),c=s(4033),m=s(2108),u=s(5917),p=s(4269);let b=d.bL;d.YJ;let h=d.WT,x=t.forwardRef((e,a)=>{let{className:s,children:t,...l}=e;return(0,r.jsxs)(d.l9,{ref:a,className:(0,p.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...l,children:[t,(0,r.jsx)(d.In,{asChild:!0,children:(0,r.jsx)(c.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=d.l9.displayName;let f=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(d.PP,{ref:a,className:(0,p.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})});f.displayName=d.PP.displayName;let g=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(d.wn,{ref:a,className:(0,p.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})});g.displayName=d.wn.displayName;let y=t.forwardRef((e,a)=>{let{className:s,children:t,position:l="popper",...i}=e;return(0,r.jsx)(d.ZL,{children:(0,r.jsxs)(d.UC,{ref:a,className:(0,p.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:l,...i,children:[(0,r.jsx)(f,{}),(0,r.jsx)(d.LM,{className:(0,p.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(g,{})]})})});y.displayName=d.UC.displayName,t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(d.JU,{ref:a,className:(0,p.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...t})}).displayName=d.JU.displayName;let N=t.forwardRef((e,a)=>{let{className:s,children:t,...l}=e;return(0,r.jsxs)(d.q7,{ref:a,className:(0,p.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(d.VF,{children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})}),(0,r.jsx)(d.p4,{children:t})]})});N.displayName=d.q7.displayName,t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(d.wv,{ref:a,className:(0,p.cn)("-mx-1 my-1 h-px bg-muted",s),...t})}).displayName=d.wv.displayName;var v=s(5125),j=s(8720),w=s(5704);let q=()=>{let[e,a]=(0,t.useState)({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""}),[s,d]=(0,t.useState)(!1),[c,m]=(0,t.useState)({}),u=s=>{let{name:r,value:t}=s.target;a({...e,[r]:t}),c[r]&&m({...c,[r]:""})},p=async s=>{if(s.preventDefault(),(()=>{let a={};return e.firstName.trim()||(a.firstName="First name is required"),e.lastName.trim()||(a.lastName="Last name is required"),e.email?/\S+@\S+\.\S+/.test(e.email)||(a.email="Email is invalid"):a.email="Email is required",e.phone?/^[0-9\s\-+()]*$/.test(e.phone)||(a.phone="Please enter a valid phone number"):a.phone="Phone number is required",e.inquiryType||(a.inquiryType="Please select an inquiry type"),e.message.trim()||(a.message="Message is required"),m(a),0===Object.keys(a).length})()){d(!0);try{let s=await v.A.post("".concat(w.env.NEXT_PUBLIC_API_URL||"https://mail.shangriladistillery.com","/api/contact"),e,{headers:{"Content-Type":"application/json"}});if(200===s.status)j.toast.success("Your message has been sent successfully!"),a({firstName:"",lastName:"",email:"",phone:"",inquiryType:"",subject:"",message:""});else throw Error("Failed to send message")}catch(e){console.error("Error sending message:",e),j.toast.error("Failed to send message. Please try again later.")}finally{d(!1)}}};return(0,r.jsxs)("div",{className:"distillery-card p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-playfair font-bold text-amber-100 mb-6",children:"Send us a Message"}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:p,children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{htmlFor:"firstName",className:"text-amber-200",children:"First Name"}),(0,r.jsx)(i.p,{id:"firstName",name:"firstName",type:"text",value:e.firstName,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(c.firstName?"border-red-500":""),onChange:u}),c.firstName&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.firstName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{htmlFor:"lastName",className:"text-amber-200",children:"Last Name"}),(0,r.jsx)(i.p,{id:"lastName",name:"lastName",type:"text",value:e.lastName,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(c.lastName?"border-red-500":""),onChange:u}),c.lastName&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.lastName})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,r.jsx)(i.p,{id:"email",name:"email",type:"email",value:e.email,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(c.email?"border-red-500":""),onChange:u}),c.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.email})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{htmlFor:"phone",className:"text-amber-200",children:"Phone Number"}),(0,r.jsx)(i.p,{id:"phone",name:"phone",type:"tel",value:e.phone,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(c.phone?"border-red-500":""),onChange:u,placeholder:"+****************"}),c.phone&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.phone})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{htmlFor:"inquiryType",className:"text-amber-200",children:"Inquiry Type"}),(0,r.jsxs)(b,{value:e.inquiryType,onValueChange:s=>{a({...e,inquiryType:s}),c.inquiryType&&m({...c,inquiryType:""})},children:[(0,r.jsx)(x,{className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(c.inquiryType?"border-red-500":""),children:(0,r.jsx)(h,{placeholder:"Select inquiry type"})}),(0,r.jsxs)(y,{className:"bg-stone-800 border-amber-500/30",children:[(0,r.jsx)(N,{value:"general",children:"General Inquiry"}),(0,r.jsx)(N,{value:"wholesale",children:"Wholesale Inquiry"}),(0,r.jsx)(N,{value:"partnership",children:"Partnership"}),(0,r.jsx)(N,{value:"press",children:"Press/Media"}),(0,r.jsx)(N,{value:"other",children:"Other"})]})]}),c.inquiryType&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.inquiryType})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{htmlFor:"subject",className:"text-amber-200",children:"Subject (Optional)"}),(0,r.jsx)(i.p,{id:"subject",name:"subject",type:"text",value:e.subject,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400",onChange:u})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{htmlFor:"message",className:"text-amber-200",children:"Message"}),(0,r.jsx)(o.T,{id:"message",name:"message",rows:5,value:e.message,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(c.message?"border-red-500":""),onChange:u}),c.message&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:c.message})]})]}),(0,r.jsx)(l.$,{type:"submit",disabled:s,className:"w-full bg-amber-600 hover:bg-amber-700 text-white py-6 text-lg font-medium transition-colors duration-200 ".concat(s?"opacity-70 cursor-not-allowed":""),children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):"Send Message"})]})]})}},6444:(e,a,s)=>{"use strict";s.d(a,{J:()=>d});var r=s(5155),t=s(2115),l=s(489),i=s(3101),n=s(4269);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(l.b,{ref:a,className:(0,n.cn)(o(),s),...t})});d.displayName=l.b.displayName},9379:(e,a,s)=>{"use strict";s.d(a,{default:()=>c});var r=s(5155),t=s(2619),l=s.n(t),i=s(63),n=s(2115),o=s(5229),d=s(9540);let c=()=>{let[e,a]=(0,n.useState)(!1),s=(0,i.usePathname)(),t=[{path:"/",label:"Home"},{path:"/about",label:"Our Story"},{path:"/products",label:"Heritage Collection"},{path:"/facility",label:"Master Distillery"},{path:"/sales",label:"Sales"},{path:"/exports",label:"Exports"},{path:"/events",label:"Events & Notices"},{path:"/blog",label:"Blog"},{path:"/careers",label:"Careers"},{path:"/our-leadership",label:"Our Leadership"},{path:"/contact",label:"Visit Us"}];return(0,r.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-stone-900/95 backdrop-blur-md border-b border-amber-400/30 z-50 shadow-lg shadow-amber-500/10",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center space-x-2 group",children:(0,r.jsx)("div",{className:"relative flex items-center justify-center h-16 w-16 bg-stone-900 overflow-hidden rounded-full",children:(0,r.jsx)("img",{src:"/lovable-uploads/favicon-shangrila.png",alt:"Shangrila Distillery",className:"h-16 w-16 object-cover filter brightness-125 contrast-125 drop-shadow-lg transition-all duration-500 rounded-full"})})}),(0,r.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:t.map(e=>(0,r.jsxs)(l(),{href:e.path,className:"font-crimson font-medium transition-all duration-300 relative group ".concat(s===e.path?"text-amber-300":"text-amber-100/80 hover:text-amber-300"),children:[e.label,(0,r.jsx)("span",{className:"absolute -bottom-1 left-0 h-0.5 bg-amber-400 transition-all duration-300 ".concat(s===e.path?"w-full":"w-0 group-hover:w-full")})]},e.path))}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("button",{onClick:()=>a(!e),className:"text-amber-100 hover:text-amber-300 transition-colors p-2",children:e?(0,r.jsx)(o.A,{className:"h-6 w-6"}):(0,r.jsx)(d.A,{className:"h-6 w-6"})})})]}),e&&(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-stone-900/98 backdrop-blur-md border-t border-amber-400/30 rounded-b-lg",children:t.map(e=>(0,r.jsx)(l(),{href:e.path,onClick:()=>a(!1),className:"block px-4 py-3 font-crimson font-medium transition-all duration-300 rounded-md ".concat(s===e.path?"text-amber-300 bg-amber-400/10 border-l-4 border-amber-400":"text-amber-100/80 hover:text-amber-300 hover:bg-amber-400/5"),children:e.label},e.path))})})]})})}}},e=>{e.O(0,[4249,5707,7736,5125,8334,9099,8441,1255,7358],()=>e(e.s=3518)),_N_E=e.O()}]);