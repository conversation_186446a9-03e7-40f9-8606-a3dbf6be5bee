(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9958],{1120:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var a=r(5155),s=r(2115),n=r(4269);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});o.displayName="Textarea"},3998:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var a=r(5155),s=r(2115),n=r(2467),o=r(3101),l=r(4269);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:c=!1,...d}=e,m=c?n.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(i({variant:s,size:o,className:r})),ref:t,...d})});c.displayName="Button"},4269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2821),s=r(5889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},5142:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var a=r(5155),s=r(2115),n=r(4269);let o=s.forwardRef((e,t)=>{let{className:r,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...o})});o.displayName="Input"},6444:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var a=r(5155),s=r(2115),n=r(489),o=r(3101),l=r(4269);let i=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,l.cn)(i(),r),...s})});c.displayName=n.b.displayName},7116:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(5155),s=r(2115),n=r(3998),o=r(5142),l=r(6444),i=r(1120),c=r(5125),d=r(8720),m=r(5704);let u=()=>{let[e,t]=(0,s.useState)({companyName:"",contactPerson:"",email:"",country:"",expectedAnualVolume:"",distributionExp:""}),[r,u]=(0,s.useState)({}),[b,p]=(0,s.useState)(!1),x=e=>{let{name:a,value:s}=e.target;t(e=>({...e,[a]:s})),r[a]&&u(e=>({...e,[a]:""}))},h=async r=>{if(r.preventDefault(),(()=>{let t={};return e.companyName.trim()||(t.companyName="Company name is required"),e.contactPerson.trim()||(t.contactPerson="Contact person is required"),e.email?/\S+@\S+\.\S+/.test(e.email)||(t.email="Email is invalid"):t.email="Email is required",e.country.trim()||(t.country="Country is required"),e.expectedAnualVolume.trim()||(t.expectedAnualVolume="Expected annual volume is required"),e.distributionExp.trim()||(t.distributionExp="Distribution experience is required"),u(t),0===Object.keys(t).length})()){p(!0);try{let r=await c.A.post("".concat(m.env.NEXT_PUBLIC_API_URL||"http://localhost:8000","/api/global"),e,{headers:{"Content-Type":"application/json"}});if(200===r.status)d.toast.success("Your inquiry has been sent successfully!"),t({companyName:"",contactPerson:"",email:"",country:"",expectedAnualVolume:"",distributionExp:""});else throw Error("Failed to send message")}catch(e){var a,s;console.error("Error sending message:",e),d.toast.error((null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Failed to send message. Please try again later.")}finally{p(!1)}}};return(0,a.jsxs)("div",{className:"distillery-card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-playfair font-bold text-amber-100 mb-8 text-center",children:"B2B Partnership Inquiry"}),(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("form",{className:"space-y-6",onSubmit:h,children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l.J,{htmlFor:"companyName",className:"text-amber-200",children:"Company Name"}),(0,a.jsx)(o.p,{id:"companyName",name:"companyName",type:"text",value:e.companyName,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(r.companyName?"border-red-500":""),onChange:x}),r.companyName&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.companyName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(l.J,{htmlFor:"contactPerson",className:"text-amber-200",children:"Contact Person"}),(0,a.jsx)(o.p,{id:"contactPerson",name:"contactPerson",type:"text",value:e.contactPerson,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(r.contactPerson?"border-red-500":""),onChange:x}),r.contactPerson&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.contactPerson})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l.J,{htmlFor:"email",className:"text-amber-200",children:"Email"}),(0,a.jsx)(o.p,{id:"email",name:"email",type:"email",value:e.email,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(r.email?"border-red-500":""),onChange:x}),r.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(l.J,{htmlFor:"country",className:"text-amber-200",children:"Country/Region"}),(0,a.jsx)(o.p,{id:"country",name:"country",type:"text",value:e.country,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(r.country?"border-red-500":""),onChange:x}),r.country&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.country})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(l.J,{htmlFor:"expectedAnualVolume",className:"text-amber-200",children:"Expected Annual Volume"}),(0,a.jsx)(o.p,{id:"expectedAnualVolume",name:"expectedAnualVolume",type:"text",value:e.expectedAnualVolume,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(r.expectedAnualVolume?"border-red-500":""),onChange:x,placeholder:"e.g., 10,000 bottles"}),r.expectedAnualVolume&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.expectedAnualVolume})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(l.J,{htmlFor:"distributionExp",className:"text-amber-200",children:"Distribution Experience"}),(0,a.jsx)(i.T,{id:"distributionExp",name:"distributionExp",rows:4,value:e.distributionExp,className:"mt-1 bg-stone-800/50 border-amber-500/30 text-amber-100 focus:border-amber-400 ".concat(r.distributionExp?"border-red-500":""),onChange:x,placeholder:"Tell us about your distribution network and experience with premium spirits..."}),r.distributionExp&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r.distributionExp})]}),(0,a.jsx)(n.$,{type:"submit",disabled:b,className:"w-full premium-button py-3 font-crimson font-semibold ".concat(b?"opacity-70 cursor-not-allowed":""),children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):"Submit Partnership Inquiry"})]})})]})}},9379:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(5155),s=r(2619),n=r.n(s),o=r(63),l=r(2115),i=r(5229),c=r(9540);let d=()=>{let[e,t]=(0,l.useState)(!1),r=(0,o.usePathname)(),s=[{path:"/",label:"Home"},{path:"/about",label:"Our Story"},{path:"/products",label:"Heritage Collection"},{path:"/facility",label:"Master Distillery"},{path:"/sales",label:"Sales"},{path:"/exports",label:"Exports"},{path:"/events",label:"Events & Notices"},{path:"/blog",label:"Blog"},{path:"/careers",label:"Careers"},{path:"/our-leadership",label:"Our Leadership"},{path:"/contact",label:"Visit Us"}];return(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-stone-900/95 backdrop-blur-md border-b border-amber-400/30 z-50 shadow-lg shadow-amber-500/10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-20",children:[(0,a.jsx)(n(),{href:"/",className:"flex items-center space-x-2 group",children:(0,a.jsx)("div",{className:"relative flex items-center justify-center h-16 w-16 bg-stone-900 overflow-hidden rounded-full",children:(0,a.jsx)("img",{src:"/lovable-uploads/favicon-shangrila.png",alt:"Shangrila Distillery",className:"h-16 w-16 object-cover filter brightness-125 contrast-125 drop-shadow-lg transition-all duration-500 rounded-full"})})}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:s.map(e=>(0,a.jsxs)(n(),{href:e.path,className:"font-crimson font-medium transition-all duration-300 relative group ".concat(r===e.path?"text-amber-300":"text-amber-100/80 hover:text-amber-300"),children:[e.label,(0,a.jsx)("span",{className:"absolute -bottom-1 left-0 h-0.5 bg-amber-400 transition-all duration-300 ".concat(r===e.path?"w-full":"w-0 group-hover:w-full")})]},e.path))}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("button",{onClick:()=>t(!e),className:"text-amber-100 hover:text-amber-300 transition-colors p-2",children:e?(0,a.jsx)(i.A,{className:"h-6 w-6"}):(0,a.jsx)(c.A,{className:"h-6 w-6"})})})]}),e&&(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-stone-900/98 backdrop-blur-md border-t border-amber-400/30 rounded-b-lg",children:s.map(e=>(0,a.jsx)(n(),{href:e.path,onClick:()=>t(!1),className:"block px-4 py-3 font-crimson font-medium transition-all duration-300 rounded-md ".concat(r===e.path?"text-amber-300 bg-amber-400/10 border-l-4 border-amber-400":"text-amber-100/80 hover:text-amber-300 hover:bg-amber-400/5"),children:e.label},e.path))})})]})})}},9636:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2619,23)),Promise.resolve().then(r.bind(r,7116)),Promise.resolve().then(r.bind(r,9379))}},e=>{e.O(0,[4249,5707,7736,5125,8441,1255,7358],()=>e(e.s=9636)),_N_E=e.O()}]);