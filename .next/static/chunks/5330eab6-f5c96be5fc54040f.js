"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3796],{8116:(t,e,a)=>{var n,r;function i(){return(i=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(t[n]=a[n])}return t}).apply(this,arguments)}function s(t,e){if(!1===t||null==t)throw Error(e)}function h(t){let{pathname:e="/",search:a="",hash:n=""}=t;return a&&"?"!==a&&(e+="?"===a.charAt(0)?a:"?"+a),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function l(t){let e={};if(t){let a=t.indexOf("#");a>=0&&(e.hash=t.substr(a),t=t.substr(0,a));let n=t.indexOf("?");n>=0&&(e.search=t.substr(n),t=t.substr(0,n)),t&&(e.pathname=t)}return e}function o(t,e){if("/"===e)return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let a=e.endsWith("/")?e.length-1:e.length,n=t.charAt(a);return n&&"/"!==n?null:t.slice(a)||"/"}function u(t,e,a,n){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field [")+JSON.stringify(n)+"].  Please separate it out to the `to."+a+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function p(t,e){let a=t.filter((t,e)=>0===e||t.route.path&&t.route.path.length>0);return e?a.map((t,e)=>e===a.length-1?t.pathname:t.pathnameBase):a.map(t=>t.pathnameBase)}function c(t,e,a,n){let r,h;void 0===n&&(n=!1),"string"==typeof t?r=l(t):(s(!(r=i({},t)).pathname||!r.pathname.includes("?"),u("?","pathname","search",r)),s(!r.pathname||!r.pathname.includes("#"),u("#","pathname","hash",r)),s(!r.search||!r.search.includes("#"),u("#","search","hash",r)));let o=""===t||""===r.pathname,p=o?"/":r.pathname;if(null==p)h=a;else{let t=e.length-1;if(!n&&p.startsWith("..")){let e=p.split("/");for(;".."===e[0];)e.shift(),t-=1;r.pathname=e.join("/")}h=t>=0?e[t]:"/"}let c=function(t,e){var a;let n;void 0===e&&(e="/");let{pathname:r,search:i="",hash:s=""}="string"==typeof t?l(t):t;return{pathname:r?r.startsWith("/")?r:(a=r,n=e.replace(/\/+$/,"").split("/"),a.split("/").forEach(t=>{".."===t?n.length>1&&n.pop():"."!==t&&n.push(t)}),n.length>1?n.join("/"):"/"):e,search:d(i),hash:m(s)}}(r,h),f=p&&"/"!==p&&p.endsWith("/"),g=(o||"."===p)&&a.endsWith("/");return!c.pathname.endsWith("/")&&(f||g)&&(c.pathname+="/"),c}a.d(e,{AO:()=>h,Gh:()=>c,HS:()=>f,Oi:()=>s,pb:()=>o,tH:()=>g,yD:()=>p}),function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"}(n||(n={})),function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"}(r||(r={}));let f=t=>t.join("/").replace(/\/\/+/g,"/"),d=t=>t&&"?"!==t?t.startsWith("?")?t:"?"+t:"",m=t=>t&&"#"!==t?t.startsWith("#")?t:"#"+t:"";class g extends Error{}Symbol("deferred")}}]);