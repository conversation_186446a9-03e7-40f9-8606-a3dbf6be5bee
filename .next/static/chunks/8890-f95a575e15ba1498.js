"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8890],{458:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9065).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1215:(e,t,n)=>{n.d(t,{UC:()=>rw,YJ:()=>rb,In:()=>ry,q7:()=>rS,VF:()=>rR,p4:()=>rC,JU:()=>rE,ZL:()=>rg,bL:()=>rh,wn:()=>rk,PP:()=>rA,wv:()=>rP,l9:()=>rm,WT:()=>rv,LM:()=>rx});var r,o,i,l=n(4232),a=n.t(l,2),u=n(8477);function s(e,[t,n]){return Math.min(n,Math.max(t,e))}function c(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var f=n(7876);function d(e,t=[]){let n=[],r=()=>{let t=n.map(e=>l.createContext(e));return function(n){let r=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=l.createContext(r),i=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[i]||o,s=l.useMemo(()=>a,Object.values(a));return(0,f.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[i]||o,s=l.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function p(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function h(...e){return t=>{let n=!1,r=e.map(e=>{let r=p(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():p(e[t],null)}}}}function m(...e){return l.useCallback(h(...e),e)}function v(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...r}=e;if(l.isValidElement(n)){var o;let e,i,a=(o=n,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==l.Fragment&&(u.ref=t?h(t,a):a),l.cloneElement(n,u)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:r,...o}=e,i=l.Children.toArray(r),a=i.find(g);if(a){let e=a.props.children,r=i.map(t=>t!==a?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,f.jsx)(t,{...o,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,f.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var y=Symbol("radix.slottable");function g(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var w=new WeakMap;function x(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=b(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function b(e){return e!=e||0===e?0:Math.trunc(e)}(class e extends Map{#e;constructor(e){super(e),this.#e=[...super.keys()],w.set(this,!0)}set(e,t){return w.get(this)&&(this.has(e)?this.#e[this.#e.indexOf(e)]=e:this.#e.push(e)),super.set(e,t),this}insert(e,t,n){let r,o=this.has(t),i=this.#e.length,l=b(e),a=l>=0?l:i+l,u=a<0||a>=i?-1:a;if(u===this.size||o&&u===this.size-1||-1===u)return this.set(t,n),this;let s=this.size+ +!o;l<0&&a++;let c=[...this.#e],f=!1;for(let e=a;e<s;e++)if(a===e){let i=c[e];c[e]===t&&(i=c[e+1]),o&&this.delete(t),r=this.get(i),this.set(t,n)}else{f||c[e-1]!==t||(f=!0);let n=c[f?e:e-1],o=r;r=this.get(n),this.delete(n),this.set(n,o)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=this.#e.indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=this.#e.indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=this.#e.indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=this.#e.indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#e=[],super.clear()}delete(e){let t=super.delete(e);return t&&this.#e.splice(this.#e.indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=x(this.#e,e);if(void 0!==t)return this.get(t)}entryAt(e){let t=x(this.#e,e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return this.#e.indexOf(e)}keyAt(e){return x(this.#e,e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(...e){let[t,n]=e,r=0,o=n??this.at(0);for(let n of this)o=0===r&&1===e.length?n:Reflect.apply(t,this,[o,n,r,this]),r++;return o}reduceRight(...e){let[t,n]=e,r=n??this.at(-1);for(let n=this.size-1;n>=0;n--){let o=this.at(n);r=n===this.size-1&&1===e.length?o:Reflect.apply(t,this,[r,o,n,this])}return r}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(...t){let n=[...this.entries()];return n.splice(...t),new e(n)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}});var E=l.createContext(void 0),S=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=v(`Primitive.${t}`),r=l.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,f.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function C(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var R="dismissableLayer.update",A=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),k=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:u,onDismiss:s,...d}=e,p=l.useContext(A),[h,v]=l.useState(null),y=h?.ownerDocument??globalThis?.document,[,g]=l.useState({}),w=m(t,e=>v(e)),x=Array.from(p.layers),[b]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),E=x.indexOf(b),k=h?x.indexOf(h):-1,L=p.layersWithOutsidePointerEventsDisabled.size>0,j=k>=E,N=function(e,t=globalThis?.document){let n=C(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){T("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));j&&!n&&(i?.(e),u?.(e),e.defaultPrevented||s?.())},y),O=function(e,t=globalThis?.document){let n=C(e),r=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!r.current&&T("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...p.branches].some(e=>e.contains(t))&&(a?.(e),u?.(e),e.defaultPrevented||s?.())},y);return!function(e,t=globalThis?.document){let n=function(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k===p.layers.size-1&&(r?.(e),!e.defaultPrevented&&s&&(e.preventDefault(),s()))},y),l.useEffect(()=>{if(h)return n&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(o=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(h)),p.layers.add(h),P(),()=>{n&&1===p.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=o)}},[h,y,n,p]),l.useEffect(()=>()=>{h&&(p.layers.delete(h),p.layersWithOutsidePointerEventsDisabled.delete(h),P())},[h,p]),l.useEffect(()=>{let e=()=>g({});return document.addEventListener(R,e),()=>document.removeEventListener(R,e)},[]),(0,f.jsx)(S.div,{...d,ref:w,style:{pointerEvents:L?j?"auto":"none":void 0,...e.style},onFocusCapture:c(e.onFocusCapture,O.onFocusCapture),onBlurCapture:c(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:c(e.onPointerDownCapture,N.onPointerDownCapture)})});function P(){let e=new CustomEvent(R);document.dispatchEvent(e)}function T(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&o.addEventListener(e,t,{once:!0}),r)o&&u.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}k.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(A),r=l.useRef(null),o=m(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,f.jsx)(S.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var L=0;function j(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}function N(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function O(...e){return t=>{let n=!1,r=e.map(e=>{let r=N(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():N(e[t],null)}}}}var M=Symbol("radix.slottable");function D(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===M}var I=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...r}=e;if(l.isValidElement(n)){var o;let e,i,a=(o=n,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==l.Fragment&&(u.ref=t?O(t,a):a),l.cloneElement(n,u)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:r,...o}=e,i=l.Children.toArray(r),a=i.find(D);if(a){let e=a.props.children,r=i.map(t=>t!==a?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,f.jsx)(t,{...o,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,f.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),r=l.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,f.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function W(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var F="focusScope.autoFocusOnMount",_="focusScope.autoFocusOnUnmount",H={bubbles:!1,cancelable:!0},B=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[u,s]=l.useState(null),c=W(o),d=W(i),p=l.useRef(null),h=function(...e){return l.useCallback(O(...e),e)}(t,e=>s(e)),m=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(m.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:$(p.current,{select:!0})},t=function(e){if(m.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||$(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&$(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,m.paused]),l.useEffect(()=>{if(u){K.add(m);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(F,H);u.addEventListener(F,c),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if($(r,{select:t}),document.activeElement!==n)return}(z(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&$(u))}return()=>{u.removeEventListener(F,c),setTimeout(()=>{let t=new CustomEvent(_,H);u.addEventListener(_,d),u.dispatchEvent(t),t.defaultPrevented||$(e??document.body,{select:!0}),u.removeEventListener(_,d),K.remove(m)},0)}}},[u,c,d,m]);let v=l.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=z(e);return[V(t,e),V(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&$(i,{select:!0})):(e.preventDefault(),n&&$(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,m.paused]);return(0,f.jsx)(I.div,{tabIndex:-1,...a,ref:h,onKeyDown:v})});function z(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function V(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function $(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}B.displayName="FocusScope";var K=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=Y(e,t)).unshift(t)},remove(t){e=Y(e,t),e[0]?.resume()}}}();function Y(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var U=globalThis?.document?l.useLayoutEffect:()=>{},X=a[" useId ".trim().toString()]||(()=>void 0),q=0;function Z(e){let[t,n]=l.useState(X());return U(()=>{e||n(e=>e??String(q++))},[e]),e||(t?`radix-${t}`:"")}let G=["top","right","bottom","left"],J=Math.min,Q=Math.max,ee=Math.round,et=Math.floor,en=e=>({x:e,y:e}),er={left:"right",right:"left",bottom:"top",top:"bottom"},eo={start:"end",end:"start"};function ei(e,t){return"function"==typeof e?e(t):e}function el(e){return e.split("-")[0]}function ea(e){return e.split("-")[1]}function eu(e){return"x"===e?"y":"x"}function es(e){return"y"===e?"height":"width"}let ec=new Set(["top","bottom"]);function ef(e){return ec.has(el(e))?"y":"x"}function ed(e){return e.replace(/start|end/g,e=>eo[e])}let ep=["left","right"],eh=["right","left"],em=["top","bottom"],ev=["bottom","top"];function ey(e){return e.replace(/left|right|bottom|top/g,e=>er[e])}function eg(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ew(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ex(e,t,n){let r,{reference:o,floating:i}=e,l=ef(t),a=eu(ef(t)),u=es(a),s=el(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(s){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(ea(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let eb=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=ex(s,r,u),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:y,data:g,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,f=null!=y?y:f,p={...p,[i]:{...p[i],...g}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:f}=ex(s,d,u)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function eE(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=ei(t,e),h=eg(p),m=a[d?"floating"===f?"reference":"floating":f],v=ew(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),y="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,g=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(g))&&await (null==i.getScale?void 0:i.getScale(g))||{x:1,y:1},x=ew(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:g,strategy:u}):y);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function eS(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eC(e){return G.some(t=>e[t]>=0)}let eR=new Set(["left","top"]);async function eA(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=el(n),a=ea(n),u="y"===ef(n),s=eR.has(l)?-1:1,c=i&&u?-1:1,f=ei(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*c,y:d*s}:{x:d*s,y:p*c}}function ek(){return"undefined"!=typeof window}function eP(e){return ej(e)?(e.nodeName||"").toLowerCase():"#document"}function eT(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eL(e){var t;return null==(t=(ej(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ej(e){return!!ek()&&(e instanceof Node||e instanceof eT(e).Node)}function eN(e){return!!ek()&&(e instanceof Element||e instanceof eT(e).Element)}function eO(e){return!!ek()&&(e instanceof HTMLElement||e instanceof eT(e).HTMLElement)}function eM(e){return!!ek()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eT(e).ShadowRoot)}let eD=new Set(["inline","contents"]);function eI(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eU(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!eD.has(o)}let eW=new Set(["table","td","th"]),eF=[":popover-open",":modal"];function e_(e){return eF.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eH=["transform","translate","scale","rotate","perspective"],eB=["transform","translate","scale","rotate","perspective","filter"],ez=["paint","layout","strict","content"];function eV(e){let t=e$(),n=eN(e)?eU(e):e;return eH.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eB.some(e=>(n.willChange||"").includes(e))||ez.some(e=>(n.contain||"").includes(e))}function e$(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eK=new Set(["html","body","#document"]);function eY(e){return eK.has(eP(e))}function eU(e){return eT(e).getComputedStyle(e)}function eX(e){return eN(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eq(e){if("html"===eP(e))return e;let t=e.assignedSlot||e.parentNode||eM(e)&&e.host||eL(e);return eM(t)?t.host:t}function eZ(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eq(t);return eY(n)?t.ownerDocument?t.ownerDocument.body:t.body:eO(n)&&eI(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=eT(o);if(i){let e=eG(l);return t.concat(l,l.visualViewport||[],eI(o)?o:[],e&&n?eZ(e):[])}return t.concat(o,eZ(o,[],n))}function eG(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eJ(e){let t=eU(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eO(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=ee(n)!==i||ee(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eQ(e){return eN(e)?e:e.contextElement}function e0(e){let t=eQ(e);if(!eO(t))return en(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eJ(t),l=(i?ee(n.width):n.width)/r,a=(i?ee(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let e1=en(0);function e2(e){let t=eT(e);return e$()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:e1}function e5(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eQ(e),a=en(1);t&&(r?eN(r)&&(a=e0(r)):a=e0(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===eT(l))&&o)?e2(l):en(0),s=(i.left+u.x)/a.x,c=(i.top+u.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=eT(l),t=r&&eN(r)?eT(r):r,n=e,o=eG(n);for(;o&&r&&t!==n;){let e=e0(o),t=o.getBoundingClientRect(),r=eU(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,f*=e.x,d*=e.y,s+=i,c+=l,o=eG(n=eT(o))}}return ew({width:f,height:d,x:s,y:c})}function e6(e,t){let n=eX(e).scrollLeft;return t?t.left+n:e5(eL(e)).left+n}function e9(e,t){let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-e6(e,n),y:n.top+t.scrollTop}}let e3=new Set(["absolute","fixed"]);function e7(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eT(e),r=eL(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=e$();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}let s=e6(r);if(s<=0){let e=r.ownerDocument,t=e.body,n=getComputedStyle(t),o="CSS1Compat"===e.compatMode&&parseFloat(n.marginLeft)+parseFloat(n.marginRight)||0,l=Math.abs(r.clientWidth-t.clientWidth-o);l<=25&&(i-=l)}else s<=25&&(i+=s);return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eL(e),n=eX(e),r=e.ownerDocument.body,o=Q(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Q(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+e6(e),a=-n.scrollTop;return"rtl"===eU(r).direction&&(l+=Q(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eL(e));else if(eN(t))r=function(e,t){let n=e5(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eO(e)?e0(e):en(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=e2(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ew(r)}function e8(e){return"static"===eU(e).position}function e4(e,t){if(!eO(e)||"fixed"===eU(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eL(e)===n&&(n=n.ownerDocument.body),n}function te(e,t){var n;let r=eT(e);if(e_(e))return r;if(!eO(e)){let t=eq(e);for(;t&&!eY(t);){if(eN(t)&&!e8(t))return t;t=eq(t)}return r}let o=e4(e,t);for(;o&&(n=o,eW.has(eP(n)))&&e8(o);)o=e4(o,t);return o&&eY(o)&&e8(o)&&!eV(o)?r:o||function(e){let t=eq(e);for(;eO(t)&&!eY(t);){if(eV(t))return t;if(e_(t))break;t=eq(t)}return null}(e)||r}let tt=async function(e){let t=this.getOffsetParent||te,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eO(t),o=eL(t),i="fixed"===n,l=e5(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=en(0);if(r||!r&&!i)if(("body"!==eP(t)||eI(o))&&(a=eX(t)),r){let e=e5(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=e6(o));i&&!r&&o&&(u.x=e6(o));let s=!o||r||i?en(0):e9(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},tn={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eL(r),a=!!t&&e_(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=en(1),c=en(0),f=eO(r);if((f||!f&&!i)&&(("body"!==eP(r)||eI(l))&&(u=eX(r)),eO(r))){let e=e5(r);s=e0(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!l||f||i?en(0):e9(l,u);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+d.x,y:n.y*s.y-u.scrollTop*s.y+c.y+d.y}},getDocumentElement:eL,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?e_(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eZ(e,[],!1).filter(e=>eN(e)&&"body"!==eP(e)),o=null,i="fixed"===eU(e).position,l=i?eq(e):e;for(;eN(l)&&!eY(l);){let t=eU(l),n=eV(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&e3.has(o.position)||eI(l)&&!n&&function e(t,n){let r=eq(t);return!(r===n||!eN(r)||eY(r))&&("fixed"===eU(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eq(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=e7(t,n,o);return e.top=Q(r.top,e.top),e.right=J(r.right,e.right),e.bottom=J(r.bottom,e.bottom),e.left=Q(r.left,e.left),e},e7(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:te,getElementRects:tt,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eJ(e);return{width:t,height:n}},getScale:e0,isElement:eN,isRTL:function(e){return"rtl"===eU(e).direction}};function tr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let to=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:s,padding:c=0}=ei(e,t)||{};if(null==s)return{};let f=eg(c),d={x:n,y:r},p=eu(ef(o)),h=es(p),m=await l.getDimensions(s),v="y"===p,y=v?"clientHeight":"clientWidth",g=i.reference[h]+i.reference[p]-d[p]-i.floating[h],w=d[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),b=x?x[y]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[y]||i.floating[h]);let E=b/2-m[h]/2-1,S=J(f[v?"top":"left"],E),C=J(f[v?"bottom":"right"],E),R=b-m[h]-C,A=b/2-m[h]/2+(g/2-w/2),k=Q(S,J(A,R)),P=!u.arrow&&null!=ea(o)&&A!==k&&i.reference[h]/2-(A<S?S:C)-m[h]/2<0,T=P?A<S?A-S:A-R:0;return{[p]:d[p]+T,data:{[p]:k,centerOffset:A-k-T,...P&&{alignmentOffset:T}},reset:P}}});var ti="undefined"!=typeof document?l.useLayoutEffect:function(){};function tl(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!tl(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!tl(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ta(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tu(e,t){let n=ta(e);return Math.round(t*n)/n}function ts(e){let t=l.useRef(e);return ti(()=>{t.current=e}),t}var tc=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,f.jsx)(S.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,f.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tc.displayName="Arrow";var tf="Popper",[td,tp]=d(tf),[th,tm]=td(tf),tv=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,f.jsx)(th,{scope:t,anchor:r,onAnchorChange:o,children:n})};tv.displayName=tf;var ty="PopperAnchor",tg=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tm(ty,n),a=l.useRef(null),u=m(t,a);return l.useEffect(()=>{i.onAnchorChange(r?.current||a.current)}),r?null:(0,f.jsx)(S.div,{...o,ref:u})});tg.displayName=ty;var tw="PopperContent",[tx,tb]=td(tw),tE=l.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:a=0,arrowPadding:s=0,avoidCollisions:c=!0,collisionBoundary:d=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:y="optimized",onPlaced:g,...w}=e,x=tm(tw,n),[b,E]=l.useState(null),R=m(t,e=>E(e)),[A,k]=l.useState(null),P=function(e){let[t,n]=l.useState(void 0);return U(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(A),T=P?.width??0,L=P?.height??0,j="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},N=Array.isArray(d)?d:[d],O=N.length>0,M={padding:j,boundary:N.filter(tA),altBoundary:O},{refs:D,floatingStyles:I,placement:W,isPositioned:F,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:c,open:f}=e,[d,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=l.useState(r);tl(h,r)||m(r);let[v,y]=l.useState(null),[g,w]=l.useState(null),x=l.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),b=l.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||v,S=a||g,C=l.useRef(null),R=l.useRef(null),A=l.useRef(d),k=null!=c,P=ts(c),T=ts(o),L=ts(f),j=l.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};T.current&&(e.platform=T.current),((e,t,n)=>{let r=new Map,o={platform:tn,...n},i={...o.platform,_c:r};return eb(e,t,{...o,platform:i})})(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};N.current&&!tl(A.current,t)&&(A.current=t,u.flushSync(()=>{p(t)}))})},[h,t,n,T,L]);ti(()=>{!1===f&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[f]);let N=l.useRef(!1);ti(()=>(N.current=!0,()=>{N.current=!1}),[]),ti(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(P.current)return P.current(E,S,j);j()}},[E,S,j,P,k]);let O=l.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),M=l.useMemo(()=>({reference:E,floating:S}),[E,S]),D=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=tu(M.floating,d.x),r=tu(M.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ta(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,M.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:j,refs:O,elements:M,floatingStyles:D}),[d,j,O,M,D])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,c=eQ(e),f=i||l?[...c?eZ(c):[],...eZ(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let d=c&&u?function(e,t){let n,r=null,o=eL(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let s=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=s;if(a||t(),!d||!p)return;let h=et(f),m=et(o.clientWidth-(c+d)),v={rootMargin:-h+"px "+-m+"px "+-et(o.clientHeight-(f+p))+"px "+-et(c)+"px",threshold:Q(0,J(1,u))||1},y=!0;function g(t){let r=t[0].intersectionRatio;if(r!==u){if(!y)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||tr(s,e.getBoundingClientRect())||l(),y=!1}try{r=new IntersectionObserver(g,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(g,v)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!s&&h.observe(c),h.observe(t));let m=s?e5(e):null;return s&&function t(){let r=e5(e);m&&!tr(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,s&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:x.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await eA(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:o+L,alignmentAxis:a}),c&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=ei(e,t),s={x:n,y:r},c=await eE(t,u),f=ef(el(o)),d=eu(f),p=s[d],h=s[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=Q(n,J(p,r))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+c[e],r=h-c[t];h=Q(n,J(h,r))}let m=a.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:l}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=ei(e,t),c={x:n,y:r},f=ef(o),d=eu(f),p=c[d],h=c[f],m=ei(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+v.mainAxis,n=i.reference[d]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(s){var y,g;let e="y"===d?"width":"height",t=eR.has(el(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(y=l.offset)?void 0:y[f])||0)+(t?0:v.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(g=l.offset)?void 0:g[f])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}))():void 0,...M}),c&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:g=!0,...w}=ei(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=el(a),b=ef(c),E=el(c)===c,S=await (null==f.isRTL?void 0:f.isRTL(d.floating)),C=m||(E||!g?[ey(c)]:function(e){let t=ey(e);return[ed(e),t,ed(t)]}(c)),R="none"!==y;!m&&R&&C.push(...function(e,t,n,r){let o=ea(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?eh:ep;return t?ep:eh;case"left":case"right":return t?em:ev;default:return[]}}(el(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(ed)))),i}(c,g,y,S));let A=[c,...C],k=await eE(t,w),P=[],T=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&P.push(k[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=ea(e),o=eu(ef(e)),i=es(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ey(l)),[l,ey(l)]}(a,s,S);P.push(k[e[0]],k[e[1]])}if(T=[...T,{placement:a,overflows:P}],!P.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=A[e];if(t&&("alignment"!==h||b===ef(t)||T.every(e=>ef(e.placement)!==b||e.overflows[0]>0)))return{data:{index:e,overflows:T},reset:{placement:t}};let n=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=T.filter(e=>{if(R){let t=ef(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...M}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:s}=t,{apply:c=()=>{},...f}=ei(e,t),d=await eE(t,f),p=el(l),h=ea(l),m="y"===ef(l),{width:v,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let g=y-d.top-d.bottom,w=v-d.left-d.right,x=J(y-d[o],g),b=J(v-d[i],w),E=!t.middlewareData.shift,S=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=g),E&&!h){let e=Q(d.left,0),t=Q(d.right,0),n=Q(d.top,0),r=Q(d.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:Q(d.left,d.right)):S=y-2*(0!==n||0!==r?n+r:Q(d.top,d.bottom))}await c({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(s.floating);return v!==R.width||y!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...M,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),A&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?to({element:n.current,padding:r}).fn(t):{}:n?to({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:A,padding:s}),tk({arrowWidth:T,arrowHeight:L}),v&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=ei(e,t);switch(r){case"referenceHidden":{let e=eS(await eE(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:eC(e)}}}case"escaped":{let e=eS(await eE(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:eC(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...M})]}),[H,B]=tP(W),z=C(g);U(()=>{F&&z?.()},[F,z]);let V=_.arrow?.x,$=_.arrow?.y,K=_.arrow?.centerOffset!==0,[Y,X]=l.useState();return U(()=>{b&&X(window.getComputedStyle(b).zIndex)},[b]),(0,f.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:F?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,f.jsx)(tx,{scope:n,placedSide:H,onArrowChange:k,arrowX:V,arrowY:$,shouldHideArrow:K,children:(0,f.jsx)(S.div,{"data-side":H,"data-align":B,...w,ref:R,style:{...w.style,animation:F?void 0:"none"}})})})});tE.displayName=tw;var tS="PopperArrow",tC={top:"bottom",right:"left",bottom:"top",left:"right"},tR=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tb(tS,n),i=tC[o.placedSide];return(0,f.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,f.jsx)(tc,{...r,ref:t,style:{...r.style,display:"block"}})})});function tA(e){return null!==e}tR.displayName=tS;var tk=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,s]=tP(n),c={start:"0%",center:"50%",end:"100%"}[s],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?c:`${f}px`,h=`${-a}px`):"top"===u?(p=i?c:`${f}px`,h=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?c:`${d}px`):"left"===u&&(p=`${r.floating.width+a}px`,h=i?c:`${d}px`),{data:{x:p,y:h}}}});function tP(e){let[t,n="center"]=e.split("-");return[t,n]}function tT(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var tL=Symbol("radix.slottable");function tj(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===tL}var tN=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...r}=e;if(l.isValidElement(n)){var o;let e,i,a=(o=n,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==l.Fragment&&(u.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=tT(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():tT(e[t],null)}}}}(t,a):a),l.cloneElement(n,u)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:r,...o}=e,i=l.Children.toArray(r),a=i.find(tj);if(a){let e=a.props.children,r=i.map(t=>t!==a?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,f.jsx)(t,{...o,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,f.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),r=l.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,f.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),tO=globalThis?.document?l.useLayoutEffect:()=>{},tM=l.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=l.useState(!1);tO(()=>i(!0),[]);let a=n||o&&globalThis?.document?.body;return a?u.createPortal((0,f.jsx)(tN.div,{...r,ref:t}),a):null});tM.displayName="Portal";var tD=a[" useInsertionEffect ".trim().toString()]||U;function tI({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return tD(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,l.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}Symbol("RADIX:SYNC_STATE");var tW=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,f.jsx)(S.span,{...e,ref:t,style:{...tW,...e.style}})).displayName="VisuallyHidden";var tF=new WeakMap,t_=new WeakMap,tH={},tB=0,tz=function(e){return e&&(e.host||tz(e.parentNode))},tV=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tz(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tH[n]||(tH[n]=new WeakMap);var i=tH[n],l=[],a=new Set,u=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var c=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tF.get(e)||0)+1,s=(i.get(e)||0)+1;tF.set(e,u),i.set(e,s),l.push(e),1===u&&o&&t_.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),tB++,function(){l.forEach(function(e){var t=tF.get(e)-1,o=i.get(e)-1;tF.set(e,t),i.set(e,o),t||(t_.has(e)||e.removeAttribute(r),t_.delete(e)),o||e.removeAttribute(n)}),--tB||(tF=new WeakMap,tF=new WeakMap,t_=new WeakMap,tH={})}},t$=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),tV(r,o,n,"aria-hidden")):function(){return null}},tK=function(){return(tK=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tY(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;var tU="right-scroll-bar-position",tX="width-before-scroll-bar";function tq(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tZ="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tG=new WeakMap;function tJ(e){return e}var tQ=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=tJ),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=tK({async:!0,ssr:!1},e),o}(),t0=function(){},t1=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),u=l.useState({onScrollCapture:t0,onWheelCapture:t0,onTouchMoveCapture:t0}),s=u[0],c=u[1],f=e.forwardProps,d=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,y=e.sideCar,g=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,E=e.as,S=e.gapMode,C=tY(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[a,t],r=function(e){return n.forEach(function(t){return tq(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tZ(function(){var e=tG.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tq(e,null)}),r.forEach(function(e){t.has(e)||tq(e,o)})}tG.set(i,n)},[n]),i),A=tK(tK({},C),s);return l.createElement(l.Fragment,null,m&&l.createElement(y,{sideCar:tQ,removeScrollBar:h,shards:v,noRelative:g,noIsolation:w,inert:x,setCallbacks:c,allowPinchZoom:!!b,lockRef:a,gapMode:S}),f?l.cloneElement(l.Children.only(d),tK(tK({},A),{ref:R})):l.createElement(void 0===E?"div":E,tK({},A,{className:p,ref:R}),d))});t1.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t1.classNames={fullWidth:tX,zeroRight:tU};var t2=function(e){var t=e.sideCar,n=tY(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,tK({},n))};t2.isSideCarExport=!0;var t5=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t6=function(){var e=t5();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t9=function(){var e=t6();return function(t){return e(t.styles,t.dynamic),null}},t3={left:0,top:0,right:0,gap:0},t7=function(e){return parseInt(e||"",10)||0},t8=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t7(n),t7(r),t7(o)]},t4=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t3;var t=t8(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ne=t9(),nt="data-scroll-locked",nn=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(nt,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tU," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tX," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tU," .").concat(tU," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tX," .").concat(tX," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(nt,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},nr=function(){var e=parseInt(document.body.getAttribute(nt)||"0",10);return isFinite(e)?e:0},no=function(){l.useEffect(function(){return document.body.setAttribute(nt,(nr()+1).toString()),function(){var e=nr()-1;e<=0?document.body.removeAttribute(nt):document.body.setAttribute(nt,e.toString())}},[])},ni=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;no();var i=l.useMemo(function(){return t4(o)},[o]);return l.createElement(ne,{styles:nn(i,!t,o,n?"":"!important")})},nl=!1;if("undefined"!=typeof window)try{var na=Object.defineProperty({},"passive",{get:function(){return nl=!0,!0}});window.addEventListener("test",na,na),window.removeEventListener("test",na,na)}catch(e){nl=!1}var nu=!!nl&&{passive:!1},ns=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},nc=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nf(e,r)){var o=nd(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nf=function(e,t){return"v"===e?ns(t,"overflowY"):ns(t,"overflowX")},nd=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},np=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,f=a>0,d=0,p=0;do{if(!u)break;var h=nd(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&nf(e,u)&&(d+=v,p+=m);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&a>d)?c=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},nh=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},nm=function(e){return[e.deltaX,e.deltaY]},nv=function(e){return e&&"current"in e?e.current:e},ny=0,ng=[];let nw=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(ny++)[0],i=l.useState(t9)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nv),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=nh(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],s="deltaY"in e?e.deltaY:l[1]-i[1],c=e.target,f=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===f&&"range"===c.type)return!1;var d=nc(f,c);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=nc(f,c)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return np(p,t,e,"h"===p?u:s,!0)},[]),s=l.useCallback(function(e){if(ng.length&&ng[ng.length-1]===i){var n="deltaY"in e?nm(e):nh(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(nv).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=l.useCallback(function(e){n.current=nh(e),r.current=void 0},[]),d=l.useCallback(function(t){c(t.type,nm(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){c(t.type,nh(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return ng.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",s,nu),document.addEventListener("touchmove",s,nu),document.addEventListener("touchstart",f,nu),function(){ng=ng.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,nu),document.removeEventListener("touchmove",s,nu),document.removeEventListener("touchstart",f,nu)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(ni,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tQ.useMedium(r),t2);var nx=l.forwardRef(function(e,t){return l.createElement(t1,tK({},e,{ref:t,sideCar:nw}))});nx.classNames=t1.classNames;var nb=[" ","Enter","ArrowUp","ArrowDown"],nE=[" ","Enter"],nS="Select",[nC,nR,nA]=function(e){let t=e+"CollectionProvider",[n,r]=d(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=l.useRef(null),i=l.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",s=v(u),c=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=m(t,i(u,n).collectionRef);return(0,f.jsx)(s,{ref:o,children:r})});c.displayName=u;let p=e+"CollectionItemSlot",h="data-radix-collection-item",y=v(p),g=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=l.useRef(null),u=m(t,a),s=i(p,n);return l.useEffect(()=>(s.itemMap.set(a,{ref:a,...o}),()=>void s.itemMap.delete(a))),(0,f.jsx)(y,{...{[h]:""},ref:u,children:r})});return g.displayName=p,[{Provider:a,Slot:c,ItemSlot:g},function(t){let n=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(nS),[nk,nP]=d(nS,[nA,tp]),nT=tp(),[nL,nj]=nk(nS),[nN,nO]=nk(nS),nM=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:a,defaultValue:u,onValueChange:s,dir:c,name:d,autoComplete:p,disabled:h,required:m,form:v}=e,y=nT(t),[g,w]=l.useState(null),[x,b]=l.useState(null),[S,C]=l.useState(!1),R=function(e){let t=l.useContext(E);return e||t||"ltr"}(c),[A,k]=tI({prop:r,defaultProp:o??!1,onChange:i,caller:nS}),[P,T]=tI({prop:a,defaultProp:u,onChange:s,caller:nS}),L=l.useRef(null),j=!g||v||!!g.closest("form"),[N,O]=l.useState(new Set),M=Array.from(N).map(e=>e.props.value).join(";");return(0,f.jsx)(tv,{...y,children:(0,f.jsxs)(nL,{required:m,scope:t,trigger:g,onTriggerChange:w,valueNode:x,onValueNodeChange:b,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:Z(),value:P,onValueChange:T,open:A,onOpenChange:k,dir:R,triggerPointerDownPosRef:L,disabled:h,children:[(0,f.jsx)(nC.Provider,{scope:t,children:(0,f.jsx)(nN,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),j?(0,f.jsxs)(rc,{"aria-hidden":!0,required:m,tabIndex:-1,name:d,autoComplete:p,value:P,onChange:e=>T(e.target.value),disabled:h,form:v,children:[void 0===P?(0,f.jsx)("option",{value:""}):null,Array.from(N)]},M):null]})})};nM.displayName=nS;var nD="SelectTrigger",nI=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nT(n),a=nj(nD,n),u=a.disabled||r,s=m(t,a.onTriggerChange),d=nR(n),p=l.useRef("touch"),[h,v,y]=rd(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=rp(t,e,n);void 0!==r&&a.onValueChange(r.value)}),g=e=>{u||(a.onOpenChange(!0),y()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,f.jsx)(tg,{asChild:!0,...i,children:(0,f.jsx)(S.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":rf(a.value)?"":void 0,...o,ref:s,onClick:c(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:c(o.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:c(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&nb.includes(e.key)&&(g(),e.preventDefault())})})})});nI.displayName=nD;var nW="SelectValue",nF=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nj(nW,n),{onValueNodeHasChildrenChange:s}=u,c=void 0!==i,d=m(t,u.onValueNodeChange);return U(()=>{s(c)},[s,c]),(0,f.jsx)(S.span,{...a,ref:d,style:{pointerEvents:"none"},children:rf(u.value)?(0,f.jsx)(f.Fragment,{children:l}):i})});nF.displayName=nW;var n_=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,f.jsx)(S.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});n_.displayName="SelectIcon";var nH=e=>(0,f.jsx)(tM,{asChild:!0,...e});nH.displayName="SelectPortal";var nB="SelectContent",nz=l.forwardRef((e,t)=>{let n=nj(nB,e.__scopeSelect),[r,o]=l.useState();return(U(()=>{o(new DocumentFragment)},[]),n.open)?(0,f.jsx)(nY,{...e,ref:t}):r?u.createPortal((0,f.jsx)(nV,{scope:e.__scopeSelect,children:(0,f.jsx)(nC.Slot,{scope:e.__scopeSelect,children:(0,f.jsx)("div",{children:e.children})})}),r):null});nz.displayName=nB;var[nV,n$]=nk(nB),nK=v("SelectContent.RemoveScroll"),nY=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:a,side:u,sideOffset:s,align:d,alignOffset:p,arrowPadding:h,collisionBoundary:v,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:x,...b}=e,E=nj(nB,n),[S,C]=l.useState(null),[R,A]=l.useState(null),P=m(t,e=>C(e)),[T,N]=l.useState(null),[O,M]=l.useState(null),D=nR(n),[I,W]=l.useState(!1),F=l.useRef(!1);l.useEffect(()=>{if(S)return t$(S)},[S]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??j()),document.body.insertAdjacentElement("beforeend",e[1]??j()),L++,()=>{1===L&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),L--}},[]);let _=l.useCallback(e=>{let[t,...n]=D().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),n?.focus(),document.activeElement!==o))return},[D,R]),H=l.useCallback(()=>_([T,S]),[_,T,S]);l.useEffect(()=>{I&&H()},[I,H]);let{onOpenChange:z,triggerPointerDownPosRef:V}=E;l.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(V.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(V.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||z(!1),document.removeEventListener("pointermove",t),V.current=null};return null!==V.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,z,V]),l.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[$,K]=rd(e=>{let t=D().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=rp(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Y=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==E.value&&E.value===t||r)&&(N(e),r&&(F.current=!0))},[E.value]),U=l.useCallback(()=>S?.focus(),[S]),X=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==E.value&&E.value===t||r)&&M(e)},[E.value]),q="popper"===r?nX:nU,Z=q===nX?{side:u,sideOffset:s,align:d,alignOffset:p,arrowPadding:h,collisionBoundary:v,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:x}:{};return(0,f.jsx)(nV,{scope:n,content:S,viewport:R,onViewportChange:A,itemRefCallback:Y,selectedItem:T,onItemLeave:U,itemTextRefCallback:X,focusSelectedItem:H,selectedItemText:O,position:r,isPositioned:I,searchRef:$,children:(0,f.jsx)(nx,{as:nK,allowPinchZoom:!0,children:(0,f.jsx)(B,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:c(o,e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,f.jsx)(k,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,f.jsx)(q,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...Z,onPlaced:()=>W(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:c(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=D().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>_(t)),e.preventDefault()}})})})})})})});nY.displayName="SelectContentImpl";var nU=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nj(nB,n),a=n$(nB,n),[u,c]=l.useState(null),[d,p]=l.useState(null),h=m(t,e=>p(e)),v=nR(n),y=l.useRef(!1),g=l.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:E}=a,C=l.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&d&&w&&x&&b){let e=i.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,c=Math.max(a,t.width),f=s(i,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=a+"px",u.style.left=f+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,c=Math.max(a,t.width),f=s(i,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=a+"px",u.style.right=f+"px"}let l=v(),a=window.innerHeight-20,c=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),m=parseInt(f.borderBottomWidth,10),g=p+h+c+parseInt(f.paddingBottom,10)+m,E=Math.min(5*x.offsetHeight,g),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,k=x.offsetHeight/2,P=p+h+(x.offsetTop+k);if(P<=A){let e=l.length>0&&x===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(a-A,k+(e?R:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+m);u.style.height=P+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;u.style.top="0px";let t=Math.max(A,p+w.offsetTop+(e?C:0)+k);u.style.height=t+(g-P)+"px",w.scrollTop=P-A+w.offsetTop}u.style.margin="10px 0",u.style.minHeight=E+"px",u.style.maxHeight=a+"px",r?.(),requestAnimationFrame(()=>y.current=!0)}},[v,i.trigger,i.valueNode,u,d,w,x,b,i.dir,r]);U(()=>C(),[C]);let[R,A]=l.useState();U(()=>{d&&A(window.getComputedStyle(d).zIndex)},[d]);let k=l.useCallback(e=>{e&&!0===g.current&&(C(),E?.(),g.current=!1)},[C,E]);return(0,f.jsx)(nq,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:y,onScrollButtonChange:k,children:(0,f.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,f.jsx)(S.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nU.displayName="SelectItemAlignedPosition";var nX=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nT(n);return(0,f.jsx)(tE,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nX.displayName="SelectPopperPosition";var[nq,nZ]=nk(nB,{}),nG="SelectViewport",nJ=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=n$(nG,n),a=nZ(nG,n),u=m(t,i.onViewportChange),s=l.useRef(0);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,f.jsx)(nC.Slot,{scope:n,children:(0,f.jsx)(S.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:c(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if(r?.current&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});nJ.displayName=nG;var nQ="SelectGroup",[n0,n1]=nk(nQ),n2=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=Z();return(0,f.jsx)(n0,{scope:n,id:o,children:(0,f.jsx)(S.div,{role:"group","aria-labelledby":o,...r,ref:t})})});n2.displayName=nQ;var n5="SelectLabel",n6=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=n1(n5,n);return(0,f.jsx)(S.div,{id:o.id,...r,ref:t})});n6.displayName=n5;var n9="SelectItem",[n3,n7]=nk(n9),n8=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...a}=e,u=nj(n9,n),s=n$(n9,n),d=u.value===r,[p,h]=l.useState(i??""),[v,y]=l.useState(!1),g=m(t,e=>s.itemRefCallback?.(e,r,o)),w=Z(),x=l.useRef("touch"),b=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,f.jsx)(n3,{scope:n,value:r,disabled:o,textId:w,isSelected:d,onItemTextChange:l.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,f.jsx)(nC.ItemSlot,{scope:n,value:r,disabled:o,textValue:p,children:(0,f.jsx)(S.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":d&&v,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:g,onFocus:c(a.onFocus,()=>y(!0)),onBlur:c(a.onBlur,()=>y(!1)),onClick:c(a.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:c(a.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:c(a.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:c(a.onPointerMove,e=>{x.current=e.pointerType,o?s.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:c(a.onPointerLeave,e=>{e.currentTarget===document.activeElement&&s.onItemLeave?.()}),onKeyDown:c(a.onKeyDown,e=>{(s.searchRef?.current===""||" "!==e.key)&&(nE.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});n8.displayName=n9;var n4="SelectItemText",re=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,a=nj(n4,n),s=n$(n4,n),c=n7(n4,n),d=nO(n4,n),[p,h]=l.useState(null),v=m(t,e=>h(e),c.onItemTextChange,e=>s.itemTextRefCallback?.(e,c.value,c.disabled)),y=p?.textContent,g=l.useMemo(()=>(0,f.jsx)("option",{value:c.value,disabled:c.disabled,children:y},c.value),[c.disabled,c.value,y]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return U(()=>(w(g),()=>x(g)),[w,x,g]),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(S.span,{id:c.textId,...i,ref:v}),c.isSelected&&a.valueNode&&!a.valueNodeHasChildren?u.createPortal(i.children,a.valueNode):null]})});re.displayName=n4;var rt="SelectItemIndicator",rn=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return n7(rt,n).isSelected?(0,f.jsx)(S.span,{"aria-hidden":!0,...r,ref:t}):null});rn.displayName=rt;var rr="SelectScrollUpButton",ro=l.forwardRef((e,t)=>{let n=n$(rr,e.__scopeSelect),r=nZ(rr,e.__scopeSelect),[o,i]=l.useState(!1),a=m(t,r.onScrollButtonChange);return U(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,f.jsx)(ra,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ro.displayName=rr;var ri="SelectScrollDownButton",rl=l.forwardRef((e,t)=>{let n=n$(ri,e.__scopeSelect),r=nZ(ri,e.__scopeSelect),[o,i]=l.useState(!1),a=m(t,r.onScrollButtonChange);return U(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,f.jsx)(ra,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});rl.displayName=ri;var ra=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=n$("SelectScrollButton",n),a=l.useRef(null),u=nR(n),s=l.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return l.useEffect(()=>()=>s(),[s]),U(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,f.jsx)(S.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:c(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:c(o.onPointerMove,()=>{i.onItemLeave?.(),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:c(o.onPointerLeave,()=>{s()})})}),ru=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,f.jsx)(S.div,{"aria-hidden":!0,...r,ref:t})});ru.displayName="SelectSeparator";var rs="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nT(n),i=nj(rs,n),l=n$(rs,n);return i.open&&"popper"===l.position?(0,f.jsx)(tR,{...o,...r,ref:t}):null}).displayName=rs;var rc=l.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let o=l.useRef(null),i=m(r,o),a=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return l.useEffect(()=>{let e=o.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,f.jsx)(S.select,{...n,style:{...tW,...n.style},ref:i,defaultValue:t})});function rf(e){return""===e||void 0===e}function rd(e){let t=C(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function rp(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}rc.displayName="SelectBubbleInput";var rh=nM,rm=nI,rv=nF,ry=n_,rg=nH,rw=nz,rx=nJ,rb=n2,rE=n6,rS=n8,rC=re,rR=rn,rA=ro,rk=rl,rP=ru},5316:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9065).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7295:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9065).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},8129:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9065).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);