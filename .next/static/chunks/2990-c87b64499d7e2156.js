(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2990],{222:(e,t,r)=>{"use strict";r.d(t,{c:()=>i});var n=r(2115);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},861:(e,t,r)=>{"use strict";r.d(t,{b:()=>a,s:()=>o});var n=r(2115),i=r(7602),s=r(5155),o=n.forwardRef((e,t)=>(0,s.jsx)(i.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));o.displayName="VisuallyHidden";var a=o},1013:e=>{e.exports={style:{fontFamily:"'Playfair Display', 'Playfair Display Fallback'",fontStyle:"normal"},className:"__className_0a80b4",variable:"__variable_0a80b4"}},1043:(e,t,r)=>{"use strict";r.d(t,{useIsMutating:()=>a,useMutationState:()=>l});var n=r(2115),i=r(4049),s=r(4268),o=r(9776);function a(e,t){let r=(0,o.useQueryClient)(t);return l({filters:{...e,status:"pending"}},r).length}function u(e,t){return e.findAll(t.filters).map(e=>t.select?t.select(e):e.state)}function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,r=(0,o.useQueryClient)(t).getMutationCache(),a=n.useRef(e),l=n.useRef(null);return l.current||(l.current=u(r,e)),n.useEffect(()=>{a.current=e}),n.useSyncExternalStore(n.useCallback(e=>r.subscribe(()=>{let t=(0,i.BH)(l.current,u(r,a.current));l.current!==t&&(l.current=t,s.j.schedule(e))}),[r]),()=>l.current,()=>l.current)}},1456:(e,t,r)=>{"use strict";r.d(t,{useMutation:()=>d});var n=r(2115),i=r(4268),s=r(2162);r(5554);s.k;var o=r(8445),a=r(4049),u=class extends o.Q{#e;#t=void 0;#r;#n;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,a.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,a.EN)(t.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#s(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#i(),this.#s()}mutate(e,t){return this.#n=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#i(){let e=this.#r?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#s(e){i.j.batch(()=>{if(this.#n&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,r),this.#n.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#n.onError?.(e.error,t,r),this.#n.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},l=r(9776),c=r(2211);function d(e,t){let r=(0,l.useQueryClient)(t),[s]=n.useState(()=>new u(r,e));n.useEffect(()=>{s.setOptions(e)},[s,e]);let o=n.useSyncExternalStore(n.useCallback(e=>s.subscribe(i.j.batchCalls(e)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),a=n.useCallback((e,t)=>{s.mutate(e,t).catch(c.l)},[s]);if(o.error&&(0,c.G)(s.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:a,mutateAsync:o.mutate}}},1515:(e,t,r)=>{"use strict";r.d(t,{useQueries:()=>v});var n=r(2115),i=r(4268),s=r(6604),o=r(8445),a=r(4049);function u(e,t){return e.filter(e=>!t.includes(e))}var l=class extends o.Q{#e;#o;#a;#u;#l;#c;#d;#f;constructor(e,t,r){super(),this.#e=e,this.#u=r,this.#a=[],this.#l=[],this.#o=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#l.forEach(e=>{e.subscribe(t=>{this.#h(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#l.forEach(e=>{e.destroy()})}setQueries(e,t,r){this.#a=e,this.#u=t,i.j.batch(()=>{let e=this.#l,t=this.#p(this.#a);t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions,r));let n=t.map(e=>e.observer),i=n.map(e=>e.getCurrentResult()),s=n.some((t,r)=>t!==e[r]);(e.length!==n.length||s)&&(this.#l=n,this.#o=i,this.hasListeners()&&(u(e,n).forEach(e=>{e.destroy()}),u(n,e).forEach(e=>{e.subscribe(t=>{this.#h(e,t)})}),this.#s()))})}getCurrentResult(){return this.#o}getQueries(){return this.#l.map(e=>e.getCurrentQuery())}getObservers(){return this.#l}getOptimisticResult(e,t){let r=this.#p(e),n=r.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[n,e=>this.#v(e??n,t),()=>r.map((e,t)=>{let i=n[t];return e.defaultedQueryOptions.notifyOnChangeProps?i:e.observer.trackResult(i,e=>{r.forEach(t=>{t.observer.trackProp(e)})})})]}#v(e,t){return t?(this.#c&&this.#o===this.#f&&t===this.#d||(this.#d=t,this.#f=this.#o,this.#c=(0,a.BH)(this.#c,t(e))),this.#c):e}#p(e){let t=new Map(this.#l.map(e=>[e.options.queryHash,e])),r=[];return e.forEach(e=>{let n=this.#e.defaultQueryOptions(e),i=t.get(n.queryHash);if(i)r.push({defaultedQueryOptions:n,observer:i});else{let e=this.#l.find(e=>e.options.queryHash===n.queryHash);r.push({defaultedQueryOptions:n,observer:e??new s.$(this.#e,n)})}}),r.sort((t,r)=>e.findIndex(e=>e.queryHash===t.defaultedQueryOptions.queryHash)-e.findIndex(e=>e.queryHash===r.defaultedQueryOptions.queryHash))}#h(e,t){let r=this.#l.indexOf(e);-1!==r&&(this.#o=function(e,t,r){let n=e.slice(0);return n[t]=r,n}(this.#o,r,t),this.#s())}#s(){this.hasListeners()&&this.#c!==this.#v(this.#o,this.#u?.combine)&&i.j.batch(()=>{this.listeners.forEach(e=>{e(this.#o)})})}},c=r(9776),d=r(4019),f=r(7785),h=r(6687),p=r(5822);function v(e,t){let{queries:r,...o}=e,a=(0,c.useQueryClient)(t),u=(0,d.useIsRestoring)(),v=(0,f.useQueryErrorResetBoundary)(),y=n.useMemo(()=>r.map(e=>{let t=a.defaultQueryOptions(e);return t._optimisticResults=u?"isRestoring":"optimistic",t}),[r,a,u]);y.forEach(e=>{(0,p.jv)(e),(0,h.LJ)(e,v)}),(0,h.wZ)(v);let[m]=n.useState(()=>new l(a,y,o)),[b,g,E]=m.getOptimisticResult(y,o.combine);n.useSyncExternalStore(n.useCallback(e=>u?()=>void 0:m.subscribe(i.j.batchCalls(e)),[m,u]),()=>m.getCurrentResult(),()=>m.getCurrentResult()),n.useEffect(()=>{m.setQueries(y,o,{listeners:!1})},[y,o,m]);let w=b.some((e,t)=>(0,p.EU)(y[t],e))?b.flatMap((e,t)=>{let r=y[t];if(r){let t=new s.$(a,r);if((0,p.EU)(r,e))return(0,p.iL)(r,t,v);(0,p.nE)(e,u)&&(0,p.iL)(r,t,v)}return[]}):[];if(w.length>0)throw Promise.all(w);let T=b.find((e,t)=>{let r=y[t];return r&&(0,h.$1)({result:e,errorResetBoundary:v,throwOnError:r.throwOnError,query:a.getQueryCache().get(r.queryHash)})});if(null==T?void 0:T.error)throw T.error;return g(E())}},2162:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var n=r(4049),i=class{#y;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.gn)(this.gcTime)&&(this.#y=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(n.S$?1/0:3e5))}clearGcTimeout(){this.#y&&(clearTimeout(this.#y),this.#y=void 0)}}},2211:(e,t,r)=>{"use strict";function n(e,t){return"function"==typeof e?e(...t):!!e}function i(){}r.d(t,{G:()=>n,l:()=>i})},2556:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},2758:(e,t,r)=>{e.exports=r(9298)()},2798:(e,t,r)=>{"use strict";r.d(t,{z:()=>u});var n=r(6604),i=r(4049);function s(e){return{onFetch:(t,r)=>{let n=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,u=t.state.data?.pages||[],l=t.state.data?.pageParams||[],c={pages:[],pageParams:[]},d=0,f=async()=>{let r=!1,f=(0,i.ZM)(t.options,t.fetchOptions),h=async(e,n,s)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let o={queryKey:t.queryKey,pageParam:n,direction:s?"backward":"forward",meta:t.options.meta};Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)});let a=await f(o),{maxPages:u}=t.options,l=s?i.ZZ:i.y9;return{pages:l(e.pages,a,u),pageParams:l(e.pageParams,n,u)}};if(s&&u.length){let e="backward"===s,t={pages:u,pageParams:l},r=(e?a:o)(n,t);c=await h(t,r,e)}else{let t=e??u.length;do{let e=0===d?l[0]??n.initialPageParam:o(n,c);if(d>0&&null==e)break;c=await h(c,e),d++}while(d<t)}return c};t.options.persister?t.fetchFn=()=>t.options.persister?.(f,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=f}}}function o(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}function a(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}var u=class extends n.${constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:s()},t)}getOptimisticResult(e){return e.behavior=s(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){var r,n;let{state:i}=e,s=super.createResult(e,t),{isFetching:u,isRefetching:l,isError:c,isRefetchError:d}=s,f=i.fetchMeta?.fetchMore?.direction,h=c&&"forward"===f,p=u&&"forward"===f,v=c&&"backward"===f,y=u&&"backward"===f;return{...s,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:!!(r=i.data)&&null!=o(t,r),hasPreviousPage:!!(n=i.data)&&!!t.getPreviousPageParam&&null!=a(t,n),isFetchNextPageError:h,isFetchingNextPage:p,isFetchPreviousPageError:v,isFetchingPreviousPage:y,isRefetchError:d&&!h&&!v,isRefetching:l&&!p&&!y}}}},3341:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3455:(e,t,r)=>{"use strict";r.d(t,{useQuery:()=>s});var n=r(6604),i=r(6384);function s(e,t){return(0,i.t)(e,n.$,t)}},3468:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(2115),i=r(5155);function s(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,s){let o=n.createContext(s),a=r.length;r=[...r,s];let u=t=>{let{scope:r,children:s,...u}=t,l=r?.[e]?.[a]||o,c=n.useMemo(()=>u,Object.values(u));return(0,i.jsx)(l.Provider,{value:c,children:s})};return u.displayName=t+"Provider",[u,function(r,i){let u=i?.[e]?.[a]||o,l=n.useContext(u);if(l)return l;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}},3515:(e,t,r)=>{"use strict";function n(){let e,t,r=new Promise((r,n)=>{e=r,t=n});function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}r.d(t,{T:()=>n})},3558:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});var n=r(2115),i=r(222);function s({prop:e,defaultProp:t,onChange:r=()=>{}}){let[s,o]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[s]=r,o=n.useRef(s),a=(0,i.c)(t);return n.useEffect(()=>{o.current!==s&&(a(s),o.current=s)},[s,o,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,u=a?e:s,l=(0,i.c)(r);return[u,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&l(r)}else o(t)},[a,e,o,l])]}},3651:(e,t,r)=>{"use strict";var n=r(2115),i=function(e){return e&&"object"==typeof e&&"default"in e?e.default:e}(n);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var o=!!("undefined"!=typeof window&&window.document&&window.document.createElement);e.exports=function(e,t,r){if("function"!=typeof e)throw Error("Expected reducePropsToState to be a function.");if("function"!=typeof t)throw Error("Expected handleStateChangeOnClient to be a function.");if(void 0!==r&&"function"!=typeof r)throw Error("Expected mapStateOnServer to either be undefined or a function.");return function(a){if("function"!=typeof a)throw Error("Expected WrappedComponent to be a React component.");var u,l=[];function c(){u=e(l.map(function(e){return e.props})),d.canUseDOM?t(u):r&&(u=r(u))}var d=function(e){function t(){return e.apply(this,arguments)||this}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e,t.peek=function(){return u},t.rewind=function(){if(t.canUseDOM)throw Error("You may only call rewind() on the server. Call peek() to read the current state.");var e=u;return u=void 0,l=[],e};var r=t.prototype;return r.UNSAFE_componentWillMount=function(){l.push(this),c()},r.componentDidUpdate=function(){c()},r.componentWillUnmount=function(){var e=l.indexOf(this);l.splice(e,1),c()},r.render=function(){return i.createElement(a,this.props)},t}(n.PureComponent);return s(d,"displayName","SideEffect("+(a.displayName||a.name||"Component")+")"),s(d,"canUseDOM",o),d}}},4019:(e,t,r)=>{"use strict";r.d(t,{IsRestoringProvider:()=>o,useIsRestoring:()=>s});var n=r(2115),i=n.createContext(!1),s=()=>n.useContext(i),o=i.Provider},4049:(e,t,r)=>{"use strict";r.d(t,{BH:()=>c,EN:()=>l,Eh:()=>u,S$:()=>n,ZM:()=>E,ZZ:()=>b,d2:()=>a,f8:()=>d,gn:()=>s,hT:()=>g,j3:()=>o,lQ:()=>i,pl:()=>y,y9:()=>m,yy:()=>v});var n="undefined"==typeof window||"Deno"in globalThis;function i(){}function s(e){return"number"==typeof e&&e>=0&&e!==1/0}function o(e,t){return Math.max(e+(t||0)-Date.now(),0)}function a(e,t){return"function"==typeof e?e(t):e}function u(e,t){return"function"==typeof e?e(t):e}function l(e){return JSON.stringify(e,(e,t)=>h(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function c(e,t){if(e===t)return e;let r=f(e)&&f(t);if(r||h(e)&&h(t)){let n=r?e:Object.keys(e),i=n.length,s=r?t:Object.keys(t),o=s.length,a=r?[]:{},u=0;for(let i=0;i<o;i++){let o=r?i:s[i];(!r&&n.includes(o)||r)&&void 0===e[o]&&void 0===t[o]?(a[o]=void 0,u++):(a[o]=c(e[o],t[o]),a[o]===e[o]&&void 0!==e[o]&&u++)}return i===o&&u===i?e:a}return t}function d(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}function f(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function h(e){if(!p(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!p(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function p(e){return"[object Object]"===Object.prototype.toString.call(e)}function v(e){return new Promise(t=>{setTimeout(t,e)})}function y(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?c(e,t):t}function m(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}function b(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var g=Symbol();function E(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==g?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}},4129:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var n=r(2115),i=globalThis?.document?n.useLayoutEffect:()=>{}},4268:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});var n=function(){let e=[],t=0,r=e=>{e()},n=e=>{e()},i=e=>setTimeout(e,0),s=n=>{t?e.push(n):i(()=>{r(n)})};return{batch:s=>{let o;t++;try{o=s()}finally{--t||(()=>{let t=e;e=[],t.length&&i(()=>{n(()=>{t.forEach(e=>{r(e)})})})})()}return o},batchCalls:e=>(...t)=>{s(()=>{e(...t)})},schedule:s,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{i=e}}}()},4766:e=>{"use strict";e.exports=Object.assign.bind(Object),e.exports.default=e.exports},5339:(e,t,r)=>{"use strict";r.d(t,{useSuspenseInfiniteQuery:()=>o});var n=r(2798),i=r(6384),s=r(5822);function o(e,t){return(0,i.t)({...e,enabled:!0,suspense:!0,throwOnError:s.R3},n.z,t)}},5379:(e,t,r)=>{"use strict";r.d(t,{useTheme:()=>o});var n=r(2115),i=n.createContext(void 0),s={setTheme:e=>{},themes:[]},o=()=>{var e;return null!=(e=n.useContext(i))?e:s}},5423:(e,t,r)=>{"use strict";r.d(t,{Action:()=>ea,Close:()=>eu,Description:()=>eo,Provider:()=>er,Root:()=>ei,Title:()=>es,Viewport:()=>en});var n,i=r(2115),s=r(7650),o=r(2556),a=r(4446),u=r(5155),l=r(2467),c=r(3468),d=r(7602),f=r(222),h="dismissableLayer.update",p=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),v=i.forwardRef((e,t)=>{var r,s;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:c,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:g,onDismiss:E,...w}=e,T=i.useContext(p),[C,x]=i.useState(null),R=null!=(s=null==C?void 0:C.ownerDocument)?s:null==(r=globalThis)?void 0:r.document,[,O]=i.useState({}),P=(0,a.s)(t,e=>x(e)),S=Array.from(T.layers),[L]=[...T.layersWithOutsidePointerEventsDisabled].slice(-1),A=S.indexOf(L),N=C?S.indexOf(C):-1,j=T.layersWithOutsidePointerEventsDisabled.size>0,I=N>=A,M=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,f.c)(e),s=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!s.current){let t=function(){b("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",o.current),o.current=t,r.addEventListener("click",o.current,{once:!0})):t()}else r.removeEventListener("click",o.current);s.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",o.current)}},[r,n]),{onPointerDownCapture:()=>s.current=!0}}(e=>{let t=e.target,r=[...T.branches].some(e=>e.contains(t));I&&!r&&(null==v||v(e),null==g||g(e),e.defaultPrevented||null==E||E())},R),k=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,f.c)(e),s=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!s.current&&b("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(e=>{let t=e.target;![...T.branches].some(e=>e.contains(t))&&(null==y||y(e),null==g||g(e),e.defaultPrevented||null==E||E())},R);return!function(e,t=globalThis?.document){let r=(0,f.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{N===T.layers.size-1&&(null==c||c(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},R),i.useEffect(()=>{if(C)return l&&(0===T.layersWithOutsidePointerEventsDisabled.size&&(n=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),T.layersWithOutsidePointerEventsDisabled.add(C)),T.layers.add(C),m(),()=>{l&&1===T.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=n)}},[C,R,l,T]),i.useEffect(()=>()=>{C&&(T.layers.delete(C),T.layersWithOutsidePointerEventsDisabled.delete(C),m())},[C,T]),i.useEffect(()=>{let e=()=>O({});return document.addEventListener(h,e),()=>document.removeEventListener(h,e)},[]),(0,u.jsx)(d.sG.div,{...w,ref:P,style:{pointerEvents:j?I?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});v.displayName="DismissableLayer";var y=i.forwardRef((e,t)=>{let r=i.useContext(p),n=i.useRef(null),s=(0,a.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(d.sG.div,{...e,ref:s})});function m(){let e=new CustomEvent(h);document.dispatchEvent(e)}function b(e,t,r,n){let{discrete:i}=n,s=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),i?(0,d.hO)(s,o):s.dispatchEvent(o)}y.displayName="DismissableLayerBranch";var g=r(4129),E=i.forwardRef((e,t)=>{var r,n;let{container:o,...a}=e,[l,c]=i.useState(!1);(0,g.N)(()=>c(!0),[]);let f=o||l&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return f?s.createPortal((0,u.jsx)(d.sG.div,{...a,ref:t}),f):null});E.displayName="Portal";var w=r(6842),T=r(3558),C=r(861),x="ToastProvider",[R,O,P]=function(e){let t=e+"CollectionProvider",[r,n]=function(e,t=[]){let r=[],n=()=>{let t=r.map(e=>i.createContext(e));return function(r){let n=r?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let s=i.createContext(n),o=r.length;function a(t){let{scope:r,children:n,...a}=t,l=r?.[e][o]||s,c=i.useMemo(()=>a,Object.values(a));return(0,u.jsx)(l.Provider,{value:c,children:n})}return r=[...r,n],a.displayName=t+"Provider",[a,function(r,a){let u=a?.[e][o]||s,l=i.useContext(u);if(l)return l;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}(t),[s,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,n=i.useRef(null),o=i.useRef(new Map).current;return(0,u.jsx)(s,{scope:t,itemMap:o,collectionRef:n,children:r})};c.displayName=t;let d=e+"CollectionSlot",f=i.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=o(d,r),s=(0,a.s)(t,i.collectionRef);return(0,u.jsx)(l.DX,{ref:s,children:n})});f.displayName=d;let h=e+"CollectionItemSlot",p="data-radix-collection-item",v=i.forwardRef((e,t)=>{let{scope:r,children:n,...s}=e,c=i.useRef(null),d=(0,a.s)(t,c),f=o(h,r);return i.useEffect(()=>(f.itemMap.set(c,{ref:c,...s}),()=>void f.itemMap.delete(c))),(0,u.jsx)(l.DX,{...{[p]:""},ref:d,children:n})});return v.displayName=h,[{Provider:c,Slot:f,ItemSlot:v},function(t){let r=o(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}("Toast"),[S,L]=(0,c.A)("Toast",[P]),[A,N]=S(x),j=e=>{let{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:s="right",swipeThreshold:o=50,children:a}=e,[l,c]=i.useState(null),[d,f]=i.useState(0),h=i.useRef(!1),p=i.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(x,"`. Expected non-empty `string`.")),(0,u.jsx)(R.Provider,{scope:t,children:(0,u.jsx)(A,{scope:t,label:r,duration:n,swipeDirection:s,swipeThreshold:o,toastCount:d,viewport:l,onViewportChange:c,onToastAdd:i.useCallback(()=>f(e=>e+1),[]),onToastRemove:i.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:p,children:a})})};j.displayName=x;var I="ToastViewport",M=["F8"],k="toast.viewportPause",_="toast.viewportResume",D=i.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:n=M,label:s="Notifications ({hotkey})",...o}=e,l=N(I,r),c=O(r),f=i.useRef(null),h=i.useRef(null),p=i.useRef(null),v=i.useRef(null),m=(0,a.s)(t,v,l.onViewportChange),b=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=l.toastCount>0;i.useEffect(()=>{let e=e=>{var t;0!==n.length&&n.every(t=>e[t]||e.code===t)&&(null==(t=v.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[n]),i.useEffect(()=>{let e=f.current,t=v.current;if(g&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(k);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(_);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||n()},s=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[g,l.isClosePausedRef]);let E=i.useCallback(e=>{let{tabbingDirection:t}=e,r=c().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[c]);return i.useEffect(()=>{let e=v.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,i,s;let r=document.activeElement,o=t.shiftKey;if(t.target===e&&o){null==(n=h.current)||n.focus();return}let a=E({tabbingDirection:o?"backwards":"forwards"}),u=a.findIndex(e=>e===r);et(a.slice(u+1))?t.preventDefault():o?null==(i=h.current)||i.focus():null==(s=p.current)||s.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,E]),(0,u.jsxs)(y,{ref:f,role:"region","aria-label":s.replace("{hotkey}",b),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&(0,u.jsx)(F,{ref:h,onFocusFromOutsideViewport:()=>{et(E({tabbingDirection:"forwards"}))}}),(0,u.jsx)(R.Slot,{scope:r,children:(0,u.jsx)(d.sG.ol,{tabIndex:-1,...o,ref:m})}),g&&(0,u.jsx)(F,{ref:p,onFocusFromOutsideViewport:()=>{et(E({tabbingDirection:"backwards"}))}})]})});D.displayName=I;var Q="ToastFocusProxy",F=i.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...i}=e,s=N(Q,r);return(0,u.jsx)(C.s,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null==(t=s.viewport)?void 0:t.contains(r))||n()}})});F.displayName=Q;var H="Toast",U=i.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:i,onOpenChange:s,...a}=e,[l=!0,c]=(0,T.i)({prop:n,defaultProp:i,onChange:s});return(0,u.jsx)(w.C,{present:r||l,children:(0,u.jsx)(W,{open:l,...a,ref:t,onClose:()=>c(!1),onPause:(0,f.c)(e.onPause),onResume:(0,f.c)(e.onResume),onSwipeStart:(0,o.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,o.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),c(!1)})})})});U.displayName=H;var[q,B]=S(H,{onClose(){}}),W=i.forwardRef((e,t)=>{let{__scopeToast:r,type:n="foreground",duration:l,open:c,onClose:h,onEscapeKeyDown:p,onPause:y,onResume:m,onSwipeStart:b,onSwipeMove:g,onSwipeCancel:E,onSwipeEnd:w,...T}=e,C=N(H,r),[x,O]=i.useState(null),P=(0,a.s)(t,e=>O(e)),S=i.useRef(null),L=i.useRef(null),A=l||C.duration,j=i.useRef(0),I=i.useRef(A),M=i.useRef(0),{onToastAdd:D,onToastRemove:Q}=C,F=(0,f.c)(()=>{var e;(null==x?void 0:x.contains(document.activeElement))&&(null==(e=C.viewport)||e.focus()),h()}),U=i.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(M.current),j.current=new Date().getTime(),M.current=window.setTimeout(F,e))},[F]);i.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{U(I.current),null==m||m()},r=()=>{let e=new Date().getTime()-j.current;I.current=I.current-e,window.clearTimeout(M.current),null==y||y()};return e.addEventListener(k,r),e.addEventListener(_,t),()=>{e.removeEventListener(k,r),e.removeEventListener(_,t)}}},[C.viewport,A,y,m,U]),i.useEffect(()=>{c&&!C.isClosePausedRef.current&&U(A)},[c,A,C.isClosePausedRef,U]),i.useEffect(()=>(D(),()=>Q()),[D,Q]);let B=i.useMemo(()=>x?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!n)if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}),r}(x):null,[x]);return C.viewport?(0,u.jsxs)(u.Fragment,{children:[B&&(0,u.jsx)($,{__scopeToast:r,role:"status","aria-live":"foreground"===n?"assertive":"polite","aria-atomic":!0,children:B}),(0,u.jsx)(q,{scope:r,onClose:F,children:s.createPortal((0,u.jsx)(R.ItemSlot,{scope:r,children:(0,u.jsx)(v,{asChild:!0,onEscapeKeyDown:(0,o.m)(p,()=>{C.isFocusedToastEscapeKeyDownRef.current||F(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,u.jsx)(d.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":C.swipeDirection,...T,ref:P,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Escape"===e.key&&(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:(0,o.m)(e.onPointerDown,e=>{0===e.button&&(S.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.m)(e.onPointerMove,e=>{if(!S.current)return;let t=e.clientX-S.current.x,r=e.clientY-S.current.y,n=!!L.current,i=["left","right"].includes(C.swipeDirection),s=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,o=i?s(0,t):0,a=i?0:s(0,r),u="touch"===e.pointerType?10:2,l={x:o,y:a},c={originalEvent:e,delta:l};n?(L.current=l,J("toast.swipeMove",g,c,{discrete:!1})):ee(l,C.swipeDirection,u)?(L.current=l,J("toast.swipeStart",b,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(r)>u)&&(S.current=null)}),onPointerUp:(0,o.m)(e.onPointerUp,e=>{let t=L.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),L.current=null,S.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};ee(t,C.swipeDirection,C.swipeThreshold)?J("toast.swipeEnd",w,n,{discrete:!0}):J("toast.swipeCancel",E,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),$=e=>{let{__scopeToast:t,children:r,...n}=e,s=N(H,t),[o,a]=i.useState(!1),[l,c]=i.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,f.c)(e);(0,g.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),i.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,u.jsx)(E,{asChild:!0,children:(0,u.jsx)(C.s,{...n,children:o&&(0,u.jsxs)(u.Fragment,{children:[s.label," ",r]})})})},z=i.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,u.jsx)(d.sG.div,{...n,ref:t})});z.displayName="ToastTitle";var G=i.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,u.jsx)(d.sG.div,{...n,ref:t})});G.displayName="ToastDescription";var K="ToastAction",Y=i.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,u.jsx)(Z,{altText:r,asChild:!0,children:(0,u.jsx)(V,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(K,"`. Expected non-empty `string`.")),null)});Y.displayName=K;var X="ToastClose",V=i.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,i=B(X,r);return(0,u.jsx)(Z,{asChild:!0,children:(0,u.jsx)(d.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,i.onClose)})})});V.displayName=X;var Z=i.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...i}=e;return(0,u.jsx)(d.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...i,ref:t})});function J(e,t,r,n){let{discrete:i}=n,s=r.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),i?(0,d.hO)(s,o):s.dispatchEvent(o)}var ee=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),i=Math.abs(e.y),s=n>i;return"left"===t||"right"===t?s&&n>r:!s&&i>r};function et(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var er=j,en=D,ei=U,es=z,eo=G,ea=Y,eu=V},5554:(e,t,r)=>{"use strict";r.d(t,{v_:()=>l,II:()=>f,wm:()=>d});var n=r(6195),i=r(8445),s=r(4049),o=new class extends i.Q{#m=!0;#b;#g;constructor(){super(),this.#g=e=>{if(!s.S$&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#b||this.setEventListener(this.#g)}onUnsubscribe(){this.hasListeners()||(this.#b?.(),this.#b=void 0)}setEventListener(e){this.#g=e,this.#b?.(),this.#b=e(this.setOnline.bind(this))}setOnline(e){this.#m!==e&&(this.#m=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#m}},a=r(3515);function u(e){return Math.min(1e3*2**e,3e4)}function l(e){return(e??"online")!=="online"||o.isOnline()}var c=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function d(e){return e instanceof c}function f(e){let t,r=!1,i=0,d=!1,f=(0,a.T)(),h=()=>n.m.isFocused()&&("always"===e.networkMode||o.isOnline())&&e.canRun(),p=()=>l(e.networkMode)&&e.canRun(),v=r=>{d||(d=!0,e.onSuccess?.(r),t?.(),f.resolve(r))},y=r=>{d||(d=!0,e.onError?.(r),t?.(),f.reject(r))},m=()=>new Promise(r=>{t=e=>{(d||h())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,d||e.onContinue?.()}),b=()=>{let t;if(d)return;let n=0===i?e.initialPromise:void 0;try{t=n??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(v).catch(t=>{if(d)return;let n=e.retry??3*!s.S$,o=e.retryDelay??u,a="function"==typeof o?o(i,t):o,l=!0===n||"number"==typeof n&&i<n||"function"==typeof n&&n(i,t);if(r||!l)return void y(t);i++,e.onFail?.(i,t),(0,s.yy)(a).then(()=>h()?void 0:m()).then(()=>{r?y(t):b()})})};return{promise:f,cancel:t=>{d||(y(new c(t)),e.abort?.())},continue:()=>(t?.(),f),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:p,start:()=>(p()?b():m().then(b),f)}}},5822:(e,t,r)=>{"use strict";r.d(t,{EU:()=>o,R3:()=>n,iL:()=>a,jv:()=>i,nE:()=>s});var n=(e,t)=>void 0===t.state.data,i=e=>{e.suspense&&(void 0===e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},s=(e,t)=>e.isLoading&&e.isFetching&&!t,o=(e,t)=>e?.suspense&&t.isPending,a=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()})},5947:(e,t,r)=>{"use strict";r.d(t,{useSuspenseQueries:()=>s});var n=r(1515),i=r(5822);function s(e,t){return(0,n.useQueries)({...e,queries:e.queries.map(e=>({...e,suspense:!0,throwOnError:i.R3,enabled:!0,placeholderData:void 0}))},t)}},5979:(e,t,r)=>{"use strict";r.d(t,{useInfiniteQuery:()=>s});var n=r(2798),i=r(6384);function s(e,t){return(0,i.t)(e,n.z,t)}},6195:(e,t,r)=>{"use strict";r.d(t,{m:()=>s});var n=r(8445),i=r(4049),s=new class extends n.Q{#E;#b;#g;constructor(){super(),this.#g=e=>{if(!i.S$&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#b||this.setEventListener(this.#g)}onUnsubscribe(){this.hasListeners()||(this.#b?.(),this.#b=void 0)}setEventListener(e){this.#g=e,this.#b?.(),this.#b=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#E!==e&&(this.#E=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#E?this.#E:globalThis.document?.visibilityState!=="hidden"}}},6384:(e,t,r)=>{"use strict";r.d(t,{t:()=>f});var n=r(2115),i=r(4268),s=r(4049),o=r(9776),a=r(7785),u=r(6687),l=r(4019),c=r(5822),d=r(2211);function f(e,t,r){var f,h,p,v,y;let m=(0,o.useQueryClient)(r),b=(0,l.useIsRestoring)(),g=(0,a.useQueryErrorResetBoundary)(),E=m.defaultQueryOptions(e);null==(h=m.getDefaultOptions().queries)||null==(f=h._experimental_beforeQuery)||f.call(h,E),E._optimisticResults=b?"isRestoring":"optimistic",(0,c.jv)(E),(0,u.LJ)(E,g),(0,u.wZ)(g);let w=!m.getQueryCache().get(E.queryHash),[T]=n.useState(()=>new t(m,E)),C=T.getOptimisticResult(E);if(n.useSyncExternalStore(n.useCallback(e=>{let t=b?()=>void 0:T.subscribe(i.j.batchCalls(e));return T.updateResult(),t},[T,b]),()=>T.getCurrentResult(),()=>T.getCurrentResult()),n.useEffect(()=>{T.setOptions(E,{listeners:!1})},[E,T]),(0,c.EU)(E,C))throw(0,c.iL)(E,T,g);if((0,u.$1)({result:C,errorResetBoundary:g,throwOnError:E.throwOnError,query:m.getQueryCache().get(E.queryHash)}))throw C.error;if(null==(v=m.getDefaultOptions().queries)||null==(p=v._experimental_afterQuery)||p.call(v,E,C),E.experimental_prefetchInRender&&!s.S$&&(0,c.nE)(C,b)){let e=w?(0,c.iL)(E,T,g):null==(y=m.getQueryCache().get(E.queryHash))?void 0:y.promise;null==e||e.catch(d.l).finally(()=>{T.updateResult()})}return E.notifyOnChangeProps?C:T.trackResult(C)}},6604:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var n=r(6195),i=r(4268),s=r(4049),o=r(5554),a=r(2162);a.k;var u=r(8445),l=r(3515),c=class extends u.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#w=null,this.#T=(0,l.T)(),this.options.experimental_prefetchInRender||this.#T.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#C=void 0;#x=void 0;#t=void 0;#R;#O;#T;#w;#P;#S;#L;#A;#N;#j;#I=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#C.addObserver(this),d(this.#C,this.options)?this.#M():this.updateResult(),this.#k())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return f(this.#C,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return f(this.#C,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#_(),this.#D(),this.#C.removeObserver(this)}setOptions(e,t){let r=this.options,n=this.#C;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,s.Eh)(this.options.enabled,this.#C))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#Q(),this.#C.setOptions(this.options),r._defaulted&&!(0,s.f8)(this.options,r)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#C,observer:this});let i=this.hasListeners();i&&h(this.#C,n,this.options,r)&&this.#M(),this.updateResult(t),i&&(this.#C!==n||(0,s.Eh)(this.options.enabled,this.#C)!==(0,s.Eh)(r.enabled,this.#C)||(0,s.d2)(this.options.staleTime,this.#C)!==(0,s.d2)(r.staleTime,this.#C))&&this.#F();let o=this.#H();i&&(this.#C!==n||(0,s.Eh)(this.options.enabled,this.#C)!==(0,s.Eh)(r.enabled,this.#C)||o!==this.#j)&&this.#U(o)}getOptimisticResult(e){var t,r;let n=this.#e.getQueryCache().build(this.#e,e),i=this.createResult(n,e);return t=this,r=i,(0,s.f8)(t.getCurrentResult(),r)||(this.#t=i,this.#O=this.options,this.#R=this.#C.state),i}getCurrentResult(){return this.#t}trackResult(e,t){let r={};return Object.keys(e).forEach(n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})}),r}trackProp(e){this.#I.add(e)}getCurrentQuery(){return this.#C}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#M({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#t))}#M(e){this.#Q();let t=this.#C.fetch(this.options,e);return e?.throwOnError||(t=t.catch(s.lQ)),t}#F(){this.#_();let e=(0,s.d2)(this.options.staleTime,this.#C);if(s.S$||this.#t.isStale||!(0,s.gn)(e))return;let t=(0,s.j3)(this.#t.dataUpdatedAt,e);this.#A=setTimeout(()=>{this.#t.isStale||this.updateResult()},t+1)}#H(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#C):this.options.refetchInterval)??!1}#U(e){this.#D(),this.#j=e,!s.S$&&!1!==(0,s.Eh)(this.options.enabled,this.#C)&&(0,s.gn)(this.#j)&&0!==this.#j&&(this.#N=setInterval(()=>{(this.options.refetchIntervalInBackground||n.m.isFocused())&&this.#M()},this.#j))}#k(){this.#F(),this.#U(this.#H())}#_(){this.#A&&(clearTimeout(this.#A),this.#A=void 0)}#D(){this.#N&&(clearInterval(this.#N),this.#N=void 0)}createResult(e,t){let r,n=this.#C,i=this.options,a=this.#t,u=this.#R,c=this.#O,f=e!==n?e.state:this.#x,{state:v}=e,y={...v},m=!1;if(t._optimisticResults){var b,g;let r=this.hasListeners(),s=!r&&d(e,t),a=r&&h(e,n,t,i);(s||a)&&(y={...y,...(b=v.data,g=e.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,o.v_)(g.networkMode)?"fetching":"paused",...void 0===b&&{error:null,status:"pending"}})}),"isRestoring"===t._optimisticResults&&(y.fetchStatus="idle")}let{error:E,errorUpdatedAt:w,status:T}=y;if(t.select&&void 0!==y.data)if(a&&y.data===u?.data&&t.select===this.#P)r=this.#S;else try{this.#P=t.select,r=t.select(y.data),r=(0,s.pl)(a?.data,r,t),this.#S=r,this.#w=null}catch(e){this.#w=e}else r=y.data;if(void 0!==t.placeholderData&&void 0===r&&"pending"===T){let e;if(a?.isPlaceholderData&&t.placeholderData===c?.placeholderData)e=a.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#L?.state.data,this.#L):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#w=null}catch(e){this.#w=e}void 0!==e&&(T="success",r=(0,s.pl)(a?.data,e,t),m=!0)}this.#w&&(E=this.#w,r=this.#S,w=Date.now(),T="error");let C="fetching"===y.fetchStatus,x="pending"===T,R="error"===T,O=x&&C,P=void 0!==r,S={status:T,fetchStatus:y.fetchStatus,isPending:x,isSuccess:"success"===T,isError:R,isInitialLoading:O,isLoading:O,data:r,dataUpdatedAt:y.dataUpdatedAt,error:E,errorUpdatedAt:w,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>f.dataUpdateCount||y.errorUpdateCount>f.errorUpdateCount,isFetching:C,isRefetching:C&&!x,isLoadingError:R&&!P,isPaused:"paused"===y.fetchStatus,isPlaceholderData:m,isRefetchError:R&&P,isStale:p(e,t),refetch:this.refetch,promise:this.#T};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===S.status?e.reject(S.error):void 0!==S.data&&e.resolve(S.data)},r=()=>{t(this.#T=S.promise=(0,l.T)())},i=this.#T;switch(i.status){case"pending":e.queryHash===n.queryHash&&t(i);break;case"fulfilled":("error"===S.status||S.data!==i.value)&&r();break;case"rejected":("error"!==S.status||S.error!==i.reason)&&r()}}return S}updateResult(e){let t=this.#t,r=this.createResult(this.#C,this.options);if(this.#R=this.#C.state,this.#O=this.options,void 0!==this.#R.data&&(this.#L=this.#C),(0,s.f8)(r,t))return;this.#t=r;let n={},i=()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#I.size)return!0;let n=new Set(r??this.#I);return this.options.throwOnError&&n.add("error"),Object.keys(this.#t).some(e=>this.#t[e]!==t[e]&&n.has(e))};e?.listeners!==!1&&i()&&(n.listeners=!0),this.#s({...n,...e})}#Q(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#C)return;let t=this.#C;this.#C=e,this.#x=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#k()}#s(e){i.j.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#t)}),this.#e.getQueryCache().notify({query:this.#C,type:"observerResultsUpdated"})})}};function d(e,t){return!1!==(0,s.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&f(e,t,t.refetchOnMount)}function f(e,t,r){if(!1!==(0,s.Eh)(t.enabled,e)){let n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&p(e,t)}return!1}function h(e,t,r,n){return(e!==t||!1===(0,s.Eh)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,s.Eh)(t.enabled,e)&&e.isStaleByTime((0,s.d2)(t.staleTime,e))}},6639:(e,t,r)=>{"use strict";r.d(t,{useSuspenseQuery:()=>o});var n=r(6604),i=r(6384),s=r(5822);function o(e,t){return(0,i.t)({...e,enabled:!0,suspense:!0,throwOnError:s.R3,placeholderData:void 0},n.$,t)}},6687:(e,t,r)=>{"use strict";r.d(t,{$1:()=>a,LJ:()=>s,wZ:()=>o});var n=r(2115),i=r(2211),s=(e,t)=>{(e.suspense||e.throwOnError)&&!t.isReset()&&(e.retryOnMount=!1)},o=e=>{n.useEffect(()=>{e.clearReset()},[e])},a=e=>{let{result:t,errorResetBoundary:r,throwOnError:n,query:s}=e;return t.isError&&!r.isReset()&&!t.isFetching&&s&&(0,i.G)(n,[t.error,s])}},6697:e=>{var t="undefined"!=typeof Element,r="function"==typeof Map,n="function"==typeof Set,i="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;e.exports=function(e,s){try{return function e(s,o){if(s===o)return!0;if(s&&o&&"object"==typeof s&&"object"==typeof o){var a,u,l,c;if(s.constructor!==o.constructor)return!1;if(Array.isArray(s)){if((a=s.length)!=o.length)return!1;for(u=a;0!=u--;)if(!e(s[u],o[u]))return!1;return!0}if(r&&s instanceof Map&&o instanceof Map){if(s.size!==o.size)return!1;for(c=s.entries();!(u=c.next()).done;)if(!o.has(u.value[0]))return!1;for(c=s.entries();!(u=c.next()).done;)if(!e(u.value[1],o.get(u.value[0])))return!1;return!0}if(n&&s instanceof Set&&o instanceof Set){if(s.size!==o.size)return!1;for(c=s.entries();!(u=c.next()).done;)if(!o.has(u.value[0]))return!1;return!0}if(i&&ArrayBuffer.isView(s)&&ArrayBuffer.isView(o)){if((a=s.length)!=o.length)return!1;for(u=a;0!=u--;)if(s[u]!==o[u])return!1;return!0}if(s.constructor===RegExp)return s.source===o.source&&s.flags===o.flags;if(s.valueOf!==Object.prototype.valueOf&&"function"==typeof s.valueOf&&"function"==typeof o.valueOf)return s.valueOf()===o.valueOf();if(s.toString!==Object.prototype.toString&&"function"==typeof s.toString&&"function"==typeof o.toString)return s.toString()===o.toString();if((a=(l=Object.keys(s)).length)!==Object.keys(o).length)return!1;for(u=a;0!=u--;)if(!Object.prototype.hasOwnProperty.call(o,l[u]))return!1;if(t&&s instanceof Element)return!1;for(u=a;0!=u--;)if(("_owner"!==l[u]&&"__v"!==l[u]&&"__o"!==l[u]||!s.$$typeof)&&!e(s[l[u]],o[l[u]]))return!1;return!0}return s!=s&&o!=o}(e,s)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}}},6842:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(2115),i=r(4446),s=r(4129),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[i,o]=n.useState(),u=n.useRef({}),l=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=a(u.current);c.current="mounted"===d?e:"none"},[d]),(0,s.N)(()=>{let t=u.current,r=l.current;if(r!==e){let n=c.current,i=a(t);e?f("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,s.N)(()=>{if(i){var e;let t,r=null!=(e=i.ownerDocument.defaultView)?e:window,n=e=>{let n=a(u.current).includes(e.animationName);if(e.target===i&&n&&(f("ANIMATION_END"),!l.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},s=e=>{e.target===i&&(c.current=a(u.current))};return i.addEventListener("animationstart",s),i.addEventListener("animationcancel",n),i.addEventListener("animationend",n),()=>{r.clearTimeout(t),i.removeEventListener("animationstart",s),i.removeEventListener("animationcancel",n),i.removeEventListener("animationend",n)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(u.current=getComputedStyle(e)),o(e)},[])}}(t),u="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),l=(0,i.s)(o.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=n&&"isReactWarning"in n&&n.isReactWarning;return i?e.ref:(i=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof r||o.isPresent?n.cloneElement(u,{ref:l}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},7785:(e,t,r)=>{"use strict";r.d(t,{QueryErrorResetBoundary:()=>u,useQueryErrorResetBoundary:()=>a});var n=r(2115),i=r(5155);function s(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var o=n.createContext(s()),a=()=>n.useContext(o),u=e=>{let{children:t}=e,[r]=n.useState(()=>s());return(0,i.jsx)(o.Provider,{value:r,children:"function"==typeof t?t(r):t})}},8194:(e,t,r)=>{"use strict";r.d(t,{Content:()=>eb,Provider:()=>ev,Root:()=>ey,Trigger:()=>em});var n,i=r(2115),s=r.t(i,2),o=r(2556),a=r(4446),u=r(3468),l=r(7602),c=r(222),d=r(5155),f="dismissableLayer.update",h=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=i.forwardRef((e,t)=>{var r,s;let{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:b,onInteractOutside:g,onDismiss:E,...w}=e,T=i.useContext(h),[C,x]=i.useState(null),R=null!=(s=null==C?void 0:C.ownerDocument)?s:null==(r=globalThis)?void 0:r.document,[,O]=i.useState({}),P=(0,a.s)(t,e=>x(e)),S=Array.from(T.layers),[L]=[...T.layersWithOutsidePointerEventsDisabled].slice(-1),A=S.indexOf(L),N=C?S.indexOf(C):-1,j=T.layersWithOutsidePointerEventsDisabled.size>0,I=N>=A,M=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,c.c)(e),s=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!s.current){let t=function(){y("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",o.current),o.current=t,r.addEventListener("click",o.current,{once:!0})):t()}else r.removeEventListener("click",o.current);s.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",o.current)}},[r,n]),{onPointerDownCapture:()=>s.current=!0}}(e=>{let t=e.target,r=[...T.branches].some(e=>e.contains(t));I&&!r&&(null==m||m(e),null==g||g(e),e.defaultPrevented||null==E||E())},R),k=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,c.c)(e),s=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!s.current&&y("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(e=>{let t=e.target;![...T.branches].some(e=>e.contains(t))&&(null==b||b(e),null==g||g(e),e.defaultPrevented||null==E||E())},R);return!function(e,t=globalThis?.document){let r=(0,c.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{N===T.layers.size-1&&(null==p||p(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},R),i.useEffect(()=>{if(C)return u&&(0===T.layersWithOutsidePointerEventsDisabled.size&&(n=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),T.layersWithOutsidePointerEventsDisabled.add(C)),T.layers.add(C),v(),()=>{u&&1===T.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=n)}},[C,R,u,T]),i.useEffect(()=>()=>{C&&(T.layers.delete(C),T.layersWithOutsidePointerEventsDisabled.delete(C),v())},[C,T]),i.useEffect(()=>{let e=()=>O({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,d.jsx)(l.sG.div,{...w,ref:P,style:{pointerEvents:j?I?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});function v(){let e=new CustomEvent(f);document.dispatchEvent(e)}function y(e,t,r,n){let{discrete:i}=n,s=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),i?(0,l.hO)(s,o):s.dispatchEvent(o)}p.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(h),n=i.useRef(null),s=(0,a.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,d.jsx)(l.sG.div,{...e,ref:s})}).displayName="DismissableLayerBranch";var m=r(4129),b=s["useId".toString()]||(()=>void 0),g=0,E=r(8334),w=r(8146),T=i.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...s}=e;return(0,d.jsx)(l.sG.svg,{...s,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});T.displayName="Arrow";var C="Popper",[x,R]=function(e,t=[]){let r=[],n=()=>{let t=r.map(e=>i.createContext(e));return function(r){let n=r?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let s=i.createContext(n),o=r.length;function a(t){let{scope:r,children:n,...a}=t,u=r?.[e][o]||s,l=i.useMemo(()=>a,Object.values(a));return(0,d.jsx)(u.Provider,{value:l,children:n})}return r=[...r,n],a.displayName=t+"Provider",[a,function(r,a){let u=a?.[e][o]||s,l=i.useContext(u);if(l)return l;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}(C),[O,P]=x(C),S=e=>{let{__scopePopper:t,children:r}=e,[n,s]=i.useState(null);return(0,d.jsx)(O,{scope:t,anchor:n,onAnchorChange:s,children:r})};S.displayName=C;var L="PopperAnchor",A=i.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...s}=e,o=P(L,r),u=i.useRef(null),c=(0,a.s)(t,u);return i.useEffect(()=>{o.onAnchorChange((null==n?void 0:n.current)||u.current)}),n?null:(0,d.jsx)(l.sG.div,{...s,ref:c})});A.displayName=L;var N="PopperContent",[j,I]=x(N),M=i.forwardRef((e,t)=>{var r,n,s,o,u,f,h,p;let{__scopePopper:v,side:y="bottom",sideOffset:b=0,align:g="center",alignOffset:T=0,arrowPadding:C=0,avoidCollisions:x=!0,collisionBoundary:R=[],collisionPadding:O=0,sticky:S="partial",hideWhenDetached:L=!1,updatePositionStrategy:A="optimized",onPlaced:I,...M}=e,k=P(N,v),[_,D]=i.useState(null),U=(0,a.s)(t,e=>D(e)),[q,B]=i.useState(null),W=function(e){let[t,r]=i.useState(void 0);return(0,m.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(q),$=null!=(h=null==W?void 0:W.width)?h:0,z=null!=(p=null==W?void 0:W.height)?p:0,G="number"==typeof O?O:{top:0,right:0,bottom:0,left:0,...O},K=Array.isArray(R)?R:[R],Y=K.length>0,X={padding:G,boundary:K.filter(Q),altBoundary:Y},{refs:V,floatingStyles:Z,placement:J,isPositioned:ee,middlewareData:et}=(0,E.we)({strategy:"fixed",placement:y+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,w.ll)(...t,{animationFrame:"always"===A})},elements:{reference:k.anchor},middleware:[(0,E.cY)({mainAxis:b+z,alignmentAxis:T}),x&&(0,E.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?(0,E.ER)():void 0,...X}),x&&(0,E.UU)({...X}),(0,E.Ej)({...X,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:i}=e,{width:s,height:o}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(s,"px")),a.setProperty("--radix-popper-anchor-height","".concat(o,"px"))}}),q&&(0,E.UE)({element:q,padding:C}),F({arrowWidth:$,arrowHeight:z}),L&&(0,E.jD)({strategy:"referenceHidden",...X})]}),[er,en]=H(J),ei=(0,c.c)(I);(0,m.N)(()=>{ee&&(null==ei||ei())},[ee,ei]);let es=null==(r=et.arrow)?void 0:r.x,eo=null==(n=et.arrow)?void 0:n.y,ea=(null==(s=et.arrow)?void 0:s.centerOffset)!==0,[eu,el]=i.useState();return(0,m.N)(()=>{_&&el(window.getComputedStyle(_).zIndex)},[_]),(0,d.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...Z,transform:ee?Z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null==(o=et.transformOrigin)?void 0:o.x,null==(u=et.transformOrigin)?void 0:u.y].join(" "),...(null==(f=et.hide)?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(j,{scope:v,placedSide:er,onArrowChange:B,arrowX:es,arrowY:eo,shouldHideArrow:ea,children:(0,d.jsx)(l.sG.div,{"data-side":er,"data-align":en,...M,ref:U,style:{...M.style,animation:ee?void 0:"none"}})})})});M.displayName=N;var k="PopperArrow",_={top:"bottom",right:"left",bottom:"top",left:"right"},D=i.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=I(k,r),s=_[i.placedSide];return(0,d.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(T,{...n,ref:t,style:{...n.style,display:"block"}})})});function Q(e){return null!==e}D.displayName=k;var F=e=>({name:"transformOrigin",options:e,fn(t){var r,n,i,s,o;let{placement:a,rects:u,middlewareData:l}=t,c=(null==(r=l.arrow)?void 0:r.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[h,p]=H(a),v={start:"0%",center:"50%",end:"100%"}[p],y=(null!=(s=null==(n=l.arrow)?void 0:n.x)?s:0)+d/2,m=(null!=(o=null==(i=l.arrow)?void 0:i.y)?o:0)+f/2,b="",g="";return"bottom"===h?(b=c?v:"".concat(y,"px"),g="".concat(-f,"px")):"top"===h?(b=c?v:"".concat(y,"px"),g="".concat(u.floating.height+f,"px")):"right"===h?(b="".concat(-f,"px"),g=c?v:"".concat(m,"px")):"left"===h&&(b="".concat(u.floating.width+f,"px"),g=c?v:"".concat(m,"px")),{data:{x:b,y:g}}}});function H(e){let[t,r="center"]=e.split("-");return[t,r]}var U=r(7650);i.forwardRef((e,t)=>{var r,n;let{container:s,...o}=e,[a,u]=i.useState(!1);(0,m.N)(()=>u(!0),[]);let c=s||a&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return c?U.createPortal((0,d.jsx)(l.sG.div,{...o,ref:t}),c):null}).displayName="Portal";var q=r(6842),B=r(2467),W=r(3558),$=r(861),[z,G]=(0,u.A)("Tooltip",[R]),K=R(),Y="TooltipProvider",X="tooltip.open",[V,Z]=z(Y),J=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:n=300,disableHoverableContent:s=!1,children:o}=e,[a,u]=i.useState(!0),l=i.useRef(!1),c=i.useRef(0);return i.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,d.jsx)(V,{scope:t,isOpenDelayed:a,delayDuration:r,onOpen:i.useCallback(()=>{window.clearTimeout(c.current),u(!1)},[]),onClose:i.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>u(!0),n)},[n]),isPointerInTransitRef:l,onPointerInTransitChange:i.useCallback(e=>{l.current=e},[]),disableHoverableContent:s,children:o})};J.displayName=Y;var ee="Tooltip",[et,er]=z(ee),en=e=>{let{__scopeTooltip:t,children:r,open:n,defaultOpen:s=!1,onOpenChange:o,disableHoverableContent:a,delayDuration:u}=e,l=Z(ee,e.__scopeTooltip),c=K(t),[f,h]=i.useState(null),p=function(e){let[t,r]=i.useState(b());return(0,m.N)(()=>{r(e=>e??String(g++))},[void 0]),e||(t?`radix-${t}`:"")}(),v=i.useRef(0),y=null!=a?a:l.disableHoverableContent,E=null!=u?u:l.delayDuration,w=i.useRef(!1),[T=!1,C]=(0,W.i)({prop:n,defaultProp:s,onChange:e=>{e?(l.onOpen(),document.dispatchEvent(new CustomEvent(X))):l.onClose(),null==o||o(e)}}),x=i.useMemo(()=>T?w.current?"delayed-open":"instant-open":"closed",[T]),R=i.useCallback(()=>{window.clearTimeout(v.current),v.current=0,w.current=!1,C(!0)},[C]),O=i.useCallback(()=>{window.clearTimeout(v.current),v.current=0,C(!1)},[C]),P=i.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{w.current=!0,C(!0),v.current=0},E)},[E,C]);return i.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,d.jsx)(S,{...c,children:(0,d.jsx)(et,{scope:t,contentId:p,open:T,stateAttribute:x,trigger:f,onTriggerChange:h,onTriggerEnter:i.useCallback(()=>{l.isOpenDelayed?P():R()},[l.isOpenDelayed,P,R]),onTriggerLeave:i.useCallback(()=>{y?O():(window.clearTimeout(v.current),v.current=0)},[O,y]),onOpen:R,onClose:O,disableHoverableContent:y,children:r})})};en.displayName=ee;var ei="TooltipTrigger",es=i.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,s=er(ei,r),u=Z(ei,r),c=K(r),f=i.useRef(null),h=(0,a.s)(t,f,s.onTriggerChange),p=i.useRef(!1),v=i.useRef(!1),y=i.useCallback(()=>p.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,d.jsx)(A,{asChild:!0,...c,children:(0,d.jsx)(l.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...n,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||u.isPointerInTransitRef.current||(s.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{p.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{p.current||s.onOpen()}),onBlur:(0,o.m)(e.onBlur,s.onClose),onClick:(0,o.m)(e.onClick,s.onClose)})})});es.displayName=ei;var[eo,ea]=z("TooltipPortal",{forceMount:void 0}),eu="TooltipContent",el=i.forwardRef((e,t)=>{let r=ea(eu,e.__scopeTooltip),{forceMount:n=r.forceMount,side:i="top",...s}=e,o=er(eu,e.__scopeTooltip);return(0,d.jsx)(q.C,{present:n||o.open,children:o.disableHoverableContent?(0,d.jsx)(eh,{side:i,...s,ref:t}):(0,d.jsx)(ec,{side:i,...s,ref:t})})}),ec=i.forwardRef((e,t)=>{let r=er(eu,e.__scopeTooltip),n=Z(eu,e.__scopeTooltip),s=i.useRef(null),o=(0,a.s)(t,s),[u,l]=i.useState(null),{trigger:c,onClose:f}=r,h=s.current,{onPointerInTransitChange:p}=n,v=i.useCallback(()=>{l(null),p(!1)},[p]),y=i.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},i=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(r,n,i,s)){case s:return"left";case i:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());l(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,i),...function(e){let{top:t,right:r,bottom:n,left:i}=e;return[{x:i,y:t},{x:r,y:t},{x:r,y:n},{x:i,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return i.useEffect(()=>()=>v(),[v]),i.useEffect(()=>{if(c&&h){let e=e=>y(e,h),t=e=>y(e,c);return c.addEventListener("pointerleave",e),h.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),h.removeEventListener("pointerleave",t)}}},[c,h,y,v]),i.useEffect(()=>{if(u){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==h?void 0:h.contains(t)),i=!function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,s=t.length-1;e<t.length;s=e++){let o=t[e].x,a=t[e].y,u=t[s].x,l=t[s].y;a>n!=l>n&&r<(u-o)*(n-a)/(l-a)+o&&(i=!i)}return i}(r,u);n?v():i&&(v(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,h,u,f,v]),(0,d.jsx)(eh,{...e,ref:o})}),[ed,ef]=z(ee,{isInside:!1}),eh=i.forwardRef((e,t)=>{let{__scopeTooltip:r,children:n,"aria-label":s,onEscapeKeyDown:o,onPointerDownOutside:a,...u}=e,l=er(eu,r),c=K(r),{onClose:f}=l;return i.useEffect(()=>(document.addEventListener(X,f),()=>document.removeEventListener(X,f)),[f]),i.useEffect(()=>{if(l.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(l.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[l.trigger,f]),(0,d.jsx)(p,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,d.jsxs)(M,{"data-state":l.stateAttribute,...c,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,d.jsx)(B.xV,{children:n}),(0,d.jsx)(ed,{scope:r,isInside:!0,children:(0,d.jsx)($.b,{id:l.contentId,role:"tooltip",children:s||n})})]})})});el.displayName=eu;var ep="TooltipArrow";i.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,i=K(r);return ef(ep,r).isInside?null:(0,d.jsx)(D,{...i,...n,ref:t})}).displayName=ep;var ev=J,ey=en,em=es,eb=el},8298:(e,t,r)=>{"use strict";r.d(t,{HydrationBoundary:()=>a});var n=r(2115);function i(e){return e}function s(e,t,r){if("object"!=typeof t||null===t)return;let n=e.getMutationCache(),s=e.getQueryCache(),o=r?.defaultOptions?.deserializeData??e.getDefaultOptions().hydrate?.deserializeData??i,a=t.mutations||[],u=t.queries||[];a.forEach(({state:t,...i})=>{n.build(e,{...e.getDefaultOptions().hydrate?.mutations,...r?.defaultOptions?.mutations,...i},t)}),u.forEach(({queryKey:t,state:n,queryHash:i,meta:a,promise:u})=>{let l=s.get(i),c=void 0===n.data?n.data:o(n.data);if(l){if(l.state.dataUpdatedAt<n.dataUpdatedAt){let{fetchStatus:e,...t}=n;l.setState({...t,data:c})}}else l=s.build(e,{...e.getDefaultOptions().hydrate?.queries,...r?.defaultOptions?.queries,queryKey:t,queryHash:i,meta:a},{...n,data:c,fetchStatus:"idle"});if(u){let e=Promise.resolve(u).then(o);l.fetch(void 0,{initialPromise:e})}})}var o=r(9776),a=e=>{let{children:t,options:r={},state:i,queryClient:a}=e,u=(0,o.useQueryClient)(a),[l,c]=n.useState(),d=n.useRef(r);return d.current=r,n.useMemo(()=>{if(i){if("object"!=typeof i)return;let e=u.getQueryCache(),t=i.queries||[],r=[],n=[];for(let i of t){let t=e.get(i.queryHash);if(t){let e=i.state.dataUpdatedAt>t.state.dataUpdatedAt,r=null==l?void 0:l.find(e=>e.queryHash===i.queryHash);e&&(!r||i.state.dataUpdatedAt>r.state.dataUpdatedAt)&&n.push(i)}else r.push(i)}r.length>0&&s(u,{queries:r},d.current),n.length>0&&c(e=>e?[...e,...n]:n)}},[u,l,i]),n.useEffect(()=>{l&&(s(u,{queries:l},d.current),c(void 0))},[u,l]),t}},8314:e=>{e.exports={style:{fontFamily:"'Libre Baskerville', 'Libre Baskerville Fallback'"},className:"__className_e40923",variable:"__variable_e40923"}},8445:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},9056:(e,t,r)=>{"use strict";r.d(t,{m:()=>X});var n=r(2758),i=r.n(n),s=r(3651),o=r.n(s),a=r(6697),u=r.n(a),l=r(2115),c=r(4766),d=r.n(c),f={BODY:"bodyAttributes",HTML:"htmlAttributes",TITLE:"titleAttributes"},h={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title"};Object.keys(h).map(function(e){return h[e]});var p={CHARSET:"charset",CSS_TEXT:"cssText",HREF:"href",HTTPEQUIV:"http-equiv",INNER_HTML:"innerHTML",ITEM_PROP:"itemprop",NAME:"name",PROPERTY:"property",REL:"rel",SRC:"src",TARGET:"target"},v={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},y={DEFAULT_TITLE:"defaultTitle",DEFER:"defer",ENCODE_SPECIAL_CHARACTERS:"encodeSpecialCharacters",ON_CHANGE_CLIENT_STATE:"onChangeClientState",TITLE_TEMPLATE:"titleTemplate"},m=Object.keys(v).reduce(function(e,t){return e[v[t]]=t,e},{}),b=[h.NOSCRIPT,h.SCRIPT,h.STYLE],g="data-react-helmet",E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},T=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),C=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},x=function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},R=function(e,t){var r={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r},O=function(e,t){if(!e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e},P=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},S=function(e){var t=N(e,h.TITLE),r=N(e,y.TITLE_TEMPLATE);if(r&&t)return r.replace(/%s/g,function(){return Array.isArray(t)?t.join(""):t});var n=N(e,y.DEFAULT_TITLE);return t||n||void 0},L=function(e,t){return t.filter(function(t){return void 0!==t[e]}).map(function(t){return t[e]}).reduce(function(e,t){return C({},e,t)},{})},A=function(e,t,r){var n={};return r.filter(function(t){return!!Array.isArray(t[e])||(void 0!==t[e]&&_("Helmet: "+e+' should be of type "Array". Instead found type "'+E(t[e])+'"'),!1)}).map(function(t){return t[e]}).reverse().reduce(function(e,r){var i={};r.filter(function(e){for(var r=void 0,s=Object.keys(e),o=0;o<s.length;o++){var a=s[o],u=a.toLowerCase();-1!==t.indexOf(u)&&(r!==p.REL||"canonical"!==e[r].toLowerCase())&&(u!==p.REL||"stylesheet"!==e[u].toLowerCase())&&(r=u),-1!==t.indexOf(a)&&(a===p.INNER_HTML||a===p.CSS_TEXT||a===p.ITEM_PROP)&&(r=a)}if(!r||!e[r])return!1;var l=e[r].toLowerCase();return n[r]||(n[r]={}),i[r]||(i[r]={}),!n[r][l]&&(i[r][l]=!0,!0)}).reverse().forEach(function(t){return e.push(t)});for(var s=Object.keys(i),o=0;o<s.length;o++){var a=s[o],u=d()({},n[a],i[a]);n[a]=u}return e},[]).reverse()},N=function(e,t){for(var r=e.length-1;r>=0;r--){var n=e[r];if(n.hasOwnProperty(t))return n[t]}return null},j=function(){var e=Date.now();return function(t){var r=Date.now();r-e>16?(e=r,t(r)):setTimeout(function(){j(t)},0)}}(),I=function(e){return clearTimeout(e)},M="undefined"!=typeof window?window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||j:r.g.requestAnimationFrame||j,k="undefined"!=typeof window?window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||I:r.g.cancelAnimationFrame||I,_=function(e){return console&&"function"==typeof console.warn&&console.warn(e)},D=null,Q=function(e,t){var r=e.baseTag,n=e.bodyAttributes,i=e.htmlAttributes,s=e.linkTags,o=e.metaTags,a=e.noscriptTags,u=e.onChangeClientState,l=e.scriptTags,c=e.styleTags,d=e.title,f=e.titleAttributes;U(h.BODY,n),U(h.HTML,i),H(d,f);var p={baseTag:q(h.BASE,r),linkTags:q(h.LINK,s),metaTags:q(h.META,o),noscriptTags:q(h.NOSCRIPT,a),scriptTags:q(h.SCRIPT,l),styleTags:q(h.STYLE,c)},v={},y={};Object.keys(p).forEach(function(e){var t=p[e],r=t.newTags,n=t.oldTags;r.length&&(v[e]=r),n.length&&(y[e]=p[e].oldTags)}),t&&t(),u(e,v,y)},F=function(e){return Array.isArray(e)?e.join(""):e},H=function(e,t){void 0!==e&&document.title!==e&&(document.title=F(e)),U(h.TITLE,t)},U=function(e,t){var r=document.getElementsByTagName(e)[0];if(r){for(var n=r.getAttribute(g),i=n?n.split(","):[],s=[].concat(i),o=Object.keys(t),a=0;a<o.length;a++){var u=o[a],l=t[u]||"";r.getAttribute(u)!==l&&r.setAttribute(u,l),-1===i.indexOf(u)&&i.push(u);var c=s.indexOf(u);-1!==c&&s.splice(c,1)}for(var d=s.length-1;d>=0;d--)r.removeAttribute(s[d]);i.length===s.length?r.removeAttribute(g):r.getAttribute(g)!==o.join(",")&&r.setAttribute(g,o.join(","))}},q=function(e,t){var r=document.head||document.querySelector(h.HEAD),n=r.querySelectorAll(e+"["+g+"]"),i=Array.prototype.slice.call(n),s=[],o=void 0;return t&&t.length&&t.forEach(function(t){var r=document.createElement(e);for(var n in t)if(t.hasOwnProperty(n))if(n===p.INNER_HTML)r.innerHTML=t.innerHTML;else if(n===p.CSS_TEXT)r.styleSheet?r.styleSheet.cssText=t.cssText:r.appendChild(document.createTextNode(t.cssText));else{var a=void 0===t[n]?"":t[n];r.setAttribute(n,a)}r.setAttribute(g,"true"),i.some(function(e,t){return o=t,r.isEqualNode(e)})?i.splice(o,1):s.push(r)}),i.forEach(function(e){return e.parentNode.removeChild(e)}),s.forEach(function(e){return r.appendChild(e)}),{oldTags:i,newTags:s}},B=function(e){return Object.keys(e).reduce(function(t,r){var n=void 0!==e[r]?r+'="'+e[r]+'"':""+r;return t?t+" "+n:n},"")},W=function(e,t,r,n){var i=B(r),s=F(t);return i?"<"+e+" "+g+'="true" '+i+">"+P(s,n)+"</"+e+">":"<"+e+" "+g+'="true">'+P(s,n)+"</"+e+">"},$=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.keys(e).reduce(function(t,r){return t[v[r]||r]=e[r],t},t)},z=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.keys(e).reduce(function(t,r){return t[m[r]||r]=e[r],t},t)},G=function(e,t,r){var n,i=$(r,((n={key:t})[g]=!0,n));return[l.createElement(h.TITLE,i,t)]},K=function(e,t,r){switch(e){case h.TITLE:return{toComponent:function(){return G(e,t.title,t.titleAttributes,r)},toString:function(){return W(e,t.title,t.titleAttributes,r)}};case f.BODY:case f.HTML:return{toComponent:function(){return $(t)},toString:function(){return B(t)}};default:return{toComponent:function(){return t.map(function(t,r){var n,i=((n={key:r})[g]=!0,n);return Object.keys(t).forEach(function(e){var r=v[e]||e;r===p.INNER_HTML||r===p.CSS_TEXT?i.dangerouslySetInnerHTML={__html:t.innerHTML||t.cssText}:i[r]=t[e]}),l.createElement(e,i)})},toString:function(){return t.reduce(function(t,n){var i=Object.keys(n).filter(function(e){return e!==p.INNER_HTML&&e!==p.CSS_TEXT}).reduce(function(e,t){var i=void 0===n[t]?t:t+'="'+P(n[t],r)+'"';return e?e+" "+i:i},""),s=n.innerHTML||n.cssText||"",o=-1===b.indexOf(e);return t+"<"+e+" "+g+'="true" '+i+(o?"/>":">"+s+"</"+e+">")},"")}}}},Y=function(e){var t=e.baseTag,r=e.bodyAttributes,n=e.encode,i=e.htmlAttributes,s=e.linkTags,o=e.metaTags,a=e.noscriptTags,u=e.scriptTags,l=e.styleTags,c=e.title,d=e.titleAttributes;return{base:K(h.BASE,t,n),bodyAttributes:K(f.BODY,r,n),htmlAttributes:K(f.HTML,i,n),link:K(h.LINK,s,n),meta:K(h.META,o,n),noscript:K(h.NOSCRIPT,a,n),script:K(h.SCRIPT,u,n),style:K(h.STYLE,l,n),title:K(h.TITLE,{title:void 0===c?"":c,titleAttributes:d},n)}},X=function(e){var t,r;return r=t=function(t){function r(){return w(this,r),O(this,t.apply(this,arguments))}return x(r,t),r.prototype.shouldComponentUpdate=function(e){return!u()(this.props,e)},r.prototype.mapNestedChildrenToProps=function(e,t){if(!t)return null;switch(e.type){case h.SCRIPT:case h.NOSCRIPT:return{innerHTML:t};case h.STYLE:return{cssText:t}}throw Error("<"+e.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")},r.prototype.flattenArrayTypeChildren=function(e){var t,r=e.child,n=e.arrayTypeChildren,i=e.newChildProps,s=e.nestedChildren;return C({},n,((t={})[r.type]=[].concat(n[r.type]||[],[C({},i,this.mapNestedChildrenToProps(r,s))]),t))},r.prototype.mapObjectTypeChildren=function(e){var t,r,n=e.child,i=e.newProps,s=e.newChildProps,o=e.nestedChildren;switch(n.type){case h.TITLE:return C({},i,((t={})[n.type]=o,t.titleAttributes=C({},s),t));case h.BODY:return C({},i,{bodyAttributes:C({},s)});case h.HTML:return C({},i,{htmlAttributes:C({},s)})}return C({},i,((r={})[n.type]=C({},s),r))},r.prototype.mapArrayTypeChildrenToProps=function(e,t){var r=C({},t);return Object.keys(e).forEach(function(t){var n;r=C({},r,((n={})[t]=e[t],n))}),r},r.prototype.warnOnInvalidChildren=function(e,t){return!0},r.prototype.mapChildrenToProps=function(e,t){var r=this,n={};return l.Children.forEach(e,function(e){if(e&&e.props){var i=e.props,s=i.children,o=z(R(i,["children"]));switch(r.warnOnInvalidChildren(e,s),e.type){case h.LINK:case h.META:case h.NOSCRIPT:case h.SCRIPT:case h.STYLE:n=r.flattenArrayTypeChildren({child:e,arrayTypeChildren:n,newChildProps:o,nestedChildren:s});break;default:t=r.mapObjectTypeChildren({child:e,newProps:t,newChildProps:o,nestedChildren:s})}}}),t=this.mapArrayTypeChildrenToProps(n,t)},r.prototype.render=function(){var t=this.props,r=t.children,n=C({},R(t,["children"]));return r&&(n=this.mapChildrenToProps(r,n)),l.createElement(e,n)},T(r,null,[{key:"canUseDOM",set:function(t){e.canUseDOM=t}}]),r}(l.Component),t.propTypes={base:i().object,bodyAttributes:i().object,children:i().oneOfType([i().arrayOf(i().node),i().node]),defaultTitle:i().string,defer:i().bool,encodeSpecialCharacters:i().bool,htmlAttributes:i().object,link:i().arrayOf(i().object),meta:i().arrayOf(i().object),noscript:i().arrayOf(i().object),onChangeClientState:i().func,script:i().arrayOf(i().object),style:i().arrayOf(i().object),title:i().string,titleAttributes:i().object,titleTemplate:i().string},t.defaultProps={defer:!0,encodeSpecialCharacters:!0},t.peek=e.peek,t.rewind=function(){var t=e.rewind();return t||(t=Y({baseTag:[],bodyAttributes:{},encodeSpecialCharacters:!0,htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}})),t},r}(o()(function(e){var t;return{baseTag:(t=[p.HREF,p.TARGET],e.filter(function(e){return void 0!==e[h.BASE]}).map(function(e){return e[h.BASE]}).reverse().reduce(function(e,r){if(!e.length)for(var n=Object.keys(r),i=0;i<n.length;i++){var s=n[i].toLowerCase();if(-1!==t.indexOf(s)&&r[s])return e.concat(r)}return e},[])),bodyAttributes:L(f.BODY,e),defer:N(e,y.DEFER),encode:N(e,y.ENCODE_SPECIAL_CHARACTERS),htmlAttributes:L(f.HTML,e),linkTags:A(h.LINK,[p.REL,p.HREF],e),metaTags:A(h.META,[p.NAME,p.CHARSET,p.HTTPEQUIV,p.PROPERTY,p.ITEM_PROP],e),noscriptTags:A(h.NOSCRIPT,[p.INNER_HTML],e),onChangeClientState:N(e,y.ON_CHANGE_CLIENT_STATE)||function(){},scriptTags:A(h.SCRIPT,[p.SRC,p.INNER_HTML],e),styleTags:A(h.STYLE,[p.CSS_TEXT],e),title:S(e),titleAttributes:L(f.TITLE,e)}},function(e){D&&k(D),e.defer?D=M(function(){Q(e,function(){D=null})}):(Q(e),D=null)},Y)(function(){return null}));X.renderStatic=X.rewind},9298:(e,t,r)=>{"use strict";var n=r(3341);function i(){}function s(){}s.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,s,o){if(o!==n){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:i};return r.PropTypes=r,r}},9441:(e,t,r)=>{"use strict";r.d(t,{useIsFetching:()=>o});var n=r(2115),i=r(4268),s=r(9776);function o(e,t){let r=(0,s.useQueryClient)(t),o=r.getQueryCache();return n.useSyncExternalStore(n.useCallback(e=>o.subscribe(i.j.batchCalls(e)),[o]),()=>r.isFetching(e),()=>r.isFetching(e))}},9511:e=>{e.exports={style:{fontFamily:"'Crimson Text', 'Crimson Text Fallback'"},className:"__className_13cef7",variable:"__variable_13cef7"}},9776:(e,t,r)=>{"use strict";r.r(t),r.d(t,{QueryClientContext:()=>s,QueryClientProvider:()=>a,useQueryClient:()=>o});var n=r(2115),i=r(5155),s=n.createContext(void 0),o=e=>{let t=n.useContext(s);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},a=e=>{let{client:t,children:r}=e;return n.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,i.jsx)(s.Provider,{value:t,children:r})}}}]);