"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8307],{8307:(e,t,n)=>{n.d(t,{$P:()=>f,Zp:()=>C,g:()=>d,jb:()=>i,x$:()=>g,zy:()=>v});var a,r=n(4232),u=n(8116);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}let s=r.createContext(null),i=r.createContext(null),c=r.createContext(null),l=r.createContext({outlet:null,matches:[],isDataRoute:!1});function f(e,t){let{relative:n}=void 0===t?{}:t;p()||(0,u.Oi)(!1);let{basename:a,navigator:o}=r.useContext(i),{hash:s,pathname:c,search:l}=g(e,{relative:n}),f=c;return"/"!==a&&(f="/"===c?a:(0,u.HS)([a,c])),o.createHref({pathname:f,search:l,hash:s})}function p(){return null!=r.useContext(c)}function v(){return p()||(0,u.Oi)(!1),r.useContext(c).location}function h(e){r.useContext(i).static||r.useLayoutEffect(e)}function C(){let{isDataRoute:e}=r.useContext(l);return e?function(){let e,t,n,a,{router:i}=(x.UseNavigateStable,(e=r.useContext(s))||(0,u.Oi)(!1),e),c=(m.UseNavigateStable,(a=((t=r.useContext(l))||(0,u.Oi)(!1),n=t).matches[n.matches.length-1]).route.id||(0,u.Oi)(!1),a.route.id),f=r.useRef(!1);return h(()=>{f.current=!0}),r.useCallback(function(e,t){void 0===t&&(t={}),f.current&&("number"==typeof e?i.navigate(e):i.navigate(e,o({fromRouteId:c},t)))},[i,c])}():function(){p()||(0,u.Oi)(!1);let e=r.useContext(s),{basename:t,future:n,navigator:a}=r.useContext(i),{matches:o}=r.useContext(l),{pathname:c}=v(),f=JSON.stringify((0,u.yD)(o,n.v7_relativeSplatPath)),C=r.useRef(!1);return h(()=>{C.current=!0}),r.useCallback(function(n,r){if(void 0===r&&(r={}),!C.current)return;if("number"==typeof n)return void a.go(n);let o=(0,u.Gh)(n,JSON.parse(f),c,"path"===r.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:(0,u.HS)([t,o.pathname])),(r.replace?a.replace:a.push)(o,r.state,r)},[t,a,f,c,e])}()}function d(){let{matches:e}=r.useContext(l),t=e[e.length-1];return t?t.params:{}}function g(e,t){let{relative:n}=void 0===t?{}:t,{future:a}=r.useContext(i),{matches:o}=r.useContext(l),{pathname:s}=v(),c=JSON.stringify((0,u.yD)(o,a.v7_relativeSplatPath));return r.useMemo(()=>(0,u.Gh)(e,JSON.parse(c),s,"path"===n),[e,c,s,n])}r.Component;var x=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(x||{}),m=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(m||{});(a||(a=n.t(r,2))).startTransition;var b=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(b||{});new Promise(()=>{}),r.Component}}]);