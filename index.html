<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Shangrila Distillery | Premium Craft Spirits from Nepal</title>
    <meta name="description" content="Shangrila Distillery produces world-class craft spirits in Nepal. Discover our premium gins, vodkas, and whiskeys, made with Himalayan purity." />
    <meta name="author" content="Lovable" />

    <meta property="og:title" content="Shangrila Distillery | Premium Spirits from Nepal" />
    <meta property="og:description" content="Discover world-class gin, vodka, and whiskey from Shangrila Distillery." />
    <meta property="og:image" content="https://shangriladistillery.com/logo.png" />
    <meta property="og:url" content="https://shangriladistillery.com/" />
    <meta property="og:type" content="website" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Shangrila Distillery | Nepal's Craft Distillery" />
    <meta name="twitter:description" content="Crafting premium spirits in the heart of the Himalayas." />
    <meta name="twitter:image" content="https://shangriladistillery.com/logo.png" />
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" type="image/png" sizes="16x16" href="/lovable-uploads/favicon-shangrila.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/lovable-uploads/favicon-shangrila.png" />
    <link rel="apple-touch-icon" href="/lovable-uploads/favicon-shangrila.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Distillery",
        "name": "Shangrila Distillery",
        "url": "https://shangriladistillery.com",
        "logo": "https://shangriladistillery.com/logo.png",
        "description": "Shangrila Distillery is a premium spirits producer in Nepal, offering gin, vodka, and whiskey.",
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "NP"
        }
      }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>